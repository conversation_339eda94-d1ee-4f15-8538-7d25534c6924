# Git Input/Select
Git 相关的 Select、Input，用于快捷选择 Git Group和 Project

## 代码演示

### GitGroupSelect 分组选择器
```tsx preview
import React from 'react';
import { Flex } from 'antd';
import { GitGroupSelect } from '@ali/mc-uikit';
import { DemoLayout } from '@ali/mc-docs';
import '@ali/mc-uikit/esm/BasicLayout/index.module.less';

export default function GitGroupSelectDemo() {
  return (
    <DemoLayout>
      <Flex vertical gap="small">
        <GitGroupSelect
          placeholder="自定义 placeholder"
          style={{
            width: 300
          }}
        />
        <GitGroupSelect
          //getPopupContainer 将下拉弹层渲染节点固定在触发器的父元素中是为了演示的css 平常看情况添加
          // getPopupContainer={(triggerNode: any) => triggerNode.parentElement}
          style={{
            width: 300
          }}
          disabled
        />
        <GitGroupSelect
          prefix="Git 分支："
          //getPopupContainer 将下拉弹层渲染节点固定在触发器的父元素中是为了演示的css 平常看情况添加
          // getPopupContainer={(triggerNode: any) => triggerNode.parentElement}
          style={{
            width: 300
          }}
        />
      </Flex>
    </DemoLayout>
  );
}
```

### GitProjectSelect 仓库选择器

```tsx preview
import React from 'react';
import { Flex } from 'antd';
import { GitProjectSelect } from '@ali/mc-uikit';
import { DemoLayout } from '@ali/mc-docs';

export default () => {
  return (
    <DemoLayout>
      <Flex vertical gap="small" style={{ maxWidth: 600 }}>
        <GitProjectSelect />
        <GitProjectSelect prefix="Git 仓库：" />
        <GitProjectSelect prefix="Git 仓库：" disabled />
      </Flex>
    </DemoLayout>
  );
}
```

### GitBranchSelect 仓库分支选择器

```tsx preview
import React, { useState } from 'react';
import { Space } from 'antd';
import { GitProjectSelect, GitBranchSelect } from '@ali/mc-uikit';
import { DemoLayout } from '@ali/mc-docs';

export default () => {
  const [scmAddress, setScmAddress] = useState<string>();

  const onChange = (repo: string) => {
    setScmAddress(`**************************:${repo}.git`);
  };

  return (
    <DemoLayout>
      <Space.Compact>
        <GitProjectSelect
          prefix="先选择Git 仓库："
          placeholder="请选择 Git 仓库"
          onChange={onChange}
          style={{
            minWidth: 350
          }}
        />
        <GitBranchSelect
          scmAddress={scmAddress}
          disabledPrefix="def_release"
          disabledOptions={['master']}
          style={{
            minWidth: 350
          }}
        />
      </Space.Compact>
    </DemoLayout>
  );
}
```

### GitProjectInput 仓库输入框
根据选择的 Git Group，选择对应的 Project，或者输入新的 Git Project 名称，
是前面三个组件的组合用法。常用用于新建项目时，用于选择已有仓库或者创建新的仓库。

```tsx preview
import React, { useState } from 'react';
import { Flex, Divider, Radio } from 'antd';
import { GitProjectInput } from '@ali/mc-uikit';
import { DemoLayout } from '@ali/mc-docs';
export default () => {

  const [disabled, setDisabled] = useState(false);
  const [useExist, setUseExist] = useState(false);
  const [size, setSize] = useState('middle');

  return (
    <DemoLayout>
      <Flex vertical gap="small">
        <Flex gap="small" wrap>
          <Radio.Group
            optionType="button"
            options={[ { label: '禁用', value: true }, { label: '启用', value: false } ]}
            onChange={(e) => {
              setDisabled(e.target.value);
            }}
            value={disabled}
          />
          <Radio.Group
            optionType="button"
            options={[ { label: '选择已有仓库', value: true }, { label: '手工输入仓库', value: false } ]}
            onChange={(e) => {
              setUseExist(e.target.value);
            }}
            value={useExist}
          />
          <Radio.Group
            optionType="button"
            options={[ { label: 'Large', value: 'large'}, { label: 'Middle', value: 'middle'}, { label: 'Small', value: 'small'} ]}
            onChange={(e) => {
              setSize(e.target.value);
            }}
            value={size}
          />
        </Flex>
        <Divider />
        <GitProjectInput
          placeholder="请输入 Git 仓库地址"
          style={{
            minWidth: 500
          }}
          size={size}
          fullAddress
          useExist={useExist}
          disabled={disabled}
          onChange={(...args) => {console.log(...args)}}
        />
        <Divider />
        <GitProjectInput
          placeholder="请输入 Git 仓库地址"
          style={{
            minWidth: 500
          }}
          defaultValue="**************************:mc-uikit/mc-uikit.git"
          size={size}
          fullAddress
          useExist={useExist}
          disabled={disabled}
          onChange={(...args) => {console.log(...args)}}
        />
      </Flex>
    </DemoLayout>
  );
}
```
