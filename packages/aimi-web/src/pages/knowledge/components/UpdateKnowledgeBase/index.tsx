import { Button, Form, message, Modal, Spin, theme, Tooltip } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ChunkConfigOutlined, GearOutlined, ServerOutlined } from '@ali/mc-icons';
import KnowledgeBaseForm from '../KnowledgeBaseForm';
import { isEmpty, omit } from 'lodash-es';
import { DataChunkConfig, DocumentChunkConfigUpdateRequest, DocumentDownloadTaskCreateRequest, DocumentVO, KnowledgeBaseAddRequest, KnowledgeBaseUpdateRequest, KnowledgeBaseUploadRequest, KnowledgeBaseVO } from '@ali/mc-services/AiMiDataContracts';
import { InlineButton } from '@ali/mc-uikit';
import { useRequest } from '@ali/mc-request';
import { updateKnowledgeBase, uploadDocuments } from '@ali/mc-services/AiMiKnowledgeBase';
import { createDownloadTask, updateDocumentChunkConfig } from '@ali/mc-services/AiMiDocument';
import styles from './index.module.less';

interface IProps {
  knowledgeBaseDetail?: KnowledgeBaseVO;
  type: 'upload' | 'setting' | 'chunkConfig' | 'tableStructure'; // 文档上传/知识库设置/文档处理
  documentDetail?: DocumentVO;
  onRefresh: () => void;
}
const UpdateKnowledgeBase = (props: IProps) => {
  const { knowledgeBaseDetail, type, onRefresh, documentDetail } = props;
  const documentId = documentDetail?.id;
  const [open, setOpen] = useState<boolean>(false);
  const [form] = Form.useForm();
  const { token } = theme.useToken();
  const tableContentRef = useRef<any>();

  const handleOpen = useCallback(() => {
    setOpen(true);
  }, []);

  const handleClose = useCallback(() => {
    setOpen(false);
    form.resetFields();
  }, [form]);

  const {
    runAsync: updateSetting,
    loading: updateKnowledgeBaseLoading,
  } = useRequest<number, [KnowledgeBaseUpdateRequest]>(updateKnowledgeBase);

  const {
    runAsync: uploadDocs,
    loading: uploadDocsLoading,
  } = useRequest<boolean, [KnowledgeBaseUploadRequest]>(uploadDocuments);

  const {
    runAsync: updateChunkConfig,
    loading: updateChunkConfigLoading,
  } = useRequest<number, [DocumentChunkConfigUpdateRequest]>(updateDocumentChunkConfig);

  const {
    runAsync: getDownloadTaskId,
    data: downloadTaskId,
    loading: downloadTaskIdLoading,
  } = useRequest<number, [DocumentDownloadTaskCreateRequest]>(createDownloadTask);

  const handleOk = () => {
    form.validateFields().then(async (values:
      KnowledgeBaseVO & DocumentVO & {
        preprocessConfig: boolean; chunkConfig: DataChunkConfig;
      }) => {
      console.log(values, 'values===>');
      let res;
      let msg;
      if (type === 'upload') {
        msg = '上传';
        const {
          preprocessConfig,
          dingTalkDocuments,
          yuqueDocuments,
          docType,
        } = values as any;

        let documents: KnowledgeBaseAddRequest['documents'] = [];
        if (dingTalkDocuments?.length) {
          dingTalkDocuments.forEach((doc: { sourceUrl: string }) => {
            if (doc?.sourceUrl) {
              documents.push({
                ...doc,
                sourceType: 'DINGDOC',
                documentType: docType,
              });
            }
          });
        }
        if (yuqueDocuments?.length) {
          yuqueDocuments.forEach((doc: { sourceUrl: string; token?: string }) => {
            if (doc?.sourceUrl && doc?.token) {
              documents.push({
                ...doc,
                sourceType: 'YUQUE',
                documentType: docType,
              });
            }
          });
        }

        let uploadParams: KnowledgeBaseUploadRequest = {
          ...omit(values, 'preprocessConfig', 'processingType', 'dingTalkDocuments', 'yuqueDocuments', 'docType', 'docSource'),
          documents,
        };
         if (docType === 'SHEET') {
          const tablePreprocessConfig = tableContentRef.current.getData();
          uploadParams.preprocessConfig = {
            ...tablePreprocessConfig,
          };
        } else {
          if (preprocessConfig) {
            uploadParams.preprocessConfig = {
              deleteEmail: true,
              deleteImage: true,
              deleteUrl: true,
            };
          }
        }
        res = await uploadDocs({
          knowledgeBaseId: knowledgeBaseDetail?.id,
          ...uploadParams,
        });
      }
      if (type === 'chunkConfig' && documentId) {
        msg = '文档处理配置更新';
        const {
          chunkConfig,
          preprocessConfig,
        } = values;
        let updateChunkCfg: DocumentChunkConfigUpdateRequest = {
          chunkConfig,
          documentId,
        };
        if (preprocessConfig) {
          updateChunkCfg.preprocessConfig = {
            deleteEmail: true,
            deleteImage: true,
            deleteUrl: true,
          };
        } else {
          updateChunkCfg.preprocessConfig = {
            deleteEmail: false,
            deleteImage: false,
            deleteUrl: false,
          };
        }
        res = await updateChunkConfig(updateChunkCfg);
      }
      if (type === 'setting') {
        msg = '知识库设置更新';
        const {
          name,
          description,
          embeddingVectorModel,
          updateFrequency,
          bizGroupId,
        } = values;
        res = await updateSetting({
          knowledgeBaseId: knowledgeBaseDetail?.id,
          name,
          description,
          embeddingVectorModel,
          updateFrequency,
          bizGroupId,
        });
      }
      if (type === 'tableStructure' && documentId) {
        msg = '更新表结构';
        let updatePreprocessCfg: DocumentChunkConfigUpdateRequest = {
          documentId,
        };
        const tablePreprocessConfig = tableContentRef.current.getData();
        updatePreprocessCfg.preprocessConfig = {
          ...tablePreprocessConfig,
        };
        res = await updateChunkConfig(updatePreprocessCfg);
      }

      if (res) {
        message.success(`${msg}成功`);
        handleClose();
        onRefresh();
      } else {
        message.error(`${msg}失败`);
      }
    }).catch((err) => {
      const { errorFields } = err || {};
      if (errorFields?.length) {
        const filedName = errorFields[errorFields?.length - 1]?.name;
        if (filedName) {
          form.scrollToField(filedName, {
            block: 'center',
            behavior: 'smooth',
          });
        }
      }
    });
  };


  useEffect(() => {
    if (type === 'setting' && !isEmpty(knowledgeBaseDetail)) {
      const {
        name,
        description,
        updateFrequency,
        embeddingVectorModel,
        bizGroupId,
      } = knowledgeBaseDetail;
      form.setFieldsValue({
        name,
        description,
        updateFrequency,
        embeddingVectorModel,
        bizGroupId,
      });
    }
  }, [knowledgeBaseDetail, form, type]);

  const [title, entryHandler, okText] = useMemo(() => {
    if (type === 'setting') {
      return ['知识库设置', <InlineButton
        type="text"
        key="setting"
        className={styles.linkStyle}
      >
        <GearOutlined />
      </InlineButton>,
      '更新'];
    }
    if (type === 'chunkConfig') {
      return [
        '文档处理',
        <Tooltip
          title="文档处理配置"
          key="chunkConfig"
        >
          <InlineButton type="text" >
            <ChunkConfigOutlined
              style={{ fontSize: token.fontSizeLG }}
            />
          </InlineButton>
        </Tooltip>,
        '确定'];
    }
    if (type === 'upload') {
      return [
        '文档上传',
        <Button type="primary" key="upload">
          上传
        </Button>,
        '上传'];
    }
    if (type === 'tableStructure') {
      return [
        '更新表结构',
        <Tooltip
          title="更新表结构"
          key="tableStructure"
        >
          <InlineButton
            type="text"
            onClick={() => {
              getDownloadTaskId({
                documentId: documentDetail?.id,
              });
            }}
          >
            <ServerOutlined
              style={{ fontSize: token.fontSizeLG }}
            />
          </InlineButton>
        </Tooltip>,
        '确定',
      ];
    }
    return [];
  }, [type]);

  return (<>
    {
      React.isValidElement(entryHandler) && React.cloneElement(entryHandler, {
        onClick: handleOpen as React.MouseEventHandler,
      } as React.HTMLAttributes<HTMLElement>)
    }
    <Modal
      open={open}
      onCancel={handleClose}
      title={title}
      centered
      destroyOnClose
      okText={okText}
      onOk={handleOk}
      confirmLoading={updateKnowledgeBaseLoading || uploadDocsLoading || updateChunkConfigLoading}
      width="80vw"
      styles={{
        body: {
          height: 'calc(100vh - 250px)',
          overflowY: 'auto',
          paddingBlock: token.padding,
          minHeight: 498,
        },
        footer: {
          marginTop: 0,
        },
        header: {
          marginBottom: 0,
        },
      }}
    >
      {
        open && <Spin spinning={downloadTaskIdLoading}>
          <KnowledgeBaseForm
            form={form}
            tableContentRef={tableContentRef}
            type={type}
            downloadTaskId={downloadTaskId}
          />
        </Spin>
      }
    </Modal>
  </>);
};

export default UpdateKnowledgeBase;
