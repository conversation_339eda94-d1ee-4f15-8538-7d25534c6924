import { useRequest } from '@ali/mc-request';
import { TableDocumentPreviewRequest, TableDocumentSheetVO, TableDocumentVO } from '@ali/mc-services/AiMiDataContracts';
import { previewTableContent } from '@ali/mc-services/AiMiDocument';
import { Flex, Select, Spin, Switch, Table, theme } from 'antd';
import QueueAnim from 'rc-queue-anim';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from 'react';

const numberToColumn = (num: number) => {
  let result = '';
  let num_ = num;
  while (num_ > 0) {
    num_--;
    result = String.fromCharCode(65 + (num_ % 26)) + result;
    num_ = Math.floor(num_ / 26);
  }
  return result;
};

const columnToNumber = (column: string) => {
  let result = 0;
  // eslint-disable-next-line @typescript-eslint/prefer-for-of
  for (let i = 0; i < column.length; i++) {
    result *= 26;
    result += column[i].charCodeAt(0) - 65;
  }
  return result;
};

export interface OptionType {
  label?: React.ReactNode;
  value?: string | number | null;
}

interface TableContentProps {
  tableDocument?: TableDocumentVO;
  taskId: number;
  // form: FormInstance;
}
interface TableContentRef {
  getData: () => void;
}
const TableContent = (props: TableContentProps, ref: React.Ref<TableContentRef>) => {
  const { taskId, tableDocument } = props || {};
  const {
    sheets,
  } = tableDocument || {};

  const { token } = theme.useToken();
  const [selectedSheet, setSelectedSheet] = useState<string>(sheets?.[0]?.sheetName);
  const [tableHeaderIndex, setTableHeaderIndex] = useState<number>(0);
  const [dataStartIndex, setDataStartIndex] = useState<number>(1);

  const [constructData, setConstructData] = useState<any>();
  const [preview, setPreview] = useState<boolean>(false);
  const [containerHeight, setContainerHeight] = useState<number>();

  const {
    runAsync: getPreviewContent,
    loading: previewContentLoading,
    data: previewContent,
  } = useRequest<TableDocumentSheetVO, [TableDocumentPreviewRequest]>(previewTableContent);

  const currSheet = useMemo(() => {
    return (sheets ?? []).find(sheet => sheet.sheetName === selectedSheet);
  }, [selectedSheet, sheets]);

  const needHandleCols = useMemo(() => {
    return (constructData ?? []).filter(item => item.needHandle);
  }, [constructData]);

  const sheetOptions = useMemo(() => {
    return (sheets ?? []).map(sheet => ({
      label: sheet.sheetName,
      value: sheet.sheetName,
    }));
  }, [sheets]);

  const {
    tableHeaderOptions,
    tableRowOptions,
  } = useMemo(() => {
    let tableHeaderOptions_: OptionType[] = [];
    let tableRowOptions_: OptionType[] = [];
    if (currSheet) {
      const { colCount, rowCount } = currSheet || {};
      tableHeaderOptions_ = Array(colCount).fill().map((_, i) => ({
        label: `第${i + 1}行`,
        value: i,
      }));
      tableRowOptions_ = Array(rowCount).fill().map((_, i) => ({
        label: `第${i + 1}行`,
        value: i,
      }));
    }
    return {
      tableHeaderOptions: tableHeaderOptions_,
      tableRowOptions: tableRowOptions_,
    };
  }, [currSheet]);

  useEffect(() => {
    if (currSheet) {
      const { dataSet } = currSheet;
      const colNames = dataSet?.[tableHeaderIndex];
      const result = (colNames ?? []).map((col, index) => ({
        name: col,
        colIndex: numberToColumn(index + 1),
        needHandle: true,
      }));
      setConstructData(result);
      setContainerHeight((result.length + 1) * 40);
    }
  }, [currSheet, tableHeaderIndex, dataStartIndex]);

  const handlePreview = useCallback(() => {
    if (taskId && selectedSheet) {
      getPreviewContent({
        taskId,
        preprocessConfig: {
          sheetName: selectedSheet,
          headerRowIndex: tableHeaderIndex,
          dataFirstRowIndex: dataStartIndex,
          selectedColumnIndexes: needHandleCols.map((item: any) => columnToNumber(item.colIndex)),
        },
      });
    }
  }, [taskId, selectedSheet, tableHeaderIndex, dataStartIndex, needHandleCols, getPreviewContent]);

  const constructTableColumns = [{
    title: '序号',
    dataIndex: 'colIndex',
  }, {
    title: '列名',
    dataIndex: 'name',
  }, {
    title: '操作',
    dataIndex: 'action',
    render: (_, record: any) => {
      return (<Switch
        checked={record?.needHandle}
        onChange={(checked) => {
          setConstructData((prev: any[]) => {
            return prev.map(item => {
              if (item.colIndex === record.colIndex) {
                return {
                  ...item,
                  needHandle: checked,
                };
              }
              return item;
            });
          });
        }}
      />);
    },
  }];

  const { previewTableColumns, previewTableData } = useMemo(() => {
    let columns: any = [];
    let data: any = [];
    if (previewContent) {
      const { headers, dataSet } = previewContent;
      columns = headers?.map((item, index) => ({
        title: item,
        dataIndex: `col_${index}`,
        ellipsis: true,
      }));
      data = dataSet?.map(item => {
        const result: any = {};
        item.forEach((value, index) => {
          result[`col_${index}`] = value;
        });
        return result;
      });
    }
    return {
      previewTableColumns: columns,
      previewTableData: data,
    };
  }, [previewContent]);


  useImperativeHandle(ref, () => ({
    getData: () => {
      return ({
        sheetName: selectedSheet,
        headerRowIndex: tableHeaderIndex,
        dataFirstRowIndex: dataStartIndex,
        selectedColumnIndexes: needHandleCols.map((item: any) => columnToNumber(item.colIndex)),
        downloadTaskId: taskId,
      });
    },
  }));

  return (<Flex
    vertical
    style={{
      width: '100%',
      marginBottom: token.margin,
    }}
  >
    <div
      className="aimi-form-item-label"
    >
      配置表结构&数据清洗
    </div>
    <Flex
      vertical
      gap={token.margin}
      style={{
        borderRadius: token.borderRadius,
        background: token.colorBgContainer,
        padding: token.padding,
      }}
    >
      <div>
        <Flex
          gap={token.margin}
          style={{
            marginBottom: token.marginXS,
            fontWeight: 500,
          }}
        >
          <div
            style={{
              width: '40%',
            }}
          >
            数据表
          </div>
          <div
            style={{
              width: '30%',
            }}
          >
            表头

          </div>
          <div
            style={{
              width: '30%',
            }}
          >
            数据起始行
          </div>
        </Flex>
        <Flex
          gap={token.margin}
        >
          <Select
            options={sheetOptions}
            placeholder="请选择数据表"
            allowClear
            showSearch
            style={{
              width: '40%',
            }}
            value={selectedSheet}
            onChange={(value) => {
              setSelectedSheet(value);
              setPreview(false);
            }}
            filterOption={(input, option) => {
              return (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
            }}
          />
          <Select
            options={tableHeaderOptions}
            placeholder="请选择表头"
            allowClear
            showSearch
            style={{
              width: '30%',
            }}
            value={tableHeaderIndex}
            onChange={(value) => {
              setTableHeaderIndex(value);
              setPreview(false);
            }}
            filterOption={(input, option) => {
              return (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
            }}
          />
          <Select
            options={tableRowOptions}
            placeholder="请选择数据起始行"
            allowClear
            showSearch
            style={{
              width: '30%',
            }}
            value={dataStartIndex}
            onChange={(value) => {
              setDataStartIndex(value);
              setPreview(false);
            }}
            filterOption={(input, option) => {
              return (option?.label ?? '').toLowerCase().includes(input.toLowerCase());
            }}
          />
        </Flex>
      </div>
      <Flex
        justify="space-between"
        align="center"
        style={{
          border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,
          borderRadius: 8,
          paddingBlock: token.paddingXS,
          paddingInline: token.paddingSM,
          backgroundColor: token.colorBorder,
        }}
      >
        <div>
          已选择
          <span
            style={{
              marginInline: token.paddingXXS,
            }}
          >
            {needHandleCols?.length ?? 0}
          </span>
          项
        </div>
        {
          preview ? <a
            onClick={() => {
              setPreview(false);
            }}
          >
            取消预览
          </a> : <a
            onClick={() => {
              setPreview(true);
              handlePreview();
            }}
          >
            预览
          </a>
        }
      </Flex>
      <Flex
        className="target"
        style={{
          height: containerHeight,
        }}
      >
        {
          preview ? null : <QueueAnim
            type={['right', 'left']}
            ease={['easeOutQuart', 'easeInOutQuart']}
            style={{
              width: '100%',
            }}
          >
            <div
              key="constructTable"
            >
              <Table
                dataSource={constructData}
                columns={constructTableColumns}
                pagination={false}
                size="small"
              />
            </div>
          </QueueAnim>
        }
        {
          preview ? <QueueAnim
            type={['right', 'left']}
            ease={['easeOutQuart', 'easeInOutQuart']}
            style={{
              width: '100%',
            }}
          >
            <div
              key="previewTable"
              style={{
                width: '100%',
                height: '100%',
                overflow: 'auto',
              }}
            >
              {
                previewContentLoading ? <Spin
                  style={{
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                /> : <Table
                  scroll={{
                    x: '100%',
                    y: 360,
                  }}
                  columns={previewTableColumns}
                  dataSource={previewTableData}
                  pagination={false}
                  size="small"
                />
              }
            </div>
          </QueueAnim> : null
        }
      </Flex>
    </Flex>
  </Flex>);
};

export default forwardRef(TableContent);
