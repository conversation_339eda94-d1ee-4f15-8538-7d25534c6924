import { ArrowRightOutlined, FileOutlined, SearchOutlined } from '@ali/mc-icons';
import { Avatar, Checkbox, Divider, Flex, Input, List, Skeleton, theme, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import HoverCard from '@/components/HoverCard';
import CreateKnowledge from './components/CreateKnowledge';
import { defineDataLoader, Link, useSearchParams } from 'ice';
import { useRequest } from '@ali/mc-request';
import { pageKnowledgeBase, PageKnowledgeBaseParams } from '@ali/mc-services/AiMiKnowledgeBase';
import { KnowledgeBasePageRequest, KnowledgeBaseVO, PageKnowledgeBaseVO } from '@ali/mc-services/AiMiDataContracts';
import { StatusTag } from '@ali/mc-uikit';
import AimiUser from '@/components/AimiUser';
import { getTimeStr } from '@ali/mc-services';
import { debounce } from 'lodash-es';
import { useDeferData } from '@/hooks/useDeferData';

export default () => {
  const { token } = theme.useToken();
  const [urlParams] = useSearchParams();
  const createModalOpen = urlParams.get('createKnowledgeBase') === 'true';

  const { data, loading: initLoading = true } = useDeferData<{
    knowledgeBaseCount: number;
  }>();

  const [searchParams, setSearchParams] = useState<KnowledgeBasePageRequest>({
    pageNo: 1,
    pageSize: 16,
  });

  const onFilterChange = (filterName: 'name' | 'onlyShowMine', filterValue: string | boolean) => {
    // 过滤的时候，需要重置当前页和历史数据；
    setSearchParams(prev => ({
      ...prev,
      [filterName]: filterValue,
      pageNo: 1,
    }));
  };

  const {
    runAsync: getKnowledgeBaseRes,
    data: knowledgeBaseRes,
    loading,
  } = useRequest<PageKnowledgeBaseVO, [PageKnowledgeBaseParams]>(pageKnowledgeBase);

  useEffect(() => {
    getKnowledgeBaseRes({
      ...searchParams,
      pageNo: (searchParams?.pageNo ?? 1) - 1,
    } as any);
  }, [getKnowledgeBaseRes, searchParams]);

  return (<Flex
    flex={1}
    vertical
    justify="center"
    gap={token.marginLG}
    style={{
      marginTop: token.marginXL,
    }}
  >
    <Skeleton
      loading={initLoading}
      active
    >
      <Flex
        vertical
        justify="center"
        align="center"
        gap={token.margin}
      >
        <div
          style={{
            color: token.colorText,
            fontSize: token.fontSizeHeading4 * 2,
            fontWeight: token.fontWeightStrong,
            lineHeight: token.lineHeightHeading5,
          }}
        >
          已收纳 {data?.knowledgeBaseCount ?? '-'} 个知识库
        </div>
        <div
          style={{
            color: token.colorTextSecondary,
            fontSize: token.fontSizeLG,
          }}
        >
          知识库支持一键同步语雀/钉钉/Web站点等多源文档。通过向量+关键词双引擎检索，提升AIMI-Agent回答精确度。
          <Link
            to="https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iv0x76DNJDOnGvpb?corpId=dingd8e1123006514592&utm_medium=im_card&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&utm_scene=team_space&utm_source=im"
            target="mcp_tool_doc"
          >
            操作手册<ArrowRightOutlined />
          </Link>
        </div>
      </Flex>
      <Flex
        flex={1}
        justify="space-between"
        align="center"
        style={{
          marginTop: token.margin,
        }}
      >
        <Flex
          align="center"
          gap={token.margin}
          style={{
            width: 'fit-content',
          }}
        >
          <Input
            placeholder="输入知识库名称"
            allowClear
            prefix={<SearchOutlined
              style={{
                color: token.colorTextSecondary,
              }}
            />}
            onChange={debounce((e: any) => {
              onFilterChange('name', e?.target?.value);
            }, 300)}
          />
          <Checkbox
            onChange={(e) => {
              onFilterChange('onlyShowMine', e.target.checked);
            }}
            checked={Boolean(searchParams.onlyShowMine)}
          >
            <div
              className="textNoWrap"
              style={{
                minWidth: 124,
              }}
            >
              我创建的知识库
            </div>
          </Checkbox>
        </Flex>
        <Flex>
          <CreateKnowledge
            open={createModalOpen}
            onRefresh={() => {
              //  新建完成后，重置所有过滤项、当前页、历史数据；
              setSearchParams(prev => ({
                ...prev,
                name: undefined,
                creator: undefined,
                pageNo: 1,
              }));
            }}
          />
        </Flex>
      </Flex>
      <Flex
        flex={1}
        vertical
      >
        <List
          grid={{
            gutter: 16,
            xs: 1,
            sm: 2,
            md: 4,
            lg: 4,
            xl: 4,
            xxl: 4,
          }}
          loading={loading}
          dataSource={knowledgeBaseRes?.items ?? []}
          renderItem={(item: KnowledgeBaseVO) => (
            <List.Item>
              <HoverCard
                style={{
                  cursor: 'default',
                }}
                icon={<Avatar icon={<FileOutlined />} size={42} shape="square" />}
                title={<Flex
                  vertical
                  gap={token.marginXXS}
                  style={{
                    width: '100%',
                  }}
                >
                  <Flex
                    align="center"
                    gap={token.marginXXS}
                    style={{
                      width: '100%',
                    }}
                  >
                    <Typography.Text
                      ellipsis={{
                        tooltip: item?.name,
                      }}
                    >
                      {item?.name}
                    </Typography.Text>
                  </Flex>
                  <Flex
                    align="center"
                    style={{
                      fontSize: token.fontSizeSM,
                      fontWeight: 'normal',
                    }}
                    gap={token.marginXS}
                  >
                    <StatusTag
                      style={{
                        height: token.controlHeightSM - token.sizeUnit,
                        borderRadius: 10,
                        marginInlineEnd: 0,
                      }}
                    >
                      {item?.bizGroupName || '--'}
                    </StatusTag>
                    <Divider type="vertical" style={{ marginInline: 0 }} />
                    <div>
                      {`${item?.documentCount || 0} 文档`}
                    </div>
                  </Flex>
                </Flex>}
                height={184}
                footer={<Flex
                  justify="space-between"
                  style={{
                    fontSize: token.fontSizeSM,
                    color: token.colorTextSecondary,
                  }}
                >
                  <div>
                    <AimiUser empIds={item?.creator} showAvatar size={20} />
                  </div>
                  <div>
                    {item?.gmtModified ? getTimeStr(item?.gmtModified) : '--'}
                  </div>
                </Flex>}
                actions={[<Link
                  key="use"
                  to={`/knowledge/${item?.id}/detail`}
                  target={`knowledge_base_${item?.id}`}
                >查看</Link>,
                ]}
              >
                <Flex>
                  <Typography.Paragraph
                    style={{
                      color: token.colorText,
                      fontSize: token.fontSizeSM,
                      lineHeight: token.lineHeightSM,
                      marginBottom: 0,
                    }}
                    ellipsis={{
                      tooltip: item?.description,
                      rows: 2,
                    }}

                  >
                    {item?.description ?? '暂无描述'}
                  </Typography.Paragraph>
                </Flex>
              </HoverCard>
            </List.Item>
          )}
          pagination={{
            align: 'center',
            total: knowledgeBaseRes?.totalCount || 0,
            showTotal: (total) => `共 ${total} 条`,
            current: searchParams.pageNo,
            pageSize: searchParams.pageSize,
            onChange: (page, size) => {
              setSearchParams(prev => ({
                ...prev,
                pageNo: page,
                pageSize: size,
              }));
            },
          }}
        />
      </Flex>
    </Skeleton>
  </Flex>);
};

export const dataLoader = defineDataLoader(
  async () => {
    const res = await pageKnowledgeBase({
      pageNo: 0,
      pageSize: 10,
    } as any);
    return {
      knowledgeBaseCount: res?.totalCount || 0,
     };
  },
  { defer: true },
);
