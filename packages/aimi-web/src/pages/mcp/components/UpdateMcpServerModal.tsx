import { useRequest } from '@ali/mc-request';
import { McpServerMeta } from '@ali/mc-services/AiMiMcpDataContracts';
import { getMcpServerMeta, GetMcpServerMetaParams, updateMcpServerMeta } from '@ali/mc-services/AiMiMcpServerMeta';
import { Form, message, Modal, Skeleton, theme } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import McpServerForm from './CreateMcpServer/McpServerForm';
import CreateMcpServerProvider, { useCreateMcpServerContext } from './CreateMcpServer/CreateMcpServerProvider';
import { omit } from 'lodash-es';
import { removeFalsy } from '@/utils/utils';
import { InlineButton } from '@ali/mc-uikit';
import { GearOutlined } from '@ali/mc-icons';

const ModalContent = ({ id, open, onClose, onRefresh }: {
  id?: number;
  open?: boolean;
  onClose: () => void;
  onRefresh: () => void;
}) => {
  const { token } = theme.useToken();
  const [form] = Form.useForm();
  const {
    setSelectedTools,
    setSelectedToolIds,
    selectedToolIds,
    selectedTools,
  } = useCreateMcpServerContext();
  const {
    runAsync: getMcpServerDetail,
    data: mcpServerDetail,
    loading: mcpServerDetailLoading,
  } = useRequest<McpServerMeta, [GetMcpServerMetaParams]>(getMcpServerMeta, {
    onSuccess: (detail) => {
      if (detail) {
        const { name, bizGroupId, fullPath, description, iconUrl, accessLevel, toolMetaIds, tools } = detail;
        form.setFieldsValue({
          name,
          bizGroupId,
          fullPath,
          description,
          iconUrl,
          accessLevel: accessLevel === 'PUBLIC',
        });
        setSelectedToolIds?.(toolMetaIds);
        setSelectedTools?.(tools);
      }
    },
  });

  const {
    runAsync: updateServer,
    loading: updateServerLoading,
  } = useRequest<number, [McpServerMeta]>(updateMcpServerMeta);

  useEffect(() => {
    if (id && open) {
      getMcpServerDetail({
        id,
      });
    }
  }, [open, id, getMcpServerDetail]);

  const handleOk = () => {
    form.validateFields().then((values) => {
      const toolMetaIds = selectedToolIds ?? [];
      const tools = selectedTools ?? [];
      updateServer(removeFalsy({
        ...mcpServerDetail,
        ...omit(values, 'path', 'fullPath'),
        tools,
        toolMetaIds,
        accessLevel: values?.accessLevel ? 'PUBLIC' : 'PRIVATE',
      })).then(res => {
        if (res) {
          message.success('MCP Server更新成功');
          onClose?.();
          onRefresh?.();
        } else {
          message.error('MCP Server更新失败');
        }
      });
    }).catch((err) => {
      const { errorFields } = err || {};
      if (errorFields?.length) {
        const filedName = errorFields[errorFields?.length - 1]?.name;
        if (filedName) {
          form.scrollToField(filedName, {
            block: 'center',
            behavior: 'smooth',
          });
        }
      }
    });
  };

  return (<Modal
    open={open}
    onCancel={onClose}
    onClose={onClose}
    title="更新MCP Server"
    afterClose={() => {
      setSelectedToolIds?.([]);
      setSelectedTools?.([]);
      form.resetFields?.();
    }}
    style={{
      minWidth: '80vw',
    }}
    confirmLoading={updateServerLoading}
    styles={{
      body: {
        height: 'calc(100vh - 250px)',
        overflowY: 'auto',
        paddingBlock: token.padding,
        minHeight: 498,
      },
      footer: {
        marginTop: 0,
      },
      header: {
        marginBottom: 0,
      },
    }}
    onOk={handleOk}
  >
    <Skeleton active loading={mcpServerDetailLoading}>
      {mcpServerDetail && <McpServerForm
        form={form}
        isUpdate
        key={id}
      />}
    </Skeleton>
  </Modal>
  );
};

const UpdateMcpServerModal = ({ id, onRefresh }: { id?: number; onRefresh: () => void }) => {
  const [open, setOpen] = useState<boolean>(false);
  const handleOpen = useCallback(() => {
    setOpen(true);
  }, []);

  const handleClose = useCallback(() => {
    setOpen(false);
  }, []);

  return (<CreateMcpServerProvider>
    <InlineButton
      type="text"
      onClick={handleOpen}
    >
      <GearOutlined />
    </InlineButton>
    <ModalContent
      id={id}
      key={id}
      open={open}
      onClose={handleClose}
      onRefresh={onRefresh}
    />

  </CreateMcpServerProvider>);
};

export default UpdateMcpServerModal;
