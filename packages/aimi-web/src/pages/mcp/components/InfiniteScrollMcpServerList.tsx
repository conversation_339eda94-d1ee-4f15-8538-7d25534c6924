import HoverCard from '@/components/HoverCard';
import { Avatar, Button, Divider, Flex, Input, List, Skeleton, theme, Typography } from 'antd';
import { Link, useAppData } from 'ice';
import React, { useRef, useState } from 'react';
import AimiUser from '@/components/AimiUser';
import { getTimeStr } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import { getMcpServerMetas, GetMcpServerMetasParams } from '@ali/mc-services/AiMiMcpServerMeta';
import { McpServerMeta, PageMcpServerMetaVO } from '@ali/mc-services/AiMiMcpDataContracts';
import { removeFalsy } from '@/utils/utils';
import UpdateMcpServerModal from './UpdateMcpServerModal';
import { defaultServerIcon } from './CreateMcpServer/McpServerForm';
import { StatusTag } from '@ali/mc-uikit';
import InfiniteScroll from 'react-infinite-scroll-component';
import { SearchOutlined } from '@ali/mc-icons';
import { debounce } from 'lodash-es';

const InfiniteScrollMcpServerList = ({ initData }: { initData?: PageMcpServerMetaVO }) => {
  const { token } = theme.useToken();
  const { user } = useAppData() || {};

  const pageRef = useRef<number>(0);
  const dataRef = useRef<McpServerMeta[]>(initData?.items ?? []);

  const [searchParams, setSearchParams] = useState<Omit<GetMcpServerMetasParams, 'pageNo' | 'pageSize'>>({});
  const [totalCount, setTotalCount] = useState<number>(initData?.totalCount || 0);
  // const [loading, setLoading] = useState(false);
  // const [data, setData] = useState(initData?.items ?? [])


  const {
    runAsync: getMcpServerList,
    refreshAsync: refreshMcpServerList,
    loading,
  } = useRequest<PageMcpServerMetaVO, [GetMcpServerMetasParams | undefined]>(getMcpServerMetas);

  const doGetMcpServerList = (params: GetMcpServerMetasParams) => {
    // setLoading(true);
    getMcpServerList(removeFalsy({
      ...params,
      pageSize: 5,
    })).then(res => {
      // setLoading(false);
      console.log(dataRef.current, 'dataRef.current===>');
      dataRef.current = [...(dataRef.current ?? []), ...(res?.items ?? [])];
      // setData(prev =>[...(prev ?? []), ...(res?.items ?? [])]);
      if (totalCount !== res?.totalCount) {
        setTotalCount(res?.totalCount || 0);
      }
    }).catch(() => {
      // setLoading(false);
    });
  };

  const onFilterChange = (filterName: 'creator' | 'name', filterValue: string) => {
    // 过滤的时候，需要重置当前页和历史数据；
    pageRef.current = 0;
    dataRef.current = [];
    setSearchParams(prev => ({
      ...prev,
      [filterName]: filterValue,
    }));

    doGetMcpServerList({
      ...searchParams,
      [filterName]: filterValue,
      pageNo: pageRef.current,
    });
  };

  const loadMoreData = () => {
    if (loading) {
      return;
    }
    pageRef.current = pageRef.current + 1;
    doGetMcpServerList({
      ...searchParams,
      pageNo: pageRef.current,
    });
  };

  return (<Flex
    flex={1}
    style={{
      height: '400px',
      overflowY: 'auto',
    }}
    id="scrollableDiv"
    vertical
  >
    <Flex
      flex={1}
      justify="space-between"
      align="center"
      style={{
        marginBottom: token.margin,
      }}
    >
      <Flex
        align="center"
        gap={token.margin}
        style={{
          width: 'fit-content',
        }}
      >
        <Input
          placeholder="输入MCP Server名称"
          allowClear
          prefix={<SearchOutlined
            style={{
              color: token.colorTextSecondary,
            }}
          />}
          onChange={debounce((e: any) => {
            onFilterChange('name', e?.target?.value);
          }, 300)}

        />
      </Flex>
      <Flex>
        <Button>
          注册MCP Server
        </Button>
      </Flex>
    </Flex>
    <Flex
      flex={1}
      vertical

    >
      <InfiniteScroll
        dataLength={totalCount}
        next={loadMoreData}
        hasMore={dataRef?.current?.length < totalCount}
        loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
        endMessage={<Divider plain>没有更多啦</Divider>}
        scrollableTarget="scrollableDiv"
      >
        <div style={{ height: 20 }}></div>
        <List
          grid={{
            xs: 1,
            sm: 1,
            md: 1,
            lg: 1,
            xl: 1,
            xxl: 1,
          }}
          dataSource={dataRef?.current ?? []}
          renderItem={(item: McpServerMeta) => (
            <List.Item>
              <HoverCard
                style={{
                  cursor: 'default',
                }}
                icon={<Avatar src={defaultServerIcon} size={42} shape="square" />}
                title={<Flex
                  vertical
                  gap={token.marginXXS}
                  style={{
                    width: '100%',
                  }}
                >
                  <Flex
                    align="center"
                    gap={token.marginXXS}
                    style={{
                      width: '100%',
                    }}
                  >
                    <Typography.Text
                      ellipsis={{
                        tooltip: item?.name,
                      }}
                    >
                      {item?.name}
                    </Typography.Text>
                  </Flex>
                  <Flex
                    align="center"
                    style={{
                      fontSize: token.fontSizeSM,
                      fontWeight: 'normal',
                    }}
                    gap={token.marginXS}
                  >
                    <StatusTag
                      style={{
                        height: token.controlHeightSM - token.sizeUnit,
                        borderRadius: 10,
                        marginInlineEnd: 0,
                      }}
                    >
                      {item?.bizGroupName}
                    </StatusTag>
                    <Divider type="vertical" style={{ marginInline: 0 }} />
                    <div>
                      {`${item?.tools?.length} 关联Tool`}
                    </div>
                  </Flex>
                </Flex>}
                height={184}
                footer={<Flex
                  justify="space-between"
                  style={{
                    fontSize: token.fontSizeSM,
                    color: token.colorTextSecondary,
                  }}
                >
                  <div>
                    <AimiUser empIds={item?.creator} showAvatar size={20} />
                  </div>
                  <div>
                    {item?.gmtCreate ? getTimeStr(item?.gmtCreate) : '--'}
                  </div>
                </Flex>}
                actions={[<Link
                  key="serverDetail"
                  to={`/mcp/server/${item?.id}/detail`}
                  target={`server_detail_page_${item?.id}`}
                >查看</Link>,
                ...((user?.empId === item?.creator || user?.isAdmin) ? [<UpdateMcpServerModal
                  id={item?.id}
                  key="update"
                  onRefresh={refreshMcpServerList}
                />] : []),
                ]}
              >
                <Flex>
                  <Typography.Paragraph
                    style={{
                      color: token.colorTextSecondary,
                    }}
                    ellipsis={{
                      tooltip: item?.description,
                      rows: 2,
                    }}

                  >
                    {item?.description}
                  </Typography.Paragraph>
                </Flex>
              </HoverCard>
            </List.Item>
          )}
        />
      </InfiniteScroll>
    </Flex>
  </Flex>);
};

export default InfiniteScrollMcpServerList;
