.dashboardContainer {
  width: 100%;
  min-width: calc(1024px - 24px - 24px);
  height: calc(100vh - 80px - 24px - 24px - 32px);
  margin-top: var(--mc-margin-xl);
  overflow: hidden;
  .agentBtn {
    display: block;
    border-radius: 40px;
    // max-width: 220px;
    height: 40px;
    overflow: hidden;
    background-color: transparent;
    box-sizing: border-box;
    .name {
      width: fit-content;
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .agentBtnActive {
    background-color: var(--mc-color-fill-content);
  }
  .dashboardCard {
    flex: 1 1 auto;
    justify-content: space-between;
    overflow: scroll;
    padding: var(--mc-padding-lg) calc(var(--mc-margin-md) * 2) 0px;
    border: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
    border-radius: calc(var(--mc-border-radius) * 2);
    background: rgba(1, 4, 9, 0.3);
    backdrop-filter: blur(50px);
    .dashboardCardHeader {
      width: 100%;
    }
    // .itemCardContainer {
    //   width: 100%;
    //   // flex-wrap: wrap;
    //   max-height: 144px;
    //   overflow: hidden;
    //   .itemCard {
    //     height: 64px;
    //     font-size: var(--mc-font-size);
    //     font-weight: var(--mc-font-weight-strong);
    //     background: var(--mc-color-border-bg);
    //     padding: var(--mc-margin-md) var(--mc-margin);
    //     border: var(--mc-line-width) var(--mc-line-type) rgba(255, 255, 255, 0.1); // 特殊边框颜色
    //     border-radius: var(--mc-border-radius-lg);
    //     cursor: pointer;
    //     color: var(--mc-color-text-secondary);
    //     &:hover {
    //       background: var(--mc-color-fill-content-hover);
    //     }
    //     .itemCardCount {
    //       font-size: var(--mc-font-size-heading-3);
    //       font-weight: normal;
    //     }
    //     .itemCardName {
    //       white-space: nowrap;
    //     }
    //   }
    // }
    .itemCard {
      height: 64px;
      font-size: var(--mc-font-size);
      font-weight: var(--mc-font-weight-strong);
      background: var(--mc-color-border-bg);
      padding: var(--mc-margin-md) var(--mc-margin);
      border: var(--mc-line-width) var(--mc-line-type) rgba(255, 255, 255, 0.1); // 特殊边框颜色
      border-radius: var(--mc-border-radius-lg);
      cursor: pointer;
      color: var(--mc-color-text-secondary);
      &:hover {
        background: var(--mc-color-fill-content-hover);
      }
      .itemCardCount {
        font-size: var(--mc-font-size-heading-3);
        font-weight: normal;
      }
      .itemCardName {
        white-space: nowrap;
      }
    }
    .decs {
      color: var(--mc-color-text-secondary);
      font-weight: 400;
    }
    .contentWrapper {
      width: 100%;
      overflow: scroll;
      padding-top: 20px;
      padding-bottom: 20px;
      flex: 1 1 auto;
      .helloMsgWrapper {
        flex: 0 0 auto;
        .helloMsg {
          font-size: var(--mc-font-size-heading-4);
          font-weight: bold;
        }
      }
      .samples {
        justify-content: center;
        margin-top: 24px;
        flex-wrap: wrap;
        overflow: hidden;
      }
      .moreSampleBtn {
        width: 136px;
        flex: 0 0 auto;
        margin-top: 24px;
      }
    }
  }

  .questionButton {
    font-weight: var(--mc-font-weight-strong);
    // max-width: 330px;
    height: 40px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    border-radius: 40px;
    background-color: var(--mc-color-bg-container);
    &:hover {
      background-color: var(--mc-color-fill-content-hover);
    }
  }
  .questionListButton {
    font-weight: var(--mc-font-weight-strong);
    width: 136px;
    height: 40px;
    border-radius: 40px;
  }

  .chatExploreBtn {
    height: 28px;
    border-radius: 28px;
    color: var(--mc-color-primary);
    border-color: var(--mc-color-primary);
    background-color: transparent;
    &:hover {
      background-color: var(--mc-color-primary-hover) !important;
    }
  }
}
.dashboardAimiChat {
  margin-bottom: var(--mc-margin-lg);
}
.dashboardAimiChatWithWelcome {
  flex: 0 !important;
  :global {
    .aimi-pro-chat-chat-list-container {
      display: none;
      min-height: fit-content;
    }
  }
}

.leftPanel {
  margin: 0 auto;
  width: 1000px; // 定宽
  min-width: 800px;
  height: 100%;
  transition: all 0.2s ease-in-out;
}

.leftPanel.rightPanelVisible {
  width: 60% !important;
  padding: 0px calc(var(--mc-margin-md) * 2);
  min-width: auto;
}

.rightPanel {
  width: 40%;
  height: 100%;
  padding-bottom: var(--mc-padding-xs);
  border-left: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
  transform: translateX(0);
  opacity: 1;
  transition:
    transform 0.2s ease,
    opacity 0.2s ease-in-out,
    width 0.2s ease-in-out;
}
.rightPanel:not(.visible) {
  transform: translateX(100%);
  opacity: 0;
  width: 0px;
}
.spinContainer {
  height: calc(100% - 54px);
  :global {
    .aimi-spin-container {
      height: 100%;
    }
  }
}
