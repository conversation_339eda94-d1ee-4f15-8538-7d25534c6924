import React, { useEffect, useState } from 'react';
import { Flex, Image, theme, GetRef } from 'antd';
import { RefreshOutlined, CleanOutlined } from '@ali/mc-icons';
import { Sender, SenderProps } from '@ant-design/x';
import styles from './index.module.less';
import SenderOutline from './sender-outline.svg';

export type AimiSenderProps = {
  senderRef?: any;
  value?: string;
  showToolbar?: boolean;
  actions?: React.ReactNode[];
  onSubmit?: (message: string) => void;
  afterSubmit?: (message?: string) => void;
} & SenderProps;

const AimiSender = (props: AimiSenderProps) => {
  const { value, showToolbar = false, onSubmit, afterSubmit, loading, actions, senderRef, ...senderProps } = props;
  // const [open, setOpen] = useState<boolean>(false);
  // const [items, setItems] = React.useState<GetProp<AttachmentsProps, 'items'>>([]);
  const [inputText, setInputText] = useState<string>('');
  const [showSenderOutline, setShowSenderOutline] = useState(true); // 装饰边框
  const { token } = theme.useToken();

  // const attachmentsRef = React.useRef<GetRef<typeof Attachments>>(null);
  const innerSenderRef = React.useRef<GetRef<typeof Sender>>();
  const aimiSenderRef = senderRef || innerSenderRef;

  useEffect(() => {
    setInputText(value || '');
  }, [value]);

  const handleSubmit = (message?: string) => {
    onSubmit?.(message || inputText);
    afterSubmit?.(message || inputText);
    // setItems([]);
    setInputText('');
  };

  return (
    <div className={styles.aimiSenderWrapper}>
      {showToolbar && (
        <Flex className={styles.toolbar} gap={8}>
          <RefreshOutlined className={styles.toolbarItem} />
          <CleanOutlined className={styles.toolbarItem} />
        </Flex>
      )}
      {showSenderOutline && (
        <div className={styles.senderOutline}>
          <Image preview={false} src={SenderOutline} />
        </div>
      )}
      <Sender
        ref={aimiSenderRef}
        className={styles.senderStyle}
        placeholder="输入你想搜索的内容吧，通过cmd+回车换行"
        // header={
        //   <Sender.Header
        //     title="附件上传"
        //     styles={{
        //       content: {
        //         padding: 0,
        //       },
        //       header: {
        //         fontWeight: 500,
        //       },
        //     }}
        //     open={open}
        //     onOpenChange={setOpen}
        //     forceRender
        //   >
        //     <Attachments
        //       ref={attachmentsRef}
        //       beforeUpload={() => false}
        //       items={items}
        //       onChange={({ fileList }) => setItems(fileList)}
        //       placeholder={{
        //         icon: <UploadOutlined />,
        //         title: '上传文件',
        //         description: '将文件拖拽至此处，或点击开始上传',
        //       }}
        //       getDropContainer={() => aimiSenderRef.current?.nativeElement}
        //     />
        //   </Sender.Header>
        // }
        value={inputText}
        onChange={setInputText}
        // onPasteFile={(_, files) => {
        //   for (const file of files) {
        //     attachmentsRef.current?.upload(file);
        //   }
        //   setOpen(true);
        // }}
        onSubmit={handleSubmit}
        onFocus={() => setShowSenderOutline(false)}
        onBlur={() => setShowSenderOutline(true)}
        autoSize={{ minRows: 2, maxRows: 2 }}
        loading={loading}
        actions={false}
        footer={(info) => {
          const { SendButton, LoadingButton } = info.components;
          return (
            <Flex gap={token.margin} align="center" justify='flex-end'>
              {!!actions?.length && actions?.map((x) => x)}
              {loading ? <LoadingButton /> : <SendButton />}
            </Flex>
          );
        }}
        {...senderProps}
      />
    </div>
  );
};

export default AimiSender;
