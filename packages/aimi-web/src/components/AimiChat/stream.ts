import { AIStream } from 'ai';

export async function callStreamAPI({ message }, { onUpdate, onSuccess }) {
  const response = await fetch('/ai/api/v1/agent/run/aimi-main', {
    method: 'POST',
    headers: {
      Accept: '*/*',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      questions: [
        {
          role: 'user',
          content: message,
        },
      ],
      stream: true,
    }),
  });

  let fullText = '';
  AIStream(
    response,
    (data) => {
      const content = JSON.parse(data)?.data?.choices?.[0]?.delta?.content ?? '';
      fullText = fullText.concat(content);
      console.log('onUpdate ===>', fullText);
      onUpdate(fullText);
    },
    {
      onFinal: () => {
        console.log('onSuccess ===>', fullText);
        onSuccess(fullText);
      },
    },
  );

  // const reader = response?.body?.getReader();
  // const decoder = new TextDecoder('utf-8');
  // let done = false;
  // let fullText = '';

  // while (!done) {
  //   const { done: readerDone, value } = (await reader?.read()) ?? {};
  //   done = readerDone;

  //   if (value) {
  //     // 解码字节并处理数据
  //     const chunk = decoder.decode(value, { stream: true });
  //     console.log('chunk ===> ', chunk);
  //     let obj = {};
  //     try {
  //       obj = JSON.parse(chunk.replace('data:', ''));
  //     } catch (error) {}
  //     // console.log(obj);
  //     const content = obj?.data?.choices?.[0]?.delta?.content ?? '';
  //     console.log('onUpdate ===> ', content);
  //     fullText = fullText.concat(content);
  //     onUpdate(fullText);
  //   }
  // }

  // console.log('done', done);
  // onSuccess(fullText);
}
