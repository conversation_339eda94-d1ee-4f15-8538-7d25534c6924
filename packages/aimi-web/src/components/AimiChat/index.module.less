// dark 主题适配
.proChat {
  .contentRender {
    background-color: rgb(21, 27, 35);
    border-radius: var(--mc-border-radius);
    * {
      color: var(--mc-color-black) !important;
    }
  }
  :global {
    .aimi-sender-content {
      padding-bottom: 0px;
    }
  }
}

[data-color-mode='dark'] {
  .proChat {
    .contentRender {
      * {
        color: var(--mc-color-white) !important;
      }
    }
    :global {
      .aimi-btn .aimi-btn-icon {
        color: var(--mc-color-white) !important;
      }
    }
  }
}
