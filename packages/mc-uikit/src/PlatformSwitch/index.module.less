.platformSwitch {
  .divider {
    font-weight: normal;
    color: var(--mc-color-text);
  }

  .list {
    min-width: 350px;
  }

  :global {
    .mc-list-split .mc-list-item {
      cursor: pointer;
      border: none;
      border-radius: var(--mc-border-radius);
      padding-top: var(--mc-padding-content-vertical-sm)!important;
      padding-bottom: var(--mc-padding-content-vertical-sm)!important;

      &:hover {
        background-color: var(--mc-color-fill-secondary)!important;
      }

      .mc-list-item-meta {
        align-items: center;

        .mc-list-item-meta-avatar {
          > svg {
            font-size: var(--mc-font-size-heading-2, 32);
          }
        }
      }

      .mc-list-item-meta-title {
        font-size: var(--mc-font-size-sm);
      }

      .mc-list-item-meta-description {
        font-size: var(--mc-font-size-sm);
      }
    }
  }

  .platformLogo {
    color: var(--mc-color-text);
    font-weight: bold;
    > svg {
      font-size: 28px;
    }
  }
}

[data-color-mode='dark'] {
  .platformSwitch {
    :global {
      .mc-list-split .mc-list-item:hover {
        background-color: var(--mc-color-fill-secondary)!important;
      }
    }
  }
}