/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { DimData } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface GetDimDatasParams {
  /**
   * productId
   * @format int64
   */
  productId: number;
}
/**
 * No description
 * @tags PublishDimData
 * @name GetDimDatas
 * @summary 获取维度值列表
 * @request GET:/api/v1/publish/data/dim/values/getDimDatas
 */
export async function getDimDatas(query: GetDimDatasParams, options?: MethodOptions): Promise<DimData[]> {
  return request(`${baseUrl}/api/v1/publish/data/dim/values/getDimDatas`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
