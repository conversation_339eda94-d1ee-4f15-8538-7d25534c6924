/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { MetricComparativeData, MetricData } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface GetBuildParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /** month */
  month: string;
  /** entityType */
  entityType:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
}
/**
 * No description
 * @tags DashboardApp
 * @name GetBuild
 * @summary 构建数据统计
 * @request GET:/api/v1/dashboard/app/build
 */
export async function getBuild(query: GetBuildParams, options?: MethodOptions): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/app/build`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetBuildTrendParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /** entityType */
  entityType:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
  /**
   * startTime
   * @format int64
   */
  startTime: number;
  /**
   * endTime
   * @format int64
   */
  endTime: number;
  /** identifiers */
  identifiers: string;
}
/**
 * No description
 * @tags DashboardApp
 * @name GetBuildTrend
 * @summary 构建趋势数据统计
 * @request GET:/api/v1/dashboard/app/buildTrend
 */
export async function getBuildTrend(query: GetBuildTrendParams, options?: MethodOptions): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/app/buildTrend`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetComparativeStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /** month */
  month: string;
  /** comparativeMonth */
  comparativeMonth?: string;
  /** entityType */
  entityType:
    | 'ALTER_SHEET'
    | 'ALTER_SHEET_MODULE'
    | 'ALTER_SHEET_MODULE_MR'
    | 'APPKEY'
    | 'APPLICATION'
    | 'BRANCH_MERGE_RECORD'
    | 'CHANGE_FREE_RECORD'
    | 'CHECK_ITEM'
    | 'CODE_MERGE_RECORD'
    | 'CODE_REVIEW'
    | 'CODE_REVIEW_RECORD'
    | 'COLLABORATION_SPACE'
    | 'FLOW_PROCESS'
    | 'GATE_CHECK'
    | 'INTEGRATE_AREA'
    | 'INTEGRATE_AREA_BUFFER'
    | 'INTEGRATE_AREA_BUFFER_MODULE'
    | 'INTEGRATE_AREA_MODULE'
    | 'INTEGRATE_SHEET'
    | 'INTEGRATE_SHEET_MODULE'
    | 'INTEGRATION'
    | 'IOS_CERT'
    | 'IOS_PROFILE'
    | 'MAIN_FRAMEWORK'
    | 'MULTIPLE_INSTANCE_MONITOR'
    | 'NATIVE_DYNAMIC_BATCH'
    | 'NATIVE_DYNAMIC_RELEASE'
    | 'OPEN_CLIENT'
    | 'PATCH_CR'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'PIPELINE'
    | 'PIPELINE_EXECUTE_RECORD'
    | 'PIPELINE_INSTANCE'
    | 'PIPELINE_JOB_INSTANCE'
    | 'PIPELINE_STAGE_INSTANCE'
    | 'PIPELINE_TASK_INSTANCE'
    | 'PLUGIN'
    | 'PUBLISH'
    | 'PUBLISH_ARCHIVE_OPERATION'
    | 'REGRESSION'
    | 'REGRESSION_ITEM'
    | 'RELEASE'
    | 'REMOTE_PUBLISH'
    | 'SHADOW_OF_PIPELINE'
    | 'SHADOW_OF_PIPELINE_INSTANCE'
    | 'SUBMIT_TEST'
    | 'VERSION_PLAN'
    | 'WORK_FLOW';
}
/**
 * No description
 * @tags DashboardApp
 * @name GetComparativeStatistics
 * @summary 对比月份数据统计
 * @request GET:/api/v1/dashboard/app/comparativeStatistics
 */
export async function getComparativeStatistics(
  query: GetComparativeStatisticsParams,
  options?: MethodOptions,
): Promise<MetricComparativeData[]> {
  return request(`${baseUrl}/api/v1/dashboard/app/comparativeStatistics`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetIntegrationStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /** month */
  month: string;
}
/**
 * No description
 * @tags DashboardApp
 * @name GetIntegrationStatistics
 * @summary 集成数据统计
 * @request GET:/api/v1/dashboard/app/integration
 */
export async function getIntegrationStatistics(
  query: GetIntegrationStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/app/integration`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetIntegrationTrendStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * startTime
   * @format int64
   */
  startTime: number;
  /**
   * endTime
   * @format int64
   */
  endTime: number;
  /** identifiers */
  identifiers: string;
}
/**
 * No description
 * @tags DashboardApp
 * @name GetIntegrationTrendStatistics
 * @summary 集成趋势数据统计
 * @request GET:/api/v1/dashboard/app/integrationTrend
 */
export async function getIntegrationTrendStatistics(
  query: GetIntegrationTrendStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/app/integrationTrend`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPublishStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /** month */
  month: string;
}
/**
 * No description
 * @tags DashboardApp
 * @name GetPublishStatistics
 * @summary 发布数据统计
 * @request GET:/api/v1/dashboard/app/publish
 */
export async function getPublishStatistics(
  query: GetPublishStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/app/publish`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetPublishTrendStatisticsParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
  /**
   * startTime
   * @format int64
   */
  startTime: number;
  /**
   * endTime
   * @format int64
   */
  endTime: number;
  /** identifiers */
  identifiers: string;
}
/**
 * No description
 * @tags DashboardApp
 * @name GetPublishTrendStatistics
 * @summary 发布趋势数据统计
 * @request GET:/api/v1/dashboard/app/publishTrend
 */
export async function getPublishTrendStatistics(
  query: GetPublishTrendStatisticsParams,
  options?: MethodOptions,
): Promise<MetricData[]> {
  return request(`${baseUrl}/api/v1/dashboard/app/publishTrend`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
