/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { IosUploadFile, IpaUploadRecord } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface SubmitReviewSuccessParams {
  /**
   * uploadRecordId
   * @format int64
   */
  uploadRecordId: number;
}
/**
 * No description
 * @tags IpaUpload
 * @name SubmitReviewSuccess
 * @summary ipa 提审验证通过进入下一步
 * @request GET:/api/v1/ipaUpload/actions/review_success
 */
export async function submitReviewSuccess(query: SubmitReviewSuccessParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/ipaUpload/actions/review_success`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface BindTestGroupParams {
  /**
   * uploadRecordId
   * @format int64
   */
  uploadRecordId: number;
}
/**
 * No description
 * @tags IpaUpload
 * @name BindTestGroup
 * @summary 关联测试组
 * @request GET:/api/v1/ipaUpload/bindTestGroup
 */
export async function bindTestGroup(query: BindTestGroupParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/ipaUpload/bindTestGroup`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface CancelIpaUploadParams {
  /**
   * uploadRecordId
   * @format int64
   */
  uploadRecordId: number;
}
/**
 * No description
 * @tags IpaUpload
 * @name CancelIpaUpload
 * @summary 取消上传ipa
 * @request GET:/api/v1/ipaUpload/cancelIpaUpload
 */
export async function cancelIpaUpload(query: CancelIpaUploadParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/ipaUpload/cancelIpaUpload`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface QueryIpaListParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /** publishWay */
  publishWay:
    | 'ANDROID_APP_STORE'
    | 'APP_STORE'
    | 'CDN'
    | 'ENTERPRISE_ANDROID'
    | 'ENTERPRISE_IOS'
    | 'GOOGLE_PLAY'
    | 'GRAY_ANDROID'
    | 'HARMONY_AGC_Production'
    | 'HARMONY_AGC_TEST'
    | 'OTHER'
    | 'TESTFLIGHT'
    | 'UPDATE';
}
/**
 * No description
 * @tags IpaUpload
 * @name QueryIpaList
 * @summary 查询可以上传apple store的ipa列表
 * @request GET:/api/v1/ipaUpload/queryIpaList
 */
export async function queryIpaList(query: QueryIpaListParams, options?: MethodOptions): Promise<IosUploadFile[]> {
  return request(`${baseUrl}/api/v1/ipaUpload/queryIpaList`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface QueryUploadStatusParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags IpaUpload
 * @name QueryUploadStatus
 * @summary 查询ipa上传状态
 * @request GET:/api/v1/ipaUpload/queryUploadStatus
 */
export async function queryUploadStatus(
  query: QueryUploadStatusParams,
  options?: MethodOptions,
): Promise<IpaUploadRecord> {
  return request(`${baseUrl}/api/v1/ipaUpload/queryUploadStatus`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface QueryUploadStatusFromAppleStoreParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags IpaUpload
 * @name QueryUploadStatusFromAppleStore
 * @summary 从AppleStore查询ipa上传状态
 * @request GET:/api/v1/ipaUpload/queryUploadStatusFromAppleStore
 */
export async function queryUploadStatusFromAppleStore(
  query: QueryUploadStatusFromAppleStoreParams,
  options?: MethodOptions,
): Promise<string> {
  return request(`${baseUrl}/api/v1/ipaUpload/queryUploadStatusFromAppleStore`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface SubmitIpaReviewParams {
  /**
   * uploadRecordId
   * @format int64
   */
  uploadRecordId: number;
}
/**
 * No description
 * @tags IpaUpload
 * @name SubmitIpaReview
 * @summary 开始ipa审核
 * @request GET:/api/v1/ipaUpload/submitIpaReview
 */
export async function submitIpaReview(query: SubmitIpaReviewParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/ipaUpload/submitIpaReview`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags IpaUpload
 * @name SubmitIpaUpload
 * @summary 开始上传ipa
 * @request POST:/api/v1/ipaUpload/submitIpaUpload
 */
export async function submitIpaUpload(data: IosUploadFile, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/ipaUpload/submitIpaUpload`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface SyncUploadStatusParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
}
/**
 * No description
 * @tags IpaUpload
 * @name SyncUploadStatus
 * @summary syncUploadStatus
 * @request GET:/api/v1/ipaUpload/syncIpaUploadStatus
 */
export async function syncUploadStatus(query: SyncUploadStatusParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/ipaUpload/syncIpaUploadStatus`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface UpdateIpaUploadStatusParams {
  /**
   * ipaUploadId
   * @format int64
   */
  ipaUploadId: number;
  /** ipaUploadStatus */
  ipaUploadStatus:
    | 'ADD_SUCCESS'
    | 'AFTER_SUBMIT_REVIEW'
    | 'BEFORE_SUBMIT_REVIEW'
    | 'PROCESSING'
    | 'REVIEWING'
    | 'REVIEW_FAIL'
    | 'REVIEW_PASS'
    | 'UPLOADING'
    | 'UPLOAD_CANCEL'
    | 'UPLOAD_FAIL'
    | 'UPLOAD_SUCCESS'
    | 'WAIT_TO_BETA_REVIEW'
    | 'WAIT_TO_CANCEL_UPLOAD'
    | 'WAIT_TO_UPLOAD';
}
/**
 * No description
 * @tags IpaUpload
 * @name UpdateIpaUploadStatus
 * @summary updateIpaUploadStatus
 * @request GET:/api/v1/ipaUpload/updateIpaUploadStatus
 */
export async function updateIpaUploadStatus(
  query: UpdateIpaUploadStatusParams,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/ipaUpload/updateIpaUploadStatus`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface UpdateUploadStatusParams {
  /**
   * releaseId
   * @format int64
   */
  releaseId: number;
  /** status */
  status:
    | 'ADD_SUCCESS'
    | 'AFTER_SUBMIT_REVIEW'
    | 'BEFORE_SUBMIT_REVIEW'
    | 'PROCESSING'
    | 'REVIEWING'
    | 'REVIEW_FAIL'
    | 'REVIEW_PASS'
    | 'UPLOADING'
    | 'UPLOAD_CANCEL'
    | 'UPLOAD_FAIL'
    | 'UPLOAD_SUCCESS'
    | 'WAIT_TO_BETA_REVIEW'
    | 'WAIT_TO_CANCEL_UPLOAD'
    | 'WAIT_TO_UPLOAD';
}
/**
 * No description
 * @tags IpaUpload
 * @name UpdateUploadStatus
 * @summary 手动更新ipa上传状态
 * @request GET:/api/v1/ipaUpload/updateUploadStatus
 */
export async function updateUploadStatus(
  query: UpdateUploadStatusParams,
  options?: MethodOptions,
): Promise<IpaUploadRecord> {
  return request(`${baseUrl}/api/v1/ipaUpload/updateUploadStatus`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
