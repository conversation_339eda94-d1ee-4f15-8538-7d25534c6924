/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PaginationResult } from './Base';
import { PipelineJobTemplate, PipelineJobTemplateApproval, PipelineJobTemplateReq } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface GetBuildJobTemplatesParams {
  /** filterUsable */
  filterUsable?: boolean;
  /**
   * templateGroupId
   * @format int64
   */
  templateGroupId?: number;
}
/**
 * No description
 * @tags SystemPipelineJobTemplate
 * @name GetBuildJobTemplates
 * @summary 获取系统内置的构建配置模板
 * @request GET:/api/v1/system/config/jobTemplate/build
 */
export async function getBuildJobTemplates(
  query?: GetBuildJobTemplatesParams,
  options?: MethodOptions,
): Promise<PipelineJobTemplate[]> {
  return request(`${baseUrl}/api/v1/system/config/jobTemplate/build`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags SystemPipelineJobTemplate
 * @name UpdateBuildJobTemplate
 * @summary 更新构建配置模板
 * @request PUT:/api/v1/system/config/jobTemplate/build
 */
export async function updateBuildJobTemplate(
  data: PipelineJobTemplateReq,
  options?: MethodOptions,
): Promise<PipelineJobTemplateApproval> {
  return request(`${baseUrl}/api/v1/system/config/jobTemplate/build`, {
    method: 'PUT',
    body: data as any,
    ...options,
  });
}

export interface CreateBuildJobTemplateParams {
  /**
   * templateGroupId
   * @format int64
   */
  templateGroupId: number;
}
/**
 * No description
 * @tags SystemPipelineJobTemplate
 * @name CreateBuildJobTemplate
 * @summary 创建构建配置模板
 * @request POST:/api/v1/system/config/jobTemplate/build
 */
export async function createBuildJobTemplate(
  query: CreateBuildJobTemplateParams,
  data: PipelineJobTemplateReq,
  options?: MethodOptions,
): Promise<PipelineJobTemplateApproval> {
  return request(`${baseUrl}/api/v1/system/config/jobTemplate/build`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface DeleteBuildJobTemplateParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags SystemPipelineJobTemplate
 * @name DeleteBuildJobTemplate
 * @summary 删除构建配置模版
 * @request DELETE:/api/v1/system/config/jobTemplate/build
 */
export async function deleteBuildJobTemplate(
  query: DeleteBuildJobTemplateParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/system/config/jobTemplate/build`, {
    method: 'DELETE',
    params: query,
    ...options,
  });
}

export interface QueryBuildJobTemplatePageParams {
  /** filterUsable */
  filterUsable?: boolean;
  /**
   * templateGroupId
   * @format int64
   */
  templateGroupId?: number;
  /**
   * pageNo
   * @format int32
   */
  pageNo?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
}
/**
 * No description
 * @tags SystemPipelineJobTemplate
 * @name QueryBuildJobTemplatePage
 * @summary 分页获取系统内置的构建配置模版
 * @request GET:/api/v1/system/config/jobTemplate/buildPage
 */
export async function queryBuildJobTemplatePage(
  query?: QueryBuildJobTemplatePageParams,
  options?: MethodOptions,
): Promise<PaginationResult<PipelineJobTemplate>> {
  return request(`${baseUrl}/api/v1/system/config/jobTemplate/buildPage`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
