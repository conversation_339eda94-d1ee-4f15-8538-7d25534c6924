/**
 * 检查sessionStorage是否可用。
 * @returns {boolean} 如果sessionStorage可用则返回true，否则返回false。
 */
function storageAvailable(): boolean {
  try {
    const test = '__storage_test__';
    sessionStorage.setItem(test, test);
    sessionStorage.removeItem(test);
    return true;
  } catch (error) {
    console.error('无法访问localStorage', error);
    return false;
  }
}

type CacheItemOption = {
  ttl?: number;
};

/**
 * @template T - 存储在缓存中的元素的类型。
 */
class LocalCache<T = any> {
  private static instance: LocalCache<any>; // 静态单例实例。

  // private cache: LRU<T>; // 本地缓存
  private storage: Storage | null = null; // 存储
  private ttl: number; // 元素的生存时间
  private max: number; // 条目最大的数量
  private storageKeyPrefix = '_lc_'; // localStorage中的键的前缀
  private keysStorageKey = '_lc_keys'; // 用于在localStorage中存储所有键的键

  /**
   * 获取 LocalCache 的单例实例。
   * @template T - 存储在缓存中的元素的类型。
   * @param {number} max - 缓存可容纳的最大元素数量。
   * @param {number} [ttl] - 元素的存在时间（以毫秒为单位）。
   * @param {boolean} [resetTtl] - 是否在获取元素时重置其存在时间。
   * @returns {LocalCache} LocalLRU的单例实例。
   */
  static getInstance<T>() {
    if (!LocalCache.instance) {
      LocalCache.instance = new LocalCache<T>(500, 0);
    }
    return LocalCache.instance;
  }

  private constructor(max: number, ttl?: number) {
    this.max = max || 10;
    this.ttl = ttl || 1000 * 60;
    if (storageAvailable()) {
      this.storage = localStorage;
      try {
        this.load();
      } catch (_e) {
        console.error('无法从localStorage加载缓存:', _e);
      }
    } else {
      console.warn('localStorage不可用. 使用内存存储.');
    }
  }

  /**
   * 从localStorage加载缓存。
   */
  private load(): void {
    const now = new Date().getTime();
    if (!this.storage) return;
    const keys = JSON.parse(this.storage.getItem(this.keysStorageKey) || '[]');

    const filteredKeys = keys.filter((key: string) => {
      const item = JSON.parse(this.storage.getItem(this.storageKeyPrefix + key) || '{}');
      if (now < item?.expiry && item.hasOwnProperty('value')) {
        return true;
      } else {
        this.storage.removeItem(this.storageKeyPrefix + key);
        return false;
      }
    });

    // 更新缓存key
    this.storage.setItem(this.keysStorageKey, JSON.stringify(filteredKeys));
  }

  /**
   * 从缓 存中获取元素。
   * @param {string} key - 元素的键。
   * @returns {T | undefined} 如果找到元素则返回该元素，否则返回undefined。
   */
  public get(key: string): T | undefined {
    const now = new Date().getTime();
    try {
      const data = JSON.parse(this.storage.getItem(this.storageKeyPrefix + key) || '{}');
      if (now < data?.expiry && data.hasOwnProperty('value')) {
        return data.value;
      }

      return undefined;
    } catch (err) {
      console.log(err);
    }

    return undefined;
  }

  /**
   * 在缓存中设置元素。
   * @param {string} key - 元素的键。
   * @param {T} value - 元素的值。
   */
  public set(key: string, value: T, options?: CacheItemOption): void {
    const now = new Date().getTime();
    const expiry = now + (options?.ttl || this.ttl || 0);

    if (!this.storage) return;

    try {
      // 获取持久化的缓存 key 列表
      let cachedKeys = JSON.parse(this.storage.getItem(this.keysStorageKey) || '[]');

      const item = { value, expiry };
      // 写持久化缓存
      this.storage.setItem(this.storageKeyPrefix + key, JSON.stringify(item));

      // 更新缓存key的位置
      const indexToRemove = cachedKeys.indexOf(key);
      if (indexToRemove > -1 && indexToRemove < cachedKeys.length) {
        cachedKeys.splice(indexToRemove, 1);
      }
      cachedKeys.push(key);

      let deletedKeys: string[] = [];
      if (cachedKeys.length > this.max) {
        deletedKeys = cachedKeys.slice(0, cachedKeys.length - this.max);
        cachedKeys = cachedKeys.slice(cachedKeys.length - this.max);
      }
      // 持久化保存 key 列表
      this.storage.setItem(this.keysStorageKey, JSON.stringify(cachedKeys));

      // 按照先进先出的原则将数据删掉
      if (deletedKeys.length > 0) {
        // 将其他的删掉
        deletedKeys.forEach((key: string) => {
          this.storage.removeItem(this.storageKeyPrefix + key);
        });
      }
    } catch (err) {
      console.error(err);
    }
  }
}

export default LocalCache;
