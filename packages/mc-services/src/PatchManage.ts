/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface ClearCacheParams {
  /** name */
  name:
    | 'APP_PUBLISH_FILE'
    | 'PATCH_CR'
    | 'PATCH_CR_PUBLISH_AREA'
    | 'PATCH_DEPENDENCY'
    | 'PATCH_PUBLISH_AREA'
    | 'PATCH_RELEASE'
    | 'app';
}
/**
 * No description
 * @tags PatchManage
 * @name ClearCache
 * @summary clearCache
 * @request GET:/api/v1/patch/manage/clearCache
 */
export async function clearCache(query: ClearCacheParams, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/patch/manage/clearCache`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
