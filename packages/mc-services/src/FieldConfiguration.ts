/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { ProductFieldConfigCreateRequest } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

/**
 * @description 保存产品字段配置信息
 * @tags FieldConfiguration
 * @name SaveProductConfig
 * @summary 保存产品字段配置信息
 * @request POST:/api/v1/product_field_config
 */
export async function saveProductConfig(data: ProductFieldConfigCreateRequest, options?: MethodOptions): Promise<void> {
  return request(`${baseUrl}/api/v1/product_field_config`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * @description 更新产品字段配置信息
 * @tags FieldConfiguration
 * @name UpdateProductConfig
 * @summary 更新产品字段配置信息
 * @request PUT:/api/v1/product_field_config/{id}
 */
export async function updateProductConfig(
  id: number,
  data: ProductFieldConfigCreateRequest,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/product_field_config/${id}`, {
    method: 'PUT',
    body: data as any,
    ...options,
  });
}

/**
 * @description 更新部分产品字段配置信息
 * @tags FieldConfiguration
 * @name PatchUpdateProductConfigUsingPatch
 * @summary 更新部分产品字段配置信息
 * @request PATCH:/api/v1/product_field_config/{id}
 */
export async function patchUpdateProductConfigUsingPatch(
  id: number,
  data: ProductFieldConfigCreateRequest,
  options?: MethodOptions,
): Promise<void> {
  return request(`${baseUrl}/api/v1/product_field_config/${id}`, {
    method: 'PATCH',
    body: data as any,
    ...options,
  });
}
