/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** 更新知识库请求 */
export interface KnowledgeBaseUpdateRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /**
   * 知识库ID
   * @format int64
   */
  knowledgeBaseId?: number;
  /** 名称 */
  name?: string;
  /** 描述信息 */
  description?: string;
  /** 嵌入向量模型名称 */
  embeddingVectorModel?: string;
  /** 知识库文档自动更新类型 */
  updateFrequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'NO_UPDATE';
  /**
   * 业务分组ID
   * @format int64
   */
  bizGroupId?: number;
}

export interface AimiResultLong {
  success?: boolean;
  /** @format int64 */
  data?: number;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AimiResultBoolean {
  success?: boolean;
  data?: boolean;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

/** 知识库召回配置 */
export interface DataSearchConfig {
  /** 召回方式列表 */
  retrievalMethods?: ('EMBEDDING' | 'KEYWORD')[];
  /** 向量召回配置 */
  embeddingConfig?: EmbeddingConfig;
  /** 关键词召回配置 */
  keywordConfig?: KeywordConfig;
  /** 重排配置 */
  rerankConfig?: RerankConfig;
}

/** 向量召回配置 */
export interface EmbeddingConfig {
  /** 召回类型 */
  retrievalType?: 'ORIGIN_CHUNK' | 'ORIGIN_FILE';
  /**
   * 召回数量
   * @format int32
   */
  retrievalCounts?: number;
  /**
   * 阈值
   * @format double
   */
  threshold?: number;
  /** 标签，不为空时，仅带标签的文档会被召回 */
  tags?: string[];
}

/** 关键词召回配置 */
export interface KeywordConfig {
  /** 召回类型 */
  retrievalType?: 'ORIGIN_CHUNK' | 'ORIGIN_FILE';
  /**
   * 召回数量
   * @format int32
   */
  retrievalCounts?: number;
  /**
   * 阈值
   * @format double
   */
  threshold?: number;
  /** 标签，不为空时，仅带标签的文档会被召回 */
  tags?: string[];
}

/** 知识库召回配置请求 */
export interface KnowledgeBaseRetrievalConfigRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /**
   * 知识库ID
   * @format int64
   */
  knowledgeBaseId?: number;
  /** 知识库召回配置 */
  dataSearchConfig?: DataSearchConfig;
}

/** 重排配置 */
export interface RerankConfig {
  /** 重排模型 */
  model?: string;
}

/** 数据分片配置 */
export interface DataChunkConfig {
  /** 用于分隔文档段落的分隔符 */
  segmentSeparator?: string;
  /**
   * 每个段落的最大长度
   * @format int32
   */
  maxSegmentLength?: number;
  /**
   * 相邻段落之间的重叠字符数
   * @format int32
   */
  segmentOverlap?: number;
  /** 段落保留文字标题 */
  keepSegmentTitle?: boolean;
  /**
   * 表格行内容最大长度
   * @format int32
   */
  sheetRowLengthLimit?: number;
}

/** 知识库数据预处理配置 */
export interface DataPreprocessConfig {
  /** 是否删除url */
  deleteUrl?: boolean;
  /** 是否删除图片 */
  deleteImage?: boolean;
  /** 是否删除邮箱 */
  deleteEmail?: boolean;
  /** 导入的Sheet名称 */
  sheetName?: string;
  /**
   * 表头行索引
   * @format int32
   */
  headerRowIndex?: number;
  /**
   * 数据首行索引
   * @format int32
   */
  dataFirstRowIndex?: number;
  /** 选择的列索引列表 */
  selectedColumnIndexes?: number[];
  /**
   * 下载任务ID
   * @format int64
   */
  downloadTaskId?: number;
  /** oss下载 */
  ossDownloadUrl?: string;
}

/** 更新文档请求 */
export interface DocumentChunkConfigUpdateRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /**
   * 文档id
   * @format int64
   */
  documentId?: number;
  /** 数据分片配置 */
  chunkConfig?: DataChunkConfig;
  /** 知识库数据预处理配置 */
  preprocessConfig?: DataPreprocessConfig;
}

/** 更新分片请求 */
export interface ChunkUpdateRequest {
  /**
   * 分片id
   * @format int64
   */
  chunkId?: number;
  /** 分片内容 */
  content?: string;
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
}

export interface AgentUpdateRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /**
   * AgentId
   * @format int64
   */
  id?: number;
  /** Agent名称 */
  name?: string;
  /** Agent详细介绍 */
  description?: string;
  /**
   * 业务分组ID
   * @format int64
   */
  bizGroupId?: number;
}

export interface AgentInstanceOnlineRequest {
  operator?: string;
  /** @format date-time */
  time?: string;
  /** @format int64 */
  agentInstanceId?: number;
}

export interface AgentInstanceOfflineRequest {
  operator?: string;
  /** @format date-time */
  time?: string;
  /** @format int64 */
  agentInstanceId?: number;
}

export interface AgentOnlineRequest {
  operator?: string;
  /** @format date-time */
  time?: string;
  /** @format int64 */
  agentId?: number;
}

export interface AgentOfflineRequest {
  operator?: string;
  /** @format date-time */
  time?: string;
  /** @format int64 */
  agentId?: number;
}

export interface AgentConversationSampleUpdateRequest {
  /** @format int64 */
  id?: number;
  agentIdentifier?: string;
  question?: string;
  description?: string;
  tag?: string;
  /** @format int32 */
  rankNum?: number;
  url?: string;
}

/** Agent关联知识库列表 */
export interface AgentKnowledgeBaseVO {
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 创建者 */
  creator?: string;
  /** 修改者 */
  modifier?: string;
  /**
   * 创建时间
   * @format date-time
   */
  gmtCreate?: string;
  /**
   * 最后修改时间
   * @format date-time
   */
  gmtModified?: string;
  /**
   * 业务组ID
   * @format int64
   */
  bizGroupId?: number;
  /** 标识符 */
  identifier?: string;
  /** 名称 */
  name?: string;
  /** 描述信息 */
  description?: string;
  /** 知识库召回配置 */
  searchConfig?: DataSearchConfig;
}

/** 大模型配置 */
export interface AgentLlmConfig {
  /** 模型名 */
  model?: string;
  /** 模型logo */
  logo?: string;
  /**
   * 惊喜值（temperature）
   * @format double
   */
  temperature?: number;
  /**
   * 最大输出token数（max_tokens）
   * @format int64
   */
  maxTokens?: number;
  /**
   * 存在惩罚参数（presence_penalty）
   * @format double
   */
  presencePenalty?: number;
  /**
   * 频率惩罚参数（frequency_penalty）
   * @format double
   */
  frequencyPenalty?: number;
  /**
   * 采样阈值（top_p）
   * @format double
   */
  topP?: number;
  /**
   * 采样前k个token（top_k）
   * @format double
   */
  topK?: number;
  /**
   * 随机种子（seed）
   * @format int64
   */
  seed?: number;
  /** 响应格式 */
  responseFormat?: 'JSON' | 'TEXT';
  /** 停止词 */
  stop?: string[];
}

/** Agent关联MCP Server */
export interface AgentMcpServerVO {
  /**
   * MCP Server ID
   * @format int64
   */
  id?: number;
  /**
   * MCP Server创建时间
   * @format date-time
   */
  gmtCreate?: string;
  /**
   * MCP Server修改时间
   * @format date-time
   */
  gmtModified?: string;
  /** MCP Server创建人 */
  creator?: string;
  /** MCP Server修改人 */
  modifier?: string;
  /** MCP Server名称 */
  name?: string;
  /** MCP Server描述 */
  description?: string;
  /** MCP Server路径 */
  path?: string;
  /** MCP Server基础路径 */
  baseUrl?: string;
  /**
   * MCP Server业务组ID
   * @format int64
   */
  bizGroupId?: number;
}

export interface AgentUpdateConfigRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /**
   * AgentId
   * @format int64
   */
  id?: number;
  /** 大模型配置 */
  llmConfig?: AgentLlmConfig;
  /** 提示词模板 */
  promptTemplate?: string;
  /** 提示词变量列表 */
  variables?: AgentVariable[];
  /** 知识库召回配置 */
  dataSearchConfig?: DataSearchConfig;
  /** Agent关联知识库列表 */
  knowledgeBases?: AgentKnowledgeBaseVO[];
  /** 知识库绑定变量 */
  knowledgeBaseBindVariableKey?: string;
  /** Agent关联MCP Server列表 */
  mcpServerMetas?: AgentMcpServerVO[];
}

/** 提示词变量列表 */
export interface AgentVariable {
  /** 变量key */
  key?: string;
  /** 变量名称 */
  name?: string;
  /** 变量类型 */
  type?: 'TEXT' | 'SELECT';
  /**
   * 最大长度
   * @format int32
   */
  maxLength?: number;
  /** 是否必填 */
  required?: boolean;
}

/** 新建知识库请求 */
export interface KnowledgeBaseAddRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /**
   * 业务组ID
   * @format int64
   */
  bizGroupId?: number;
  /** 名称 */
  name?: string;
  /** 描述信息 */
  description?: string;
  /** 嵌入向量模型名称 */
  embeddingVectorModel?: string;
  /** 知识库文档自动更新类型 */
  updateFrequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'NO_UPDATE';
  /** 数据分片配置 */
  chunkConfig?: DataChunkConfig;
  /** 知识库数据预处理配置 */
  preprocessConfig?: DataPreprocessConfig;
  /** 文档列表 */
  documents?: OriginDocumentVO[];
}

/** 文档列表 */
export interface OriginDocumentVO {
  /** 来源类型 */
  sourceType?: 'DINGDOC' | 'YUQUE' | 'WEB' | 'UPLOAD';
  /** 文档类型 */
  documentType?: 'TEXT' | 'SHEET' | 'PDF' | 'MARKDOWN';
  /** 节点/知识库链接 */
  sourceUrl?: string;
  /** 鉴权token, yuque使用 */
  token?: string;
}

/** 知识库文档更新请求 */
export interface KnowledgeBaseDocumentSyncRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /** 知识库内容更新模式 */
  type?: 'FULL_OVERWRITE' | 'FULL_MODIFY' | 'AUTO_FULL_MODIFY' | 'PARTIAL_OVERWRITE' | 'PARTIAL_MODIFY';
  /**
   * 知识库id
   * @format int64
   */
  knowledgeBaseId?: number;
  /** 文档id列表, 源数据id和文档数据id均可，根据函数和同步类型进行区分 */
  documentIds?: number[];
}

export interface AimiResultKnowledgeBaseDocumentSyncResponse {
  success?: boolean;
  /** 知识库更新结果 */
  data?: KnowledgeBaseDocumentSyncResponse;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

/** 知识库更新结果 */
export interface KnowledgeBaseDocumentSyncResponse {
  /**
   * 知识库id
   * @format int64
   */
  knowledgeBaseId?: number;
  /** 找到的文档id列表 */
  foundIds?: number[];
  /** 未找到文档id列表 */
  notFoundIds?: number[];
}

/** 知识库上传请求 */
export interface KnowledgeBaseUploadRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /** 文档来源信息 */
  documents?: OriginDocumentVO[];
  /**
   * 知识库id
   * @format int64
   */
  knowledgeBaseId?: number;
  /** 数据分片配置 */
  chunkConfig?: DataChunkConfig;
  /** 知识库数据预处理配置 */
  preprocessConfig?: DataPreprocessConfig;
}

/** 表格文档预览请求 */
export interface TableDocumentPreviewRequest {
  operator?: string;
  /** @format date-time */
  time?: string;
  /**
   * 文档下载任务id
   * @format int64
   */
  taskId?: number;
  /** 知识库数据预处理配置 */
  preprocessConfig?: DataPreprocessConfig;
}

export interface AimiResultTableDocumentSheetVO {
  success?: boolean;
  data?: TableDocumentSheetVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface TableDocumentSheetVO {
  sheetName?: string;
  /** @format int64 */
  rowCount?: number;
  /** @format int64 */
  colCount?: number;
  headers?: string[];
  dataSet?: string[][];
}

/** 刷新文档 */
export interface DocumentReloadRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /**
   * 文档id
   * @format int64
   */
  documentId?: number;
}

/** 下载任务创建请求 */
export interface DocumentDownloadTaskCreateRequest {
  operator?: string;
  /** @format date-time */
  time?: string;
  /** 来源类型 */
  sourceType?: 'DINGDOC' | 'YUQUE' | 'WEB' | 'UPLOAD';
  /** 文档类型 */
  documentType?: 'TEXT' | 'SHEET' | 'PDF' | 'MARKDOWN';
  /** 节点/知识库链接 */
  sourceUrl?: string;
  /** 鉴权token, yuque使用 */
  token?: string;
  /**
   * document id, 优先级高于上述参数
   * @format int64
   */
  documentId?: number;
}

/** 新建Agent请求 */
export interface AgentAddRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /** Agent类型 */
  type?: 'RAG';
  /** Agent名称 */
  name?: string;
  /** Agent详细介绍 */
  description?: string;
  /**
   * 业务组ID
   * @format int64
   */
  bizGroupId?: number;
}

export interface AgentPublishRequest {
  /** 操作人 */
  operator?: string;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /**
   * AgentId
   * @format int64
   */
  id?: number;
  /** 大模型配置 */
  llmConfig?: AgentLlmConfig;
  /** 提示词模板 */
  promptTemplate?: string;
  /** 提示词变量列表 */
  variables?: AgentVariable[];
  /** Agent关联知识库列表 */
  knowledgeBases?: AgentKnowledgeBaseVO[];
  /** 知识库绑定变量 */
  knowledgeBaseBindVariableKey?: string;
  /** Agent关联MCP Server列表 */
  mcpServerMetas?: AgentMcpServerVO[];
}

/** 知识库列表 */
export interface AgentInstanceKnowledgeBaseVO {
  /** @format int64 */
  id?: number;
  identifier?: string;
  name?: string;
  description?: string;
  /** 知识库召回配置 */
  searchConfig?: DataSearchConfig;
}

/** MCP Server列表 */
export interface AgentInstanceMcpServerVO {
  /** @format int64 */
  id?: number;
  name?: string;
  description?: string;
  sseUrl?: string;
}

export interface AgentInstanceVO {
  /** @format int64 */
  id?: number;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  creator?: string;
  modifier?: string;
  /**
   * Agent ID
   * @format int64
   */
  agentId?: number;
  /** 标识符（UUID） */
  identifier?: string;
  /** 状态 */
  status?: 'ONLINE' | 'OFFLINE';
  /** 类型 */
  type?: 'RAG';
  /** 名称 */
  name?: string;
  /** 描述 */
  description?: string;
  /** 提示词模版 */
  promptTemplate?: string;
  /** 变量列表 */
  variables?: AgentVariable[];
  /** 大模型配置 */
  llmConfig?: AgentLlmConfig;
  /** 知识库列表 */
  knowledgeBases?: AgentInstanceKnowledgeBaseVO[];
  /** 知识库绑定变量 */
  knowledgeBaseBindVariableKey?: string;
  /** MCP Server列表 */
  mcpServerMetas?: AgentInstanceMcpServerVO[];
  /**
   * 版本，递增整数
   * @format int64
   */
  versionCode?: number;
  /** API */
  api?: string;
}

export interface AimiResultAgentInstanceVO {
  success?: boolean;
  data?: AgentInstanceVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AgentTokenAddRequest {
  operator?: string;
  /** @format date-time */
  time?: string;
}

export interface AgentTokenVO {
  /** @format int64 */
  id?: number;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  creator?: string;
  modifier?: string;
  /** token */
  token?: string;
}

export interface AimiResultAgentTokenVO {
  success?: boolean;
  data?: AgentTokenVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AgentVO {
  /** @format int64 */
  id?: number;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  creator?: string;
  modifier?: string;
  /**
   * 业务分组ID
   * @format int64
   */
  bizGroupId?: number;
  /** 类型 */
  type?: 'RAG';
  /** 名称 */
  name?: string;
  /** 标识符，不带分隔符的UUID */
  identifier?: string;
  /** 描述 */
  description?: string;
  /** 提示词模版 */
  promptTemplate?: string;
  /** 变量列表 */
  variables?: AgentVariable[];
  /** 大模型配置 */
  llmConfig?: AgentLlmConfig;
  /** 知识库列表 */
  knowledgeBases?: AgentKnowledgeBaseVO[];
  /** 知识库绑定变量 */
  knowledgeBaseBindVariableKey?: string;
  /** MCP Server列表 */
  mcpServerMetas?: AgentMcpServerVO[];
  /** 状态 */
  status?: 'ONLINE' | 'DRAFT' | 'OFFLINE';
  /** 最新API链接 */
  latestApi?: string;
  /** 最新Instance标识符 */
  latestInstanceIdentifier?: string;
}

export interface AgentInstanceBO {
  type?: 'RAG';
  name?: string;
  identifier?: string;
  description?: string;
  status?: 'ONLINE' | 'OFFLINE';
  groupIdentifier?: string;
  promptTemplate?: string;
  promptExecutionSettings?: PromptExecutionSettingsDTO;
  knowledgeBaseBindVariableKey?: string;
  agentVariables?: AgentVariableBO[];
  knowledgeBases?: KnowledgeConfigBO[];
  mcpServerMetas?: AgentInstanceMcpServer[];
}

export interface AgentInstanceMcpServer {
  /** @format int64 */
  id?: number;
  name?: string;
  description?: string;
  sseUrl?: string;
}

export interface AgentVariableBO {
  key?: string;
  name?: string;
  type?: 'TEXT' | 'SELECT';
  /** @format int32 */
  maxLength?: number;
  required?: boolean;
}

export interface AimiResultAgentInstanceBO {
  success?: boolean;
  data?: AgentInstanceBO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface KnowledgeConfigBO {
  identifier?: string;
  name?: string;
  description?: string;
  /** 知识库召回配置 */
  searchConfig?: DataSearchConfig;
}

export interface PromptExecutionSettingsDTO {
  service_id?: string;
  model_id?: string;
  /** @format double */
  temperature?: number;
  /** @format double */
  top_p?: number;
  /** @format int32 */
  top_k?: number;
  /** @format int64 */
  seed?: number;
  /** @format double */
  presence_penalty?: number;
  /** @format double */
  frequency_penalty?: number;
  /** @format int32 */
  max_tokens?: number;
  stop_sequences?: string[];
  token_selection_biases?: Record<string, number>;
  response_format?: ResponseFormat;
  enable_thinking?: boolean;
  /** @format int32 */
  thinking_budget?: number;
}

export interface ResponseFormat {
  type?: string;
}

export interface AgentConversationSampleAddRequest {
  agentIdentifier?: string;
  question?: string;
  description?: string;
  tag?: string;
  /** @format int32 */
  rankNum?: number;
  url?: string;
}

export interface AimiResultListUserVO {
  success?: boolean;
  data?: UserVO[];
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface UserVO {
  /** 工号 */
  empId?: string;
  /** 花名 */
  nickName?: string;
  /** 真名 */
  name?: string;
  /** 英文名 */
  englishName?: string;
  /** 邮箱 */
  email?: string;
  /** 职位描述：eg.技术-基础平台-开发 */
  jobName?: string;
  /**
   * 员工类型
   * 正式\外包
   */
  empType?: string;
  /** 离职状态 A：在职 I：离职 */
  workStatus?: string;
  /** 主管工号 */
  superWorkNo?: string;
  /** 公司编号 */
  corpDeptNo?: string;
  /** 公司名称，淘天集团 */
  corpName?: string;
  /** 公司英文名 */
  corpEnName?: string;
  /** BG编号 */
  bgDeptNo?: string;
  /** BG名称，淘天集团-业务技术 */
  bgName?: string;
  /** BG英文名 */
  bgEnName?: string;
  /** BU编号 */
  buDeptNo?: string;
  /** BU名称，淘天集团-业务技术-终端平台 */
  buName?: string;
  /** BU英文名 */
  buEnName?: string;
  /** 部门编号 */
  deptNo?: string;
  /** 部门名称 */
  deptName?: string;
  /** 部门英文名称 */
  deptEnName?: string;
  /** 钉钉Id */
  dingTalkId?: string;
  /** 钉钉Nick */
  dingTalkNick?: string;
  /** 钉钉地址 */
  dingTalkUrl?: string;
  /** 头像 */
  avatar?: string;
  /** 内外地址 */
  workUrl?: string;
}

export interface AimiResultUserVO {
  success?: boolean;
  data?: UserVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AimiResultKnowledgeBaseVO {
  success?: boolean;
  data?: KnowledgeBaseVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface KnowledgeBaseVO {
  /** @format int64 */
  id?: number;
  /**
   * 业务组ID
   * @format int64
   */
  bizGroupId?: number;
  /** 业务组名称 */
  bizGroupName?: string;
  name?: string;
  description?: string;
  identifier?: string;
  embeddingVectorModel?: string;
  vectorDatabase?: string;
  /** 知识库文档自动更新类型 */
  updateFrequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'NO_UPDATE';
  /** 知识库召回配置 */
  searchConfig?: DataSearchConfig;
  status?: 'ENABLED' | 'DISABLED';
  creator?: string;
  /** @format date-time */
  gmtCreate?: string;
  modifier?: string;
  /** @format date-time */
  gmtModified?: string;
  /**
   * 文档数量
   * @format int64
   */
  documentCount?: number;
}

/** 知识库列表请求 */
export interface KnowledgeBasePageRequest {
  /**
   * 页码，从0开始
   * @format int32
   * @default 0
   */
  pageNo?: number;
  /**
   * 每页大小，默认10
   * @format int32
   * @default 10
   */
  pageSize?: number;
  /**
   * 操作时间
   * @format date-time
   */
  time?: string;
  /** 名称 */
  operator?: string;
  /** 是否只显示我的 */
  onlyShowMine?: boolean;
  /** 知识库名称 */
  name?: string;
}

export interface AimiResultPageKnowledgeBaseVO {
  success?: boolean;
  data?: PageKnowledgeBaseVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface PageKnowledgeBaseVO {
  items?: KnowledgeBaseVO[];
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int64 */
  totalCount?: number;
  /** @format int32 */
  pages?: number;
}

export interface AimiResultListEmbeddingModelVO {
  success?: boolean;
  data?: EmbeddingModelVO[];
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface EmbeddingModelVO {
  displayName?: string;
  key?: string;
  /** @format int32 */
  maxContextLength?: number;
}

export interface AimiResultDocumentConfigVO {
  success?: boolean;
  data?: DocumentConfigVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface DocumentConfigVO {
  /** embedding模型 */
  embeddingModel?: string;
  /** 数据分片配置 */
  chunkConfig?: DataChunkConfig;
  /** 知识库数据预处理配置 */
  preprocessConfig?: DataPreprocessConfig;
}

export interface AimiResultDocumentVO {
  success?: boolean;
  /** 文档传输对象 */
  data?: DocumentVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

/** 文档评分 */
export interface DocumentScoreVO {
  /** 文档质量等级 */
  level?: string;
}

/** 文档传输对象 */
export interface DocumentVO {
  /**
   * 文档id
   * @format int64
   */
  id?: number;
  /**
   * 创建时间
   * @format date-time
   */
  gmtCreate?: string;
  /** 创建人 */
  creator?: string;
  /** 修改人 */
  modifier?: string;
  /**
   * 最后修改时间
   * @format date-time
   */
  gmtModified?: string;
  /** 文档标题 */
  title?: string;
  /** 文档来源URL */
  sourceUrl?: string;
  /** 导入源类型 */
  importSource?: 'DINGDOC' | 'YUQUE' | 'WEB' | 'UPLOAD';
  /** 文档类型 */
  docType?: 'TEXT' | 'SHEET' | 'PDF' | 'MARKDOWN';
  /** 文档处理状态 */
  status?: 'PROCESSING' | 'ACTIVE' | 'ERROR' | 'REMOVED' | 'DISABLED';
  /** 数据分片配置 */
  chunkConfig?: DataChunkConfig;
  /** 知识库数据预处理配置 */
  preprocessConfig?: DataPreprocessConfig;
  /** 文档评分 */
  score?: DocumentScoreVO;
}

export interface AimiResultTableDocumentVO {
  success?: boolean;
  /** 表格文档 */
  data?: TableDocumentVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

/** 表格文档 */
export interface TableDocumentVO {
  docName?: string;
  sheets?: TableDocumentSheetVO[];
}

export interface AimiResultDocumentSuggestionVO {
  success?: boolean;
  /** 文档优化建议 */
  data?: DocumentSuggestionVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

/** 文档优化建议 */
export interface DocumentSuggestionVO {
  /** 文档内容，markdown格式，评审部分通过<comment>标签包裹，评审内容在content属性中，会encode */
  content?: string;
  /** 优化建议，markdown格式 */
  suggestion?: string;
}

/** 文档列表请求 */
export interface DocumentPageRequest {
  /**
   * 页码，从0开始
   * @format int32
   * @default 0
   */
  pageNo?: number;
  /**
   * 每页大小，默认10
   * @format int32
   * @default 10
   */
  pageSize?: number;
  /**
   * 知识库id
   * @format int64
   */
  knowledgeBaseId?: number;
  /** 文档标题 */
  title?: string;
}

export interface AimiResultPageDocumentVO {
  success?: boolean;
  data?: PageDocumentVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface PageDocumentVO {
  items?: DocumentVO[];
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int64 */
  totalCount?: number;
  /** @format int32 */
  pages?: number;
}

export interface AimiResultDashboardStatisticsVO {
  success?: boolean;
  /** 仪表盘统计信息 */
  data?: DashboardStatisticsVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

/** 仪表盘统计信息 */
export interface DashboardStatisticsVO {
  /** @format int64 */
  knowledgeBaseCount?: number;
  /** @format int64 */
  mcpToolCount?: number;
  /** @format int64 */
  sdkCount?: number;
  /** @format int64 */
  codeFileCount?: number;
}

/** 分片列表请求 */
export interface ChunkListRequest {
  /**
   * 页码，从0开始
   * @format int32
   * @default 0
   */
  pageNo?: number;
  /**
   * 每页大小，默认10
   * @format int32
   * @default 10
   */
  pageSize?: number;
  /**
   * 文档ID
   * @format int64
   */
  documentId?: number;
  /** 搜索关键词 */
  keyword?: string;
}

export interface AimiResultPageChunkVO {
  success?: boolean;
  data?: PageChunkVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

/** 文档分片 */
export interface ChunkVO {
  /**
   * Chunk id
   * @format int64
   */
  id?: number;
  /** 唯一标识符 */
  identifier?: string;
  /** 分片id */
  splitId?: string;
  /** chunk内容 */
  content?: string;
  /** 文档分片状态 */
  status?: 'ACTIVE' | 'REMOVED';
  /** 修改人 */
  modifier?: string;
  /**
   * 修改时间
   * @format date-time
   */
  gmtModified?: string;
}

export interface PageChunkVO {
  items?: ChunkVO[];
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int64 */
  totalCount?: number;
  /** @format int32 */
  pages?: number;
}

export interface AimiResultPageTableChunkVO {
  success?: boolean;
  data?: PageTableChunkVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface PageTableChunkVO {
  items?: TableChunkVO[];
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int64 */
  totalCount?: number;
  /** @format int32 */
  pages?: number;
}

/** 文档分片 */
export interface TableChunkVO {
  /**
   * Chunk id
   * @format int64
   */
  id?: number;
  /** 唯一标识符 */
  identifier?: string;
  /** 列名 */
  headers?: string[];
  /** 单元格内容 */
  cells?: string[];
}

export interface AimiResultAgentVO {
  success?: boolean;
  data?: AgentVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AimiResultListAgentInstanceVO {
  success?: boolean;
  data?: AgentInstanceVO[];
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AimiResultString {
  success?: boolean;
  data?: string;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AgentRerankModelVO {
  key?: string;
  displayName?: string;
}

export interface AimiResultListAgentRerankModelVO {
  success?: boolean;
  data?: AgentRerankModelVO[];
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AgentPageRequest {
  /**
   * 页码，从0开始
   * @format int32
   * @default 0
   */
  pageNo?: number;
  /**
   * 每页大小，默认10
   * @format int32
   * @default 10
   */
  pageSize?: number;
  operator?: string;
  /** @format date-time */
  time?: string;
  /** 只展示我的 */
  onlyShowMine?: boolean;
  /** 状态 */
  status?: 'ONLINE' | 'DRAFT' | 'OFFLINE';
  /** 名称 */
  name?: string;
}

export interface AimiResultPageAgentVO {
  success?: boolean;
  data?: PageAgentVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface PageAgentVO {
  items?: AgentVO[];
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int64 */
  totalCount?: number;
  /** @format int32 */
  pages?: number;
}

export interface AimiResultListLlmModelVO {
  success?: boolean;
  data?: LlmModelVO[];
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface LlmModelParamVO {
  key?: string;
  displayName?: string;
  description?: string;
  type?: string;
  required?: boolean;
  /** @format double */
  maxValue?: number;
  /** @format double */
  minValue?: number;
  /** @format double */
  stepValue?: number;
  /** @format double */
  defaultValue?: number;
  regex?: string;
  checkFailMsg?: string;
}

export interface LlmModelVO {
  key?: string;
  displayName?: string;
  logo?: string;
  params?: LlmModelParamVO[];
}

export interface AgentListRequest {
  operator?: string;
  /** @format date-time */
  time?: string;
  /** 只展示我的 */
  onlyShowMine?: boolean;
  /** 状态 */
  status?: 'ONLINE' | 'DRAFT' | 'OFFLINE';
  /** 名称 */
  name?: string;
}

export interface AimiResultListAgentVO {
  success?: boolean;
  data?: AgentVO[];
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AgentConfigVO {
  /** 大模型配置 */
  llmConfig?: AgentLlmConfig;
  /** 知识库召回配置 */
  dataSearchConfig?: DataSearchConfig;
}

export interface AimiResultAgentConfigVO {
  success?: boolean;
  data?: AgentConfigVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AgentConversationSampleVO {
  /** @format int64 */
  id?: number;
  agentIdentifier?: string;
  question?: string;
  description?: string;
  tag?: string;
  /** @format int32 */
  rankNum?: number;
  sampleUrl?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  creator?: string;
  modifier?: string;
}

export interface AimiResultListAgentConversationSampleVO {
  success?: boolean;
  data?: AgentConversationSampleVO[];
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AgentConversationSampleGroupVO {
  tag?: string;
  items?: AgentConversationSampleVO[];
}

export interface AimiResultListAgentConversationSampleGroupVO {
  success?: boolean;
  data?: AgentConversationSampleGroupVO[];
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AgentAccessTokenVO {
  token?: string;
  type?: string;
  /** @format date-time */
  issuedAt?: string;
  /** @format date-time */
  expiredAt?: string;
}

export interface AimiResultAgentAccessTokenVO {
  success?: boolean;
  data?: AgentAccessTokenVO;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface AccessToken {
  tenantId?: string;
  token?: string;
  type?: string;
  identity?: Identity;
  authorities?: GrantedAuthority[];
  /** @format date-time */
  issuedAt?: string;
  /** @format date-time */
  expiredAt?: string;
  expired?: boolean;
}

export interface AimiResultAccessToken {
  success?: boolean;
  data?: AccessToken;
  errorCode?: string;
  errorMsg?: string;
  ip?: string;
}

export interface GrantedAuthority {
  authority?: string;
}

export interface Identity {
  identityType?: 'BUC_USER' | 'LLM_AGENT';
  identifier?: string;
}
