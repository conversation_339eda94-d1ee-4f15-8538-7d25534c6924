/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PaginationResult } from './Base';
import {
  MessageNotifyQuery,
  MessageNotifyReq,
  MessageNotifyRes,
  MessageNotifyTemplateQuery,
  MessageNotifyTemplateReq,
  MessageNotifyTemplateRes,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface DeleteTemplateByIdParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags MessageNotify
 * @name DeleteTemplateById
 * @summary deleteTemplateById
 * @request GET:/api/v1/notify/deleteTemplateById
 */
export async function deleteTemplateById(query: DeleteTemplateByIdParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/notify/deleteTemplateById`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FindMsgNotifyByIdParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags MessageNotify
 * @name FindMsgNotifyById
 * @summary findMsgNotifyById
 * @request POST:/api/v1/notify/findMsgNotifyById
 */
export async function findMsgNotifyById(
  query: FindMsgNotifyByIdParams,
  options?: MethodOptions,
): Promise<MessageNotifyRes> {
  return request(`${baseUrl}/api/v1/notify/findMsgNotifyById`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags MessageNotify
 * @name FindMsgNotifyByQuery
 * @summary findMsgNotifyByQuery
 * @request POST:/api/v1/notify/findMsgNotifyByQuery
 */
export async function findMsgNotifyByQuery(
  data: MessageNotifyQuery,
  options?: MethodOptions,
): Promise<PaginationResult<MessageNotifyRes>> {
  return request(`${baseUrl}/api/v1/notify/findMsgNotifyByQuery`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags MessageNotify
 * @name FindMsgNotifyTempByQuery
 * @summary findMsgNotifyTempByQuery
 * @request POST:/api/v1/notify/findMsgNotifyTempByQuery
 */
export async function findMsgNotifyTempByQuery(
  data: MessageNotifyTemplateQuery,
  options?: MethodOptions,
): Promise<PaginationResult<MessageNotifyTemplateRes>> {
  return request(`${baseUrl}/api/v1/notify/findMsgNotifyTempByQuery`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags MessageNotify
 * @name SaveAndSendMessageNotify
 * @summary saveAndSendMessageNotify
 * @request POST:/api/v1/notify/saveAndSendMessageNotify
 */
export async function saveAndSendMessageNotify(data: MessageNotifyReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/notify/saveAndSendMessageNotify`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags MessageNotify
 * @name SaveMessageNotify
 * @summary saveMessageNotify
 * @request POST:/api/v1/notify/saveMessageNotify
 */
export async function saveMessageNotify(data: MessageNotifyReq, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/notify/saveMessageNotify`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags MessageNotify
 * @name SaveMessageNotifyTemplate
 * @summary saveMessageNotifyTemplate
 * @request POST:/api/v1/notify/saveMessageNotifyTemplate
 */
export async function saveMessageNotifyTemplate(
  data: MessageNotifyTemplateReq,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/api/v1/notify/saveMessageNotifyTemplate`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
