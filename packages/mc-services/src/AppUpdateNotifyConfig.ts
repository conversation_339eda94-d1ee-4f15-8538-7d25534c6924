/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  AppPopLayerTypeConfig,
  AppPopupTypeConfig,
  AppUpdateNoticeConfig,
  FatigueControlConfig,
  PopLayerTemplateBO,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface DeleteAppUpdateNoticeConfigParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name DeleteAppUpdateNoticeConfig
 * @summary 删除app更新通知配置
 * @request DELETE:/api/v1/appUpdateNoticeConfig
 */
export async function deleteAppUpdateNoticeConfig(
  query: DeleteAppUpdateNoticeConfigParams,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig`, {
    method: 'DELETE',
    params: query,
    ...options,
  });
}

export interface GetConfigsByApplicationIdParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
}
/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name GetConfigsByApplicationId
 * @summary getConfigsByApplicationId
 * @request GET:/api/v1/appUpdateNoticeConfig/all
 */
export async function getConfigsByApplicationId(
  query: GetConfigsByApplicationIdParams,
  options?: MethodOptions,
): Promise<Record<string, AppUpdateNoticeConfig[]>> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig/all`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name CreateFatigueConfig
 * @summary 创建疲劳度配置
 * @request POST:/api/v1/appUpdateNoticeConfig/createFatigueConfig
 */
export async function createFatigueConfig(data: FatigueControlConfig, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig/createFatigueConfig`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface DelFatigueConfigParams {
  /**
   * appId
   * @format int64
   */
  appId: number;
  /** releaseOrigin */
  releaseOrigin: string;
}
/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name DelFatigueConfig
 * @summary 删除疲劳度配置
 * @request GET:/api/v1/appUpdateNoticeConfig/delFatigueConfig
 */
export async function delFatigueConfig(query: DelFatigueConfigParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig/delFatigueConfig`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface FindFatigueConfigParams {
  /**
   * appId
   * @format int64
   */
  appId: number;
}
/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name FindFatigueConfig
 * @summary 查找应用下所有配置
 * @request GET:/api/v1/appUpdateNoticeConfig/findFatigueConfig
 */
export async function findFatigueConfig(
  query: FindFatigueConfigParams,
  options?: MethodOptions,
): Promise<FatigueControlConfig[]> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig/findFatigueConfig`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name UpdatePopLayerConfig
 * @summary 修改poplayer配置
 * @request PUT:/api/v1/appUpdateNoticeConfig/poplayer
 */
export async function updatePopLayerConfig(data: AppPopLayerTypeConfig, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig/poplayer`, {
    method: 'PUT',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name CreatePopLayerConfig
 * @summary 创建poplayer配置
 * @request POST:/api/v1/appUpdateNoticeConfig/poplayer
 */
export async function createPopLayerConfig(data: AppPopLayerTypeConfig, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig/poplayer`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface GetTemplatesParams {
  /**
   * applicationId
   * @format int64
   */
  applicationId: number;
}
/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name GetTemplates
 * @summary getTemplates
 * @request GET:/api/v1/appUpdateNoticeConfig/poplayerTempalte/list
 */
export async function getTemplates(query: GetTemplatesParams, options?: MethodOptions): Promise<PopLayerTemplateBO[]> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig/poplayerTempalte/list`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name UpdatePopupConfig
 * @summary 修改popup配置
 * @request PUT:/api/v1/appUpdateNoticeConfig/popup
 */
export async function updatePopupConfig(data: AppPopupTypeConfig, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig/popup`, {
    method: 'PUT',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags AppUpdateNotifyConfig
 * @name CreatePopupConfig
 * @summary 创建popup配置
 * @request POST:/api/v1/appUpdateNoticeConfig/popup
 */
export async function createPopupConfig(data: AppPopupTypeConfig, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/appUpdateNoticeConfig/popup`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
