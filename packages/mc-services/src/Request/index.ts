import { stringify } from 'qs';
import Cookies from 'js-cookie';
import omit from 'lodash/omit';

const injectionFunctions: Record<string, any> = {};

export function injectRequest({
  checkStatus: _checkStatus,
  checkResBodyCode: _checkResBodyCode,
  dealWithRequestError: _dealWithRequestError,
}: {
  checkStatus?: (response: Response) => Response;
  checkResBodyCode?: (resBody: any, uniformError: boolean, uniform403: boolean) => void;
  dealWithRequestError?: (error: any, uniform403: any, toErrorPage: any) => void;
}) {
  injectionFunctions.checkStatus = _checkStatus;
  injectionFunctions.checkResBodyCode = _checkResBodyCode;
  injectionFunctions.dealWithRequestError = _dealWithRequestError;
}

function checkStatus(response: Response): Response {
  if (injectionFunctions.checkStatus) {
    return injectionFunctions.checkStatus(response);
  }

  return response;
}

function checkResBodyCode(resBody: any, uniformError: boolean, uniform403: boolean): any {
  if (injectionFunctions.checkResBodyCode) {
    return injectionFunctions.checkResBodyCode(resBody, uniformError, uniform403);
  }

  if (!uniformError) {
    return resBody;
  }

  if (resBody && resBody.success) {
    return resBody.data;
  }
}

function dealWithRequestError(error: any, uniform403: any, toErrorPage: any): void {
  if (injectionFunctions.dealWithRequestError) {
    injectionFunctions.dealWithRequestError(error, uniform403, toErrorPage);
  }
}

export type MethodOptions = {
  useOpenSignature?: boolean;

  /**
   * 是否统一处理错误，默认为true
   */
  uniformError?: boolean;

  /**
   * 是否在发生错误时跳转到错误页面，默认为false
   */
  toErrorPage?: boolean;

  /**
   * 是否统一处理403错误，默认为true
   */
  uniform403?: boolean;

  /**
   * 是否添加URL后缀，默认为true，将添加'.json'后缀
   */
  suffix?: boolean;

  headers?: RequestInit['headers'];
};

type RequestOptions = RequestInit & {
  data?: Record<string, any>;
  params?: Record<string, any>;
} & MethodOptions;

/**
 * 发起网络请求的通用函数
 *
 * 该函数封装了fetch API，提供了统一的错误处理、URL格式化、请求参数处理等功能
 * 它允许通过不同的选项来定制请求，如是否使用错误页面跳转，是否处理403错误等
 *
 * @param url 请求的URL基础路径
 * @param option 请求的配置选项，包括方法、头信息、参数等
 * @returns 返回一个Promise，解析为请求的响应数据
 */
export async function request(url: string, options?: RequestOptions): Promise<any> {
  const { uniformError = true, toErrorPage = false, uniform403 = true, suffix = true } = options;

  let newUrl = url;
  let useSuffix = suffix;

  const defaultOptions: RequestInit = {
    mode: 'cors',
    credentials: 'include',
    method: 'GET',
  };
  const newOptions: RequestOptions = {
    ...defaultOptions,
    ...omit(options, ['useOpenSignature', 'uniformError', 'toErrorPage', 'uniform403', 'suffix']),
  };

  // 走摩天轮 OpenAPI 调用
  if (options.useOpenSignature && typeof globalThis.genMTLSignature === 'function') {
    // OpenAPI 调用，需要强制不增加 .json 的后缀
    useSuffix = false;

    // OpenAPI 调用域名收敛，需要做一下替换
    if (newUrl.startsWith('https://mc.')) {
      newUrl = newUrl.replace('https://mc.alibaba-inc.com', 'https://open.mtl4.alibaba-inc.com');
    } else if (newUrl.startsWith('https://pre-mc.')) {
      newUrl = newUrl.replace('https://pre-mc.alibaba-inc.com', 'https://pre-open.mtl4.alibaba-inc.com');
    }

    const { pathname: api } = new URL(newUrl);
    const { identifier, timestamp, signature, client, workId } = await globalThis.genMTLSignature(api);

    newOptions.headers = newOptions.headers || {};
    newOptions.headers = {
      ...newOptions.headers,
      'x-mtl4-api-consumer-identifier': identifier,
      'x-mtl4-api-timestamp': timestamp,
      'x-mtl4-api-signature': signature,
      'x-mtl4-api-client': client,
      'x-mtl4-api-user': workId,
    };
    // delete newOptions.headers[
  } else if (typeof globalThis.getOneTimeSSOTicket === 'function') {
    const SSO_TICKET = await globalThis.getOneTimeSSOTicket();
    newOptions.params = {
      ...(options.params || {}),
      SSO_TICKET,
    };
  }

  if (useSuffix) {
    newUrl = `${newUrl}.json`;
  }

  // newOptions.params = dealObjectValue(newOptions.params);

  if (newOptions.method === 'GET') {
    newUrl = `${newUrl}?${stringify({ ...newOptions.data, ...newOptions.params }, { indices: false })}`;
  } else {
    newUrl = `${newUrl}?${stringify({ ...newOptions.params }, { indices: false })}`;
  }

  if (
    newOptions.method === 'POST' ||
    newOptions.method === 'PUT' ||
    newOptions.method === 'DELETE' ||
    newOptions.method === 'PATCH'
  ) {
    // newUrl = `${newUrl}?${stringify({ ...newOptions.data, ...newOptions.params })}`;

    if (newOptions.body instanceof FormData) {
      newOptions.headers = {
        Accept: 'application/json',
        ...newOptions.headers,
      };
    } else {
      newOptions.headers = {
        Accept: 'application/json',
        'Content-Type': 'application/json; charset=utf-8',
        ...newOptions.headers,
      };
      newOptions.body = JSON.stringify(newOptions.body);
    }
  }

  newOptions.headers = {
    ...newOptions.headers,
    'X-XSRF-TOKEN': Cookies.get('XSRF-TOKEN'),
    // 'Content-Type': 'application/json; charset=utf-8',
  };

  newOptions.redirect = 'manual';
  // console.log(newOptions);
  return fetch(newUrl, newOptions)
    .then(checkStatus)
    .then((response) => response.json())
    .then((resBody) => checkResBodyCode(resBody, uniformError, uniform403))
    .catch((err) => {
      dealWithRequestError(err, uniform403, toErrorPage);
    });
}

export async function requestOpenAPI(
  urlPrefix: string,
  url: string,
  options: RequestInit & { data?: Record<string, any>; params?: Record<string, any> },
  uniformError = true,
  toErrorPage = false,
  uniform403 = true,
) {
  const newOptions: RequestInit = {
    credentials: 'include',
    method: 'GET',
    ...options,
    headers: {
      Accept: 'application/json',
      ...(options?.headers || {}),
      'X-XSRF-TOKEN': Cookies.get('XSRF-TOKEN'),
      'Content-Type': 'application/json; charset=utf-8',
    },
  };

  if (newOptions.method === 'POST') {
    newOptions.body = JSON.stringify(newOptions.body);
  }

  let newUrl = url;
  if (options.params) {
    newUrl = `${newUrl}?${stringify({ ...options.params }, { indices: false })}`;
  }

  return fetch(`${urlPrefix}${newUrl}`, newOptions)
    .then(checkStatus)
    .then((response) => response.json())
    .then((resBody) => {
      if (!resBody.error && !resBody.errorCode) {
        return resBody.data;
      }
      if (uniformError) {
        return resBody;
      }
      const error = new Error(resBody.error) as any;
      error.status = -Math.abs(resBody.errorNo) || 500;
      error.errorCode = resBody.errorCode;
      error.response = {
        content: resBody.data,
      };
      throw error;
    })
    .catch((err) => {
      dealWithRequestError(err, uniform403, toErrorPage);
    });
}

export async function requestWormholeAPI(
  url: string,
  options = {},
  uniformError = true,
  toErrorPage = false,
  uniform403 = true,
) {
  const urlPrefix = '/dev/api/wormhole';
  return requestOpenAPI(urlPrefix, url, options, uniformError, toErrorPage, uniform403);
}

export async function requestO2API(
  url: string,
  options = {},
  uniformError = true,
  toErrorPage = false,
  uniform403 = true,
) {
  const urlPrefix = '/dev/api/o2/v1.0/work';
  return requestOpenAPI(urlPrefix, url, options, uniformError, toErrorPage, uniform403);
}
