{
  "compilerOptions": {
    "allowSyntheticDefaultImports": true,
    "module": "ESNext",
    "target": "ESNext",
    "jsx": "react",
    "moduleResolution": "node",
    "lib": ["ES2017", "DOM", "DOM.Iterable"],
    "allowJs": true,
    "noUnusedLocals": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitAny": true,
    "skipLibCheck": true,
  },
  "include": ["src", "../mc-core/src/utils/authority.ts"],
}
