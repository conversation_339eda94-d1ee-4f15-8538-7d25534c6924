import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function TriangleRightOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-triangle-right-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_05070">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_05070)">
          <path
            d="M6.42678,4.426750213238526L9.82322,7.823225913238526C9.92085,7.920855913238525,9.92085,8.079145913238525,9.82322,8.176775913238526L6.42678,11.573195913238525C6.26928,11.730695913238526,6,11.619195913238524,6,11.396495913238525L6,4.603529913238526C6,4.380799813238525,6.26929,4.269259913238526,6.42678,4.426750213238526Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
