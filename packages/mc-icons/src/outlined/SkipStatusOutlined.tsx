import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function SkipStatusOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-skip-status-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_4255_62433">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_4255_62433)">
          <path
            d="M3,13.5L3,9.5C3,8.671573,3.671573,8,4.5,8L11.5,8C12.32843,8,13,8.671573,13,9.5L13,13.5C13,14.32843,12.32843,15,11.5,15L4.5,15C3.671573,15,3,14.32843,3,13.5ZM4.5,13.5L11.5,13.5L11.5,9.5L4.5,9.5L4.5,13.5Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M13.999980712890626,4.08477L13.999980712890626,2.75C13.999980712890626,2.3357900000000003,14.335780712890624,2,14.749980712890626,2C15.164180712890625,2,15.499980712890626,2.3357900000000003,15.499980712890626,2.75L15.499980712890626,6.25C15.499980712890626,6.66421,15.164180712890625,7,14.749980712890626,7L11.249980712890626,7C10.835790712890624,7,10.500000712890625,6.66421,10.500000712890625,6.25C10.500000712890625,5.83579,10.835790712890624,5.5,11.249980712890626,5.5L13.182780712890626,5.5Q12.478980712890625,4.4505300000000005,11.382180712890625,3.74323Q9.842080712890626,2.75,8.000000712890625,2.75Q6.157920712890625,2.75,4.6177807128906245,3.74323Q3.116270712890625,4.711539999999999,2.351390712890625,6.32116C2.227130712890625,6.58264,1.963480712890625,6.74926,1.673980712890625,6.74926C1.259767712890625,6.74926,0.923980712890625,6.41348,0.923980712890625,5.99926C0.923980712890625,5.88791,0.948778112890625,5.77795,0.996573412890625,5.67737Q1.944980712890625,3.68203,3.804830712890625,2.48263Q5.716200712890625,1.25,8.000000712890625,1.25Q10.283800712890624,1.25,12.195180712890625,2.48263Q13.241980712890625,3.15773,13.999980712890626,4.08477Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
