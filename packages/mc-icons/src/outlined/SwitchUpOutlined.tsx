import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function SwitchUpOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-switch-up-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_04672">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_04672)">
          <path
            strokeOpacity={1}
            stroke="currentColor"
            fill="none"
            strokeWidth={1.25}
            d="M1.625 1.625H14.375V14.375H1.625z"
          />
          <path
            d="M20.780272820281983,17.03033C20.920972820281982,16.88968,20.999972820281982,16.698909999999998,20.999972820281982,16.5C20.999972820281982,16.30109,20.920972820281982,16.11032,20.780272820281983,15.96967L17.53030282028198,12.719667C17.237412820281982,12.4267775,16.762532820281983,12.4267775,16.469642820281983,12.719667C16.176752820281983,13.012557,16.176752820281983,13.487438000000001,16.469642820281983,13.78033L18.439312820281984,15.75L12.749972820281982,15.75C12.335762820281982,15.75,11.999972820281982,16.08579,11.999972820281982,16.5C11.999972820281982,16.91421,12.335762820281982,17.25,12.749972820281982,17.25L18.439312820281984,17.25L16.469642820281983,19.2197C16.176752820281983,19.5126,16.176752820281983,19.9874,16.469642820281983,20.2803C16.762532820281983,20.5732,17.237412820281982,20.5732,17.53030282028198,20.2803L20.780272820281983,17.03033Z"
            fillRule="evenodd"
            fill="currentColor"
            transform="matrix(1,0,0,-1,0,25) matrix(0,1,-1,0,24.499972820281982,0.5000271797180176)"
          />
        </g>
      </svg>
    </span>
  );
}
