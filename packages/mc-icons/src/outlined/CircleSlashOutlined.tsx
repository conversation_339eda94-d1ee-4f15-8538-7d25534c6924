import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function CircleSlashOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-circle-slash-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_05141">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_05141)">
          <path
            d="M1.5,8C1.49999,6.77839,1.84424,5.5815,2.49327,4.54656C3.14231,3.51162,4.06985,2.68053,5.16957,2.14858C6.26928,1.61662,7.49664,1.40534,8.71092,1.53895C9.92521,1.67256,11.0773,2.14566,12.035,2.904L2.904,12.035C1.99276,10.8877,1.49778,9.46515,1.5,8ZM3.965,13.096C5.21468,14.0856,6.78443,14.5815,8.37581,14.4893C9.96719,14.3972,11.4692,13.7236,12.5964,12.5964C13.7236,11.4692,14.3972,9.96719,14.4893,8.37581C14.5815,6.78443,14.0856,5.21468,13.096,3.965L3.965,13.096ZM8,0C5.87827,0,3.84344,0.842855,2.34315,2.34315C0.842855,3.84344,0,5.87827,0,8C0,10.1217,0.842855,12.1566,2.34315,13.6569C3.84344,15.1571,5.87827,16,8,16C10.1217,16,12.1566,15.1571,13.6569,13.6569C15.1571,12.1566,16,10.1217,16,8C16,5.87827,15.1571,3.84344,13.6569,2.34315C12.1566,0.842855,10.1217,0,8,0Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
