import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function DependencySourceOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-dependency-source-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_2690_07553">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_2690_07553)">
          <path
            d="M0,9.25L0,2.75C0,1.783502,0.783502,1,1.75,1L10.25,1C11.2165,1,12,1.783502,12,2.75L12,5.75C12,6.16421,11.6642,6.5,11.25,6.5C10.8358,6.5,10.5,6.16421,10.5,5.75L10.5,2.75Q10.5,2.5,10.25,2.5L1.75,2.5Q1.64645,2.5,1.57324,2.57324Q1.50002,2.64647,1.5,2.75L1.5,9.25Q1.5,9.35355,1.57324,9.42676Q1.64647,9.49998,1.75,9.5L2.25,9.5C2.66421,9.5,3,9.83579,3,10.25C3,10.66421,2.66421,11,2.25,11L1.75,11C0.783502,11,0,10.2165,0,9.25Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M4,10.25L4,13.25C4,14.2165,4.783502,15,5.75,15L14.25,15C15.2165,15,16,14.2165,16,13.25L16,6.75C16,5.783502,15.2165,5,14.25,5L13.75,5C13.33579,5,13,5.335786,13,5.75C13,6.16421,13.33579,6.5,13.75,6.5L14.25,6.5Q14.5,6.5,14.5,6.75L14.5,13.25Q14.5,13.5,14.25,13.5L5.75,13.5Q5.64645,13.5,5.57324,13.42676Q5.50002,13.35353,5.5,13.25L5.5,10.25C5.5,9.83579,5.16421,9.5,4.75,9.5C4.335786,9.5,4,9.83579,4,10.25"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M6.0304839999999995,4.969824L11.03033,9.96967Q11.135819999999999,10.07516,11.192910000000001,10.21299Q11.25,10.350819999999999,11.25,10.5Q11.25,10.57387,11.23559,10.64632Q11.22118,10.71877,11.192910000000001,10.78701Q11.16464,10.855260000000001,11.1236,10.91668Q11.08256,10.978100000000001,11.03033,11.03033Q10.978100000000001,11.08256,10.91668,11.1236Q10.855260000000001,11.16464,10.78701,11.192910000000001Q10.71877,11.22118,10.64632,11.23559Q10.57387,11.25,10.5,11.25Q10.350819999999999,11.25,10.21299,11.192910000000001Q10.07516,11.135819999999999,9.96967,11.03033L4.969824,6.0304839999999995L4.96967,6.03033Q4.864181,5.924841,4.80709,5.787013Q4.75,5.649184,4.75,5.5Q4.75,5.4261315,4.764411,5.353682Q4.778822,5.281233,4.80709,5.212987Q4.835359,5.144742,4.876398,5.083322Q4.917437,5.021903,4.96967,4.96967Q5.021903,4.917437,5.083322,4.876398Q5.144742,4.835359,5.212987,4.80709Q5.281233,4.778822,5.353682,4.764411Q5.4261315,4.75,5.5,4.75Q5.649184,4.75,5.787013,4.80709Q5.924841,4.864181,6.03033,4.96967L6.0304839999999995,4.969824Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
