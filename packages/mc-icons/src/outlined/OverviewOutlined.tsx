import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function OverviewOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-overview-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_3284_05921">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_3284_05921)">
          <path
            d="M2.5,15Q1.88125,15,1.440625,14.5594Q1,14.1187,1,13.5L1,2.5Q1,1.88125,1.440625,1.4406240000000001Q1.88125,1,2.5,1L13.5,1Q14.1187,1,14.5594,1.4406240000000001Q15,1.88125,15,2.5L15,13.5Q15,14.1187,14.5594,14.5594Q14.1187,15,13.5,15L2.5,15ZM6.5,13.5L6.5,8.5L2.5,8.5L2.5,13.5L6.5,13.5ZM8,13.5L13.5,13.5L13.5,8.5L8,8.5L8,13.5ZM2.5,7L13.5,7L13.5,2.5L2.5,2.5L2.5,7Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
