import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function XCircleOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-x-circle-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_04870">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_04870)">
          <path
            d="M6.030332861022949,4.9696698610229495C5.737439861022949,4.676780261022949,5.262559861022949,4.676780261022949,4.9696698610229495,4.9696698610229495C4.676780261022949,5.262559861022949,4.676780261022949,5.737439861022949,4.9696698610229495,6.030332861022949L6.939342861022949,8.00000286102295L4.9696698610229495,9.96967286102295C4.676780261022949,10.262602861022948,4.676780261022949,10.73740286102295,4.9696698610229495,11.03030286102295C5.262559861022949,11.32320286102295,5.737439861022949,11.32320286102295,6.030332861022949,11.03030286102295L8.00000286102295,9.06066286102295L9.96967286102295,11.03030286102295C10.262602861022948,11.32320286102295,10.73740286102295,11.32320286102295,11.03030286102295,11.03030286102295C11.32320286102295,10.73740286102295,11.32320286102295,10.262602861022948,11.03030286102295,9.96967286102295L9.06066286102295,8.00000286102295L11.03030286102295,6.030342861022949C11.32320286102295,5.737439861022949,11.32320286102295,5.262569861022949,11.03030286102295,4.969679861022949C10.73740286102295,4.676780261022949,10.262602861022948,4.676780261022949,9.96967286102295,4.969679861022949L8.00000286102295,6.939342861022949L6.030332861022949,4.9696698610229495Z"
            fill="currentColor"
          />
          <path
            d="M2.34315,2.34315C-0.781049,5.46734,-0.781049,10.5327,2.34315,13.6569C5.46734,16.781,10.5327,16.781,13.6569,13.6569C16.781,10.5327,16.781,5.46734,13.6569,2.34315C10.5327,-0.781049,5.46734,-0.781049,2.34315,2.34315ZM3.40381,12.5962C0.865398,10.0578,0.865398,5.94221,3.40381,3.40381C5.94221,0.865398,10.0578,0.865398,12.5962,3.40381C15.1346,5.94221,15.1346,10.0578,12.5962,12.5962C10.0578,15.1346,5.94221,15.1346,3.40381,12.5962Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
