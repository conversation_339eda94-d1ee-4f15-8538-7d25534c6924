import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function CleanOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-clean-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_5191_019729">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_5191_019729)">
          <path
            d="M7.261659985694886,7.27273L8.716199985694885,7.27273L8.716199985694885,2.18182Q8.716199985694885,1.87273,8.507109985694886,1.66364Q8.298019985694886,1.45455,7.988929985694885,1.45455Q7.679839985694885,1.45455,7.470749985694885,1.66364Q7.261659985694886,1.87273,7.261659985694886,2.18182L7.261659985694886,7.27273ZM2.8980199856948854,10.1818L13.079799985694885,10.1818L13.079799985694885,8.72727L2.8980199856948854,8.72727L2.8980199856948854,10.1818ZM1.8434799856948854,14.5455L3.6252899856948853,14.5455L3.6252899856948853,13.0909Q3.6252899856948853,12.7818,3.834389985694885,12.5727Q4.043479985694885,12.3636,4.352569985694885,12.3636Q4.661659985694885,12.3636,4.870749985694885,12.5727Q5.0798399856948855,12.7818,5.0798399856948855,13.0909L5.0798399856948855,14.5455L7.261659985694886,14.5455L7.261659985694886,13.0909Q7.261659985694886,12.7818,7.470749985694885,12.5727Q7.679839985694885,12.3636,7.988929985694885,12.3636Q8.298019985694886,12.3636,8.507109985694886,12.5727Q8.716199985694885,12.7818,8.716199985694885,13.0909L8.716199985694885,14.5455L10.897999985694884,14.5455L10.897999985694884,13.0909Q10.897999985694884,12.7818,11.107099985694886,12.5727Q11.316199985694885,12.3636,11.625299985694886,12.3636Q11.934399985694885,12.3636,12.143499985694886,12.5727Q12.352599985694885,12.7818,12.352599985694885,13.0909L12.352599985694885,14.5455L14.134399985694886,14.5455L13.407099985694884,11.6364L2.5707499856948854,11.6364L1.8434799856948854,14.5455ZM14.134399985694886,16L1.8434799856948854,16Q1.134384985694885,16,0.6980209856948852,15.4364Q0.26165798569488524,14.8727,0.4434758856948853,14.1818L1.4434799856948852,10.1818L1.4434799856948852,8.72727Q1.4434799856948852,8.12727,1.8707499856948853,7.7Q2.2980199856948853,7.27273,2.8980199856948854,7.27273L5.807109985694885,7.27273L5.807109985694885,2.18182Q5.807109985694885,1.27273,6.443479985694886,0.636364Q7.0798399856948855,0,7.988929985694885,0Q8.898019985694885,0,9.534389985694885,0.636364Q10.170749985694885,1.27273,10.170749985694885,2.18182L10.170749985694885,7.27273L13.079799985694885,7.27273Q13.679799985694885,7.27273,14.107099985694886,7.7Q14.534399985694884,8.12727,14.534399985694884,8.72727L14.534399985694884,10.1818L15.534399985694884,14.1818Q15.770799985694886,14.8727,15.325299985694885,15.4364Q14.879799985694886,16,14.134399985694886,16ZM13.079799985694885,8.72727L2.8980199856948854,8.72727L13.079799985694885,8.72727ZM8.716199985694885,7.27273L7.261659985694886,7.27273L8.716199985694885,7.27273Z"
            fill="currentColor"
            style={{
              mixBlendMode: 'passthrough',
            }}
          />
        </g>
      </svg>
    </span>
  );
}
