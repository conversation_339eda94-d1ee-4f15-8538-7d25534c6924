import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function CloudOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-cloud-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_04757">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_04757)">
          <path
            d="M2,7.25C2,4.3311,4.3311,2,7.25,2C9.37133,2,11.1862,3.22888,12.0169,5.028700000000001C14.2737,5.28123,16,7.17026,16,9.5C16,12.0047,14.0047,14,11.5,14L3.5,14C1.54755,14,0,12.4525,0,10.5C0,9.08932,0.80883,7.88564,2.00109,7.32926C2.00039,7.30268,2,7.27618,2,7.25ZM3.54065,7.73151C3.5947,8.110430000000001,3.35475,8.46927,2.98391,8.56406C2.12376,8.78393,1.5,9.55056,1.5,10.5C1.5,11.624,2.37598,12.5,3.5,12.5L11.5,12.5C13.1763,12.5,14.5,11.1763,14.5,9.5C14.5,7.82373,13.1763,6.5,11.5,6.5C11.1806,6.5,10.8962,6.29766,10.7914,5.99586C10.2844,4.5347,8.90454,3.5,7.25,3.5C5.15953,3.5,3.5,5.15953,3.5,7.25C3.5,7.33576,3.50883,7.45208,3.52005,7.55976C3.52537,7.61084,3.5307,7.6551,3.53466,7.68626L3.5393,7.72168L3.54045,7.7301L3.54065,7.73151Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
