import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function UnlockOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-unlock-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1860_03769">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1860_03769)">
          <path
            d="M5.5,4.000047637729645C5.49991,3.4592476377296446,5.67518,2.932997637729645,5.99953,2.500247637729645C6.32387,2.0675076377296446,6.7798,1.7515976377296447,7.2989,1.5999376377296448C7.818,1.4482776377296447,8.37229,1.4690276377296447,8.878589999999999,1.6590876377296448C9.3849,1.8491476377296447,9.81593,2.1982576377296446,10.107,2.654047637729645C10.2141,2.821667637729645,10.3835,2.9398576377296446,10.5778,2.982607637729645C10.7721,3.025367637729645,10.9754,2.989197637729645,11.143,2.8820476377296447C11.3106,2.774897637729645,11.4288,2.6055576377296448,11.4716,2.4112676377296447C11.5143,2.2169776377296446,11.4781,2.013667637729645,11.371,1.8460476377296449C10.9052,1.1168476377296448,10.2156,0.5583416377296447,9.405470000000001,0.2543166377296448C8.595379999999999,-0.049707262270355226,7.70855,-0.08285526227035522,6.87802,0.15984563772964477C6.04749,0.4025476377296448,5.31806,0.9080116377296448,4.79915,1.6004076377296448C4.28024,2.292797637729645,3.99983,3.134787637729645,4,4.000047637729645L4,6.000047637729645L3.499,6.000047637729645C3.10135,6.000317637729645,2.7200699999999998,6.158467637729645,2.43899,6.439747637729645C2.1579,6.721017637729645,2,7.102397637729645,2,7.500047637729645L2,13.499967637729645C2,13.897767637729645,2.15804,14.279367637729644,2.43934,14.560667637729646C2.72064,14.841967637729645,3.1021799999999997,14.999967637729645,3.5,14.999967637729645L12.5,14.999967637729645C12.8978,14.999967637729645,13.2794,14.841967637729645,13.5607,14.560667637729646C13.842,14.279367637729644,14,13.897767637729645,14,13.499967637729645L14,7.500047637729645C14,7.102227637729645,13.842,6.720697637729645,13.5607,6.439387637729645C13.2794,6.158087637729645,12.8978,6.000047637729645,12.5,6.000047637729645L5.5,6.000047637729645L5.5,4.000047637729645ZM4.75,7.500047637729645L3.5,7.500047637729645L3.5,13.499967637729645L12.5,13.499967637729645L12.5,7.500047637729645L4.75,7.500047637729645Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
