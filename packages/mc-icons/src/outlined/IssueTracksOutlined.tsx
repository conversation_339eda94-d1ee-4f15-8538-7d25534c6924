import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function IssueTracksOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-issue-tracks-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1703_03985">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1703_03985)">
          <path
            d="M1.5,8C1.5,4.41015,4.41015,1.5,8,1.5C11.5899,1.5,14.5,4.41015,14.5,8C14.5,8.41421,14.8358,8.75,15.25,8.75C15.6642,8.75,16,8.41421,16,8C16,3.58172,12.4183,0,8,0C3.58172,0,0,3.58172,0,8C0,12.4183,3.58172,16,8,16C8.41421,16,8.75,15.6642,8.75,15.25C8.75,14.8358,8.41421,14.5,8,14.5C4.41015,14.5,1.5,11.5899,1.5,8Z"
            fill="currentColor"
          />
          <path
            d="M9.5,8Q9.5,8.07369,9.49278,8.14703Q9.48555,8.22036,9.47118,8.29264Q9.4568,8.36491,9.435410000000001,8.43543Q9.41402,8.505939999999999,9.385819999999999,8.57402Q9.35762,8.64211,9.32288,8.7071Q9.28814,8.772079999999999,9.2472,8.833359999999999Q9.20626,8.89463,9.15952,8.95159Q9.11277,9.00855,9.06066,9.06066Q9.00855,9.11277,8.95159,9.15952Q8.89463,9.20626,8.83335,9.2472Q8.772079999999999,9.28814,8.7071,9.32288Q8.64211,9.35762,8.57402,9.385819999999999Q8.505939999999999,9.41402,8.43543,9.435410000000001Q8.36491,9.4568,8.29264,9.47118Q8.22036,9.48555,8.14703,9.49278Q8.07369,9.5,8,9.5Q7.92631,9.5,7.85297,9.49278Q7.7796400000000006,9.48555,7.7073599999999995,9.47118Q7.63509,9.4568,7.56457,9.435410000000001Q7.4940560000000005,9.41402,7.425975,9.385819999999999Q7.357894,9.35762,7.292905,9.32288Q7.2279160000000005,9.28814,7.166645,9.2472Q7.105373,9.20626,7.04841,9.15952Q6.991447,9.11277,6.93934,9.06066Q6.887233,9.00855,6.840484,8.95159Q6.793736,8.89463,6.752796,8.83335Q6.711855,8.772079999999999,6.677118,8.7071Q6.642381,8.64211,6.614181,8.57402Q6.5859806,8.505939999999999,6.5645895,8.43543Q6.5431983,8.36491,6.5288221,8.29264Q6.5144458,8.22036,6.50722291,8.14703Q6.5,8.07369,6.5,8Q6.5,7.92631,6.50722291,7.85297Q6.5144458,7.7796400000000006,6.5288221,7.7073599999999995Q6.5431983,7.63509,6.5645895,7.56457Q6.5859806,7.4940560000000005,6.614181,7.425975Q6.642381,7.357894,6.677118,7.292905Q6.711855,7.2279160000000005,6.752796,7.166645Q6.793736,7.105373,6.840484,7.04841Q6.887233,6.991447,6.93934,6.93934Q6.991447,6.887233,7.04841,6.840484Q7.105373,6.793736,7.166645,6.752796Q7.2279160000000005,6.711855,7.292905,6.677118Q7.357894,6.642381,7.425975,6.614181Q7.4940560000000005,6.5859806,7.56457,6.5645895Q7.63509,6.5431983,7.7073599999999995,6.5288221Q7.7796400000000006,6.5144458,7.85297,6.50722291Q7.92631,6.5,8,6.5Q8.07369,6.5,8.14703,6.50722291Q8.22036,6.5144458,8.29264,6.5288221Q8.36491,6.5431983,8.43543,6.5645895Q8.505939999999999,6.5859806,8.57402,6.614181Q8.64211,6.642381,8.7071,6.677118Q8.772079999999999,6.711855,8.833359999999999,6.752796Q8.89463,6.793736,8.95159,6.840484Q9.00855,6.887233,9.06066,6.93934Q9.11277,6.991447,9.15952,7.04841Q9.20626,7.105373,9.2472,7.166645Q9.28814,7.2279160000000005,9.32288,7.292905Q9.35762,7.357894,9.385819999999999,7.425975Q9.41402,7.4940560000000005,9.435410000000001,7.56457Q9.4568,7.63509,9.47118,7.7073599999999995Q9.48555,7.7796400000000006,9.49278,7.85297Q9.5,7.92631,9.5,8Z"
            fill="currentColor"
          />
          <path
            d="M9.5,11.25C9.5,10.8358,9.83579,10.5,10.25,10.5L15.25,10.5C15.664200000000001,10.5,16,10.8358,16,11.25C16,11.6642,15.664200000000001,12,15.25,12L10.25,12C9.83579,12,9.5,11.6642,9.5,11.25Z"
            fill="currentColor"
          />
          <path
            d="M12.25,13.5C11.8358,13.5,11.5,13.8358,11.5,14.25C11.5,14.6642,11.8358,15,12.25,15L15.25,15C15.664200000000001,15,16,14.6642,16,14.25C16,13.8358,15.664200000000001,13.5,15.25,13.5L12.25,13.5Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
