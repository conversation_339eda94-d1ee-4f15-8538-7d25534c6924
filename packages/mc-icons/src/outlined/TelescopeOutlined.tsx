import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function TelescopeOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-telescope-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1860_03788">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1860_03788)">
          <path
            d="M14.18356414937973,1.14335040625C13.67456414937973,0.26168000625,12.523264149379731,-0.0010555937499999724,11.68216414937973,0.57249140625L0.9113531493797302,7.91686640625C0.1547770093797302,8.43275640625,-0.07615885062026978,9.44467640625,0.3817041493797302,10.23769640625L0.8288961493797302,11.01226640625C1.2867641493797302,11.80526640625,2.2785841493797303,12.11126640625,3.1036441493797304,11.71396640625L14.849464149379731,6.05839640625C15.76666414937973,5.61672640625,16.11476414937973,4.48832640625,15.60576414937973,3.60665640625L14.18356414937973,1.14335040625ZM12.52716414937973,1.81179640625C12.64736414937973,1.72985640625,12.81186414937973,1.76739640625,12.884564149379731,1.89334640625L14.306764149379731,4.35665640625C14.37946414937973,4.48260640625,14.32976414937973,4.64380640625,14.19866414937973,4.70690640625L12.18266414937973,5.67762640625L10.67846414937973,3.07237640625L12.52716414937973,1.81179640625ZM9.435754149379731,3.91980640625L10.827364149379731,6.33020640625L5.40623414937973,8.94049640625L4.46459414937973,7.30952640625L9.435754149379731,3.91980640625ZM3.22180414937973,8.15695640625L1.7564141493797303,9.15616640625C1.6483241493797303,9.22986640625,1.6153341493797302,9.37442640625,1.6807441493797302,9.48771640625L2.12793414937973,10.26229640625C2.19334414937973,10.37556640625,2.33503414937973,10.41926640625,2.45290414937973,10.36246640625L4.05094414937973,9.59306640625L3.22180414937973,8.15695640625ZM9.47544414937973,10.46276640625C9.26619414937973,10.15699640625,8.86496414937973,10.04979640625,8.53109414937973,10.21059640625L6.72225414937973,11.08146640625C6.604004149379731,11.13846640625,6.50276414937973,11.22546640625,6.42865414937973,11.33376640625L4.38102414937973,14.32646640625C4.147124149379731,14.66826640625,4.234634149379731,15.13506640625,4.57649414937973,15.36896640625C4.91834414937973,15.60286640625,5.38508414937973,15.51526640625,5.61898414937973,15.17346640625L7.5000041493797305,12.42426640625L7.5000041493797305,15.24996640625C7.5000041493797305,15.66416640625,7.83579414937973,15.99996640625,8.25000414937973,15.99996640625C8.664214149379731,15.99996640625,9.00000414937973,15.66416640625,9.00000414937973,15.24996640625L9.00000414937973,12.42426640625L10.88096414937973,15.17346640625C11.11486414937973,15.51526640625,11.58166414937973,15.60286640625,11.92346414937973,15.36896640625C12.26536414937973,15.13506640625,12.35286414937973,14.66826640625,12.11896414937973,14.32646640625L9.47544414937973,10.46276640625Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
