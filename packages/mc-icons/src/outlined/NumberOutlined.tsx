import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function NumberOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-number-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_04829">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_04829)">
          <path
            d="M5.6038773849487304,0.0887455C5.847747384948731,0.21926,5.999987384948731,0.473405,5.999987384948731,0.750001L5.999987384948731,5.51921L6.711017384948731,5.51921C7.12523738494873,5.51921,7.461017384948731,5.855,7.461017384948731,6.26921C7.461017384948731,6.68342,7.12523738494873,7.01921,6.711017384948731,7.01921L3.7585803849487305,7.01921C3.3443603849487307,7.01921,3.0085799649487304,6.68342,3.0085799649487304,6.26921C3.0085799649487304,5.855,3.3443603849487307,5.51921,3.7585803849487305,5.51921L4.499987384948731,5.51921L4.499987384948731,2.15139L4.16600738494873,2.37404C3.8213603849487305,2.6038,3.3557103849487304,2.51067,3.1259503849487307,2.16603C2.8961803849487304,1.82138,2.9893099849487306,1.35573,3.3339603849487305,1.12596L4.83396738494873,0.125963C5.064107384948731,-0.0274649,5.36001738494873,-0.0417689,5.6038773849487304,0.0887455ZM8.999987384948732,4.74996C8.999987384948732,4.33575,9.33577738494873,3.99996,9.749987384948732,3.99996L13.75002738494873,3.99996C14.05332738494873,3.99996,14.32682738494873,4.1827,14.44292738494873,4.46295C14.559027384948731,4.74321,14.49482738494873,5.0658,14.28032738494873,5.28029L12.38859738494873,7.17203C12.70099738494873,7.24801,12.99349738494873,7.35229,13.25622738494873,7.49091C13.99762738494873,7.88203,14.50002738494873,8.55444,14.50002738494873,9.49604C14.50002738494873,10.1488,14.26872738494873,10.7036,13.87112738494873,11.1234C13.485027384948731,11.5312,12.97749738494873,11.7759,12.46299738494873,11.8997C11.45209738494873,12.1431,10.23799738494873,11.9628,9.338777384948731,11.3733C8.99236738494873,11.1462,8.89565738494873,10.6812,9.12276738494873,10.3348C9.34987738494873,9.98843,9.81479738494873,9.89172,10.16119738494873,10.1188C10.69539738494873,10.469,11.48119738494873,10.5932,12.111997384948731,10.4414C12.418397384948731,10.3676,12.64209738494873,10.2398,12.781997384948731,10.092C12.91049738494873,9.95637,13.00002738494873,9.77277,13.00002738494873,9.49604C13.00002738494873,9.18764,12.87739738494873,8.98702,12.55629738494873,8.8176C12.182897384948731,8.62062,11.57599738494873,8.49996,10.749997384948731,8.49996C10.446597384948731,8.49996,10.17319738494873,8.31723,10.05709738494873,8.03698C9.94098738494873,7.75672,10.00519738494873,7.43413,10.21969738494873,7.21963L11.93929738494873,5.49996L9.749987384948732,5.49996C9.33577738494873,5.49996,8.999987384948732,5.16418,8.999987384948732,4.74996ZM5.412807384948731,10.5129C5.062137384948731,10.4621,4.6434073849487305,10.6255,4.430257384948731,11.0846C4.25583738494873,11.4603,3.8098703849487303,11.6235,3.4341803849487302,11.449C3.0584799849487303,11.2746,2.8953203849487306,10.8287,3.0697500849487307,10.453C3.5776803849487306,9.35891,4.658957384948731,8.88793,5.6280173849487305,9.02842C6.627167384948731,9.17327,7.500007384948731,9.97317,7.500007384948731,11.2499C7.500007384948731,12.6826,6.412117384948731,13.442,5.710667384948731,13.9315C5.40204738494873,14.1469,5.1389273849487305,14.3281,4.93782738494873,14.504L7.000007384948731,14.504C7.4142173849487305,14.504,7.750007384948731,14.8398,7.750007384948731,15.254C7.750007384948731,15.6682,7.4142173849487305,16.004,7.000007384948731,16.004L3.7500103849487303,16.004C3.3357903849487305,16.004,3.0000100139487307,15.6682,3.0000100139487307,15.254C3.0000100139487307,14.5647,3.2991403849487306,14.0425,3.6704903849487307,13.6446C4.01794738494873,13.2724,4.470027384948731,12.9684,4.820557384948731,12.7236C5.620687384948731,12.1647,6.000007384948731,11.8199,6.000007384948731,11.2499C6.000007384948731,10.7766,5.733387384948731,10.5594,5.412807384948731,10.5129Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
