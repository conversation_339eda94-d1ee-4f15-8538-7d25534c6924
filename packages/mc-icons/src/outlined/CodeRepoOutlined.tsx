import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function CodeRepoOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-code-repo-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_58_05743">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_58_05743)">
          <path
            d="M8.6191,1.4880859490737914L14.6191,6.218159949073791C14.8596,6.407779949073792,15,6.697179949073791,15,7.003469949073792L15,13.999999949073791C15,14.552299949073792,14.5523,14.999999949073791,14,14.999999949073791L2,14.999999949073791C1.447715,14.999999949073791,1,14.552299949073792,1,13.999999949073791L1,7.003469949073792C1,6.697179949073791,1.140368,6.407779949073792,1.380902,6.218159949073791L7.3809,1.4880859490737914C7.744,1.2018378490737915,8.256,1.2018378490737915,8.6191,1.4880859490737914ZM8,2.9100199490737912L2.499,7.2460199490737915L2.5,13.499999949073791L13.5,13.499999949073791L13.5,7.2460199490737915L8,2.9100199490737912Z"
            fill="currentColor"
          />
          <path
            d="M7.480329809265137,8.28035348423004C7.773219809265137,7.9874614842300415,7.773219809265137,7.512581484230042,7.480329809265137,7.219690484230042C7.187439809265136,6.926800884230041,6.712559809265137,6.926800884230041,6.4196698092651365,7.219690484230042L4.419667809265137,9.219693484230042C4.126777409265137,9.512583484230042,4.126777409265137,9.987463484230041,4.419667809265137,10.28035348423004L6.4196698092651365,12.280323484230042C6.712559809265137,12.573223484230041,7.187439809265136,12.573223484230041,7.480329809265137,12.280323484230042C7.773219809265137,11.987423484230042,7.773219809265137,11.512583484230042,7.480329809265137,11.219693484230042L6.010659809265137,9.750023484230042L7.480329809265137,8.28035348423004Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            strokeOpacity={1}
            stroke="currentColor"
            fill="none"
            strokeWidth={1.5}
            strokeLinecap="ROUND"
            d="M9.87392520904541 7.062101483345032 13.864940643310547 7.062101483345032"
            transform="matrix(-0.2505628168582916,0.968100368976593,-0.968100368976593,-0.2505628168582916,19.91086205138084,0.21057299847065636)"
          />
        </g>
      </svg>
    </span>
  );
}
