import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function CodeOptimizationOutlined(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-code-optimization-outlined': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_2690_06325">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_2690_06325)">
          <path
            d="M8.969668,5.469667C8.6767778,5.762557,8.6767778,6.237437,8.969668,6.53033L10.43934,8L8.969668,9.46967C8.6767778,9.76256,8.6767778,10.23744,8.969668,10.5303C9.262558,10.8232,9.737438000000001,10.8232,10.03033,10.5303L12.0303,8.53033C12.3232,8.23744,12.3232,7.762560000000001,12.0303,7.46967L10.03033,5.469667C9.737438000000001,5.1767774,9.262558,5.1767774,8.969668,5.469667Z"
            fill="currentColor"
          />
          <path
            d="M7.03033,6.53033C7.32322,6.237437,7.32322,5.762557,7.03033,5.469667C6.737439999999999,5.1767774,6.262560000000001,5.1767774,5.96967,5.469667L3.969668,7.46967C3.6767776,7.762560000000001,3.6767776,8.23744,3.969668,8.53033L5.96967,10.5303C6.262560000000001,10.8232,6.737439999999999,10.8232,7.03033,10.5303C7.32322,10.23744,7.32322,9.76256,7.03033,9.46967L5.56066,8L7.03033,6.53033Z"
            fill="currentColor"
          />
          <path
            d="M5.02862,2.2173C5.94786,1.74507,6.96656,1.49915,8,1.5C9.60748,1.49999,11.158,2.09554,12.3521,3.17167C13.5462,4.24779,14.2993,5.72818,14.466,7.327C14.4752,7.42569,14.5039,7.52157,14.5504,7.60911C14.5968,7.69665,14.6602,7.77411,14.7368,7.83701C14.8134,7.89991,14.9017,7.947,14.9966,7.97556C15.0915,8.00412,15.1912,8.01359,15.2898,8.00341C15.3884,7.99324,15.484,7.96362,15.571,7.91627C15.6581,7.86893,15.7349,7.80479,15.7971,7.72757C15.8592,7.65035,15.9054,7.56158,15.933,7.46639C15.9607,7.37121,15.9692,7.27149,15.958,7.173C15.7926,5.57905,15.1523,4.07152,14.12,2.84574C13.0878,1.61996,11.7112,0.732444,10.1687,0.298181C8.6261,-0.136082,6.98868,-0.097071,5.46856,0.41016C3.94844,0.91739,2.61569,1.86946,1.643,3.143L0.427,1.927C0.392036,1.89195,0.34745,1.86806,0.298896,1.85838C0.250342,1.8487,0.200005,1.85365,0.154269,1.87261C0.108532,1.89157,0.069455,1.92368,0.0419907,1.96487C0.0145264,2.00607,-0.0000877856,2.05449,3.96727e-7,2.104L3.96727e-7,5.75C3.96727e-7,5.8163,0.02634,5.87989,0.0732241,5.92678C0.120108,5.97366,0.183696,6,0.25,6L3.896,6C3.94551,6.00009,3.99393,5.98547,4.03513,5.95801C4.07632,5.93055,4.10843,5.89147,4.12739,5.84573C4.14635,5.8,4.1513,5.74966,4.14162,5.70111C4.13194,5.65255,4.10805,5.60797,4.073,5.573L2.715,4.215C3.31618,3.37442,4.10938,2.68953,5.02862,2.2173Z"
            fill="currentColor"
          />
          <path
            d="M1.2617314673888684,8.16880077319336C1.1075814673888684,8.043160473193359,0.9098434673888683,7.983880073193359,0.7120004673888684,8.003999713193359C0.6139484673888683,8.013990373193359,0.5188274673888683,8.04321957319336,0.43207646738886835,8.09000017319336C0.3453254673888683,8.136779773193359,0.2686454673888683,8.20019977319336,0.20642046738886832,8.276630773193359C0.14419546738886835,8.35306977319336,0.09764616738886833,8.441019773193359,0.06943386738886834,8.53544977319336C0.04122159738886833,8.62989077319336,0.03189959738886833,8.72895977319336,0.04200079738886833,8.826999773193359C0.20790546738886834,10.42069877319336,0.8484784673888683,11.92779877319336,1.8807614673888684,13.15329877319336C2.9130514673888683,14.37869877319336,4.289481467388868,15.26599877319336,5.831811467388868,15.70019877319336C7.374141467388869,16.13439877319336,9.011311467388868,16.09549877319336,10.531251467388868,15.58859877319336C12.051251467388868,15.08169877319336,13.383951467388869,14.13009877319336,14.356951467388868,12.856998773193359L15.572951467388869,14.072998773193358C15.607951467388869,14.108098773193358,15.652451467388868,14.13189877319336,15.701051467388869,14.141598773193358C15.749651467388869,14.15129877319336,15.799951467388869,14.14639877319336,15.845651467388869,14.12739877319336C15.891451467388869,14.10839877319336,15.930451467388869,14.07629877319336,15.957951467388868,14.03509877319336C15.985451467388868,13.99389877319336,16.000051467388868,13.94549877319336,15.999951467388868,13.895998773193359L15.999951467388868,10.24999877319336C15.999951467388868,10.18369877319336,15.97365146738887,10.120098773193359,15.926751467388868,10.07319877319336C15.879851467388868,10.026298773193359,15.816251467388868,9.99999877319336,15.749951467388868,9.99999877319336L12.103951467388868,9.99999877319336C12.05445146738887,9.99990877319336,12.006051467388868,10.01449877319336,11.964851467388868,10.04199877319336C11.923651467388868,10.06949877319336,11.891551467388869,10.108498773193359,11.872551467388869,10.154298773193359C11.853551467388868,10.199998773193359,11.84865146738887,10.250298773193359,11.858351467388868,10.29889877319336C11.868051467388868,10.34749877319336,11.891851467388868,10.39199877319336,11.926951467388868,10.42699877319336L13.284951467388868,11.78499877319336C12.516951467388868,12.85729877319336,11.440851467388867,13.670498773193358,10.199751467388868,14.116898773193359C8.958681467388868,14.56319877319336,7.6111514673888685,14.62139877319336,6.336131467388868,14.28389877319336C5.061111467388868,13.946298773193359,3.918921467388868,13.22889877319336,3.061211467388868,12.226998773193358C2.2035114673888683,11.22499877319336,1.6708714673888683,9.98582877319336,1.5340014673888682,8.673999773193358C1.5138114673888683,8.47615977319336,1.4158714673888684,8.294439773193359,1.2617314673888684,8.16880077319336Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
