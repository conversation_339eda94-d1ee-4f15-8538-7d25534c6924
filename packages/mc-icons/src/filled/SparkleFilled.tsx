import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function SparkleFilled(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-sparkle-filled': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_04617">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_04617)">
          <path
            d="M7.530485982284546,1.282449941543579C7.690645982284546,0.8449779415435791,8.309365982284547,0.8449779415435791,8.469525982284546,1.282449941543579L8.947525982284546,2.588055941543579C9.706295982284546,4.660575941543579,11.339375982284546,6.293685941543579,13.411875982284545,7.052455941543579L14.717475982284546,7.530455941543579C15.154975982284546,7.690615941543579,15.154975982284546,8.30933594154358,14.717475982284546,8.46949594154358L13.411875982284545,8.947495941543579C11.339375982284546,9.70625594154358,9.706285982284546,11.339445941543579,8.947525982284546,13.411945941543578L8.469525982284546,14.717545941543579C8.309365982284547,15.155045941543579,7.690645982284546,15.155045941543579,7.530485982284546,14.717545941543579L7.052485982284546,13.411945941543578C6.293715982284546,11.339445941543579,4.660605982284546,9.70625594154358,2.5880859822845457,8.947495941543579L1.2824799822845458,8.46949594154358C0.8450079822845459,8.30933594154358,0.8450079822845459,7.690615941543579,1.2824799822845458,7.530455941543579L2.5880859822845457,7.052455941543579C4.660605982284546,6.293685941543579,6.293715982284546,4.660575941543579,7.052485982284546,2.588055941543579L7.530485982284546,1.282449941543579Z"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
