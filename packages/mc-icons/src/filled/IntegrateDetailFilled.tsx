import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function IntegrateDetailFilled(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-integrate-detail-filled': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_4676_81086">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_4676_81086)">
          <path
            d="M0.7999992370605469,14.5L0.7999992370605469,1.5C0.7999992370605469,0.671573,1.4715722370605469,0,2.299999237060547,0L9.219999237060547,0L13.599999237060548,4.38L13.599999237060548,7.48C13.266699237060546,7.33333,12.922199237060546,7.22,12.566699237060547,7.14Q12.033299237060547,7.02,11.499999237060546,7.02C10.939999237060547,7.02,10.406669237060546,7.10667,9.899999237060547,7.28C9.393329237060547,7.45333,8.926669237060548,7.69333,8.499999237060546,8L3.5800792370605468,8C3.2486692370605468,8,2.979999237060547,8.26867,2.979999237060547,8.60008C2.979999237060547,8.93151,3.248679237060547,9.20018,3.580109237060547,9.20017L7.3199992370605464,9.2C7.093329237060547,9.52,6.910669237060547,9.86798,6.771999237060547,10.2439C6.633329237060547,10.6199,6.529329237060547,11.0052,6.459999237060547,11.4L3.580099237060547,11.3999C3.248679237060547,11.3999,2.979999237060547,11.6686,2.979999237060547,12C2.979999237060547,12.3314,3.248679237060547,12.6001,3.580099237060547,12.6001L6.4399992370605466,12.6C6.506669237060547,13.2667,6.696669237060547,13.8933,7.009999237060547,14.48Q7.479999237060547,15.36,8.239999237060548,16L2.299999237060547,16C1.4715722370605469,16,0.7999992370605469,15.3284,0.7999992370605469,14.5ZM12.400099237060546,4.9202L9.120059237060547,4.9202C8.843919237060547,4.9202,8.620059237060547,4.69634,8.620059237060547,4.4202L8.620059237060547,1.2002L12.400099237060546,4.9202ZM8.754899237060547,14.8448Q9.889799237060547,15.9797,11.499999237060546,15.9797Q13.110199237060547,15.9797,14.245099237060547,14.8448Q15.379999237060547,13.7099,15.379999237060547,12.0997Q15.379999237060547,10.4895,14.245099237060547,9.35463Q13.110199237060547,8.21973,11.499999237060546,8.21973Q9.889799237060547,8.21973,8.754899237060547,9.35463Q7.619999237060547,10.4895,7.619999237060547,12.0997Q7.619999237060547,13.7099,8.754899237060547,14.8448ZM13.574799237060548,10.0254C13.141199237060547,9.59179,12.438199237060546,9.59179,12.004599237060546,10.0254L11.752999237060546,10.2769L12.412399237060546,10.9363L12.663899237060546,10.6847C12.733399237060548,10.6153,12.845999237060546,10.6153,12.915499237060548,10.6847C12.984899237060548,10.7542,12.984899237060548,10.8668,12.915499237060548,10.9363L11.854999237060547,11.9967L11.752999237060546,12.0987L11.854999237060547,12.2006L12.310499237060547,12.6561L12.412399237060546,12.758L12.514399237060546,12.6561L13.574799237060548,11.5956C14.008399237060546,11.162,14.008399237060546,10.459,13.574799237060548,10.0254ZM10.997999237060547,10.0249C10.563859237060546,9.59453,9.863489237060547,9.59606,9.431219237060548,10.0283C8.998959237060546,10.4606,8.997419237060546,11.161,9.427789237060546,11.5951L9.679789237060547,11.8471L10.339149237060546,11.1878L10.087599237060546,10.9362C10.042659237060548,10.8913,10.025109237060548,10.8258,10.041559237060547,10.7644C10.057999237060546,10.703,10.105939237060547,10.6551,10.167309237060547,10.6387C10.228689237060546,10.6222,10.294169237060547,10.6397,10.339109237060548,10.6847L11.399599237060547,11.7452L11.501499237060546,11.8471L11.603499237060547,11.7452L12.058899237060547,11.2897L12.160899237060548,11.1877L10.997999237060547,10.0249ZM10.692619237060548,11.5409L10.590659237060548,11.439L9.428239237060547,12.6014C9.146139237060547,12.881,9.035039237060547,13.2909,9.137449237060547,13.6753C9.239859237060546,14.0597,9.540119237060546,14.36,9.924549237060546,14.4624C10.308989237060548,14.5648,10.718829237060547,14.4537,10.998899237060547,14.1712L11.147999237060548,14.0221L11.249999237060546,13.9201L10.692619237060548,13.3627L10.590649237060546,13.2607L10.339089237060547,13.5123C10.269649237060547,13.5817,10.157029237060547,13.5817,10.087569237060547,13.5123C10.018119237060548,13.4428,10.018129237060547,13.3302,10.087589237060547,13.2607L11.148099237060547,12.2003L11.249999237060546,12.0983L10.692619237060548,11.5409ZM13.425199237060546,12.4521L13.323299237060548,12.3501L12.765899237060546,12.9075L12.663899237060546,13.0095L12.765899237060546,13.1114L12.915399237060546,13.261C12.984899237060548,13.3305,12.984899237060548,13.4431,12.915499237060548,13.5125C12.845999237060546,13.582,12.733399237060548,13.582,12.663899237060546,13.5125L11.603499237060547,12.4521L11.501499237060546,12.3501L11.399599237060547,12.4521L10.944099237060547,12.9075L10.842199237060546,13.0095L12.004599237060546,14.1719C12.439199237060548,14.6027,13.139599237060548,14.6012,13.571799237060548,14.1689C14.004099237060547,13.7366,14.005599237060547,13.0363,13.575299237060547,12.6021L13.425199237060546,12.4521Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
