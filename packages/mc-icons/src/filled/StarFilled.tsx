import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function StarFilled(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-star-filled': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 16 16"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_1743_05154">
            <path d="M0 0H16V16H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_1743_05154)">
          <path
            d="M7.999996427631378,0.2500000000003881C8.140006427631377,0.24990999698838806,8.277246427631379,0.28901405098838806,8.396186427631378,0.3628868509883881C8.515126427631378,0.4367598509883881,8.611016427631379,0.542451850988388,8.672996427631379,0.667999850988388L10.554996427631378,4.482999850988388L14.764996427631377,5.094999850988388C14.903496427631378,5.115109850988388,15.033496427631379,5.173549850988388,15.140496427631378,5.263719850988388C15.247496427631377,5.353879850988388,15.327096427631378,5.472179850988388,15.370396427631379,5.605229850988388C15.413696427631379,5.738289850988388,15.418896427631378,5.880799850988388,15.385396427631377,6.016649850988388C15.351896427631377,6.152499850988388,15.281096427631379,6.276279850988388,15.180996427631378,6.373999850988388L12.134996427631378,9.343999850988387L12.853996427631378,13.535999850988388C12.877696427631378,13.673899850988388,12.862396427631378,13.815699850988388,12.809696427631378,13.945399850988387C12.756996427631378,14.075099850988389,12.669096427631379,14.187399850988388,12.555896427631378,14.269699850988388C12.442696427631379,14.351999850988388,12.308696427631379,14.400999850988388,12.169096427631379,14.411099850988387C12.029496427631377,14.421199850988389,11.889896427631378,14.392099850988387,11.765996427631379,14.326999850988388L7.999996427631378,12.346999850988388L4.233996427631379,14.326999850988388C4.110176427631378,14.391999850988388,3.9706564276313783,14.421099850988387,3.831186427631378,14.410999850988388C3.6917064276313782,14.400899850988388,3.5578364276313783,14.351999850988388,3.444676427631378,14.269899850988388C3.331516427631378,14.187699850988388,3.2435864276313784,14.075499850988388,3.190806427631378,13.945999850988388C3.1380264276313783,13.816499850988388,3.1225064276313783,13.674899850988387,3.145996427631378,13.536999850988389L3.8659964276313783,9.342999850988388L0.8179974276313782,6.373999850988388C0.7175634276313781,6.276319850988388,0.6464960276313781,6.152469850988388,0.6128480276313781,6.016469850988388C0.5792010276313782,5.880469850988388,0.5843179776313782,5.737769850988388,0.6276210276313782,5.604529850988388C0.6709240276313782,5.471289850988388,0.7506804276313782,5.352839850988388,0.8578524276313781,5.262609850988388C0.9650254276313781,5.1723798509883885,1.0953304276313782,5.113969850988388,1.2340004276313783,5.093999850988388L5.443996427631379,4.482999850988388L7.326996427631379,0.667999850988388C7.388976427631378,0.542451850988388,7.484856427631378,0.4367598509883881,7.603796427631378,0.3628868509883881C7.722736427631379,0.28901405098838806,7.859976427631378,0.24990999698838806,7.999996427631378,0.2500000000003881Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
