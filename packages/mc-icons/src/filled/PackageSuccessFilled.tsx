import React from 'react';
import classNames from 'classnames';
import type { SVGIconProps } from '../types';

import styles from '../index.module.less';

export default function PackageSuccessFilled(props: SVGIconProps) {
  return (
    <span
      className={classNames({
        ['mc-icon']: true,
        ['mc-icon-spin']: props.spin,
        'mc-package-success-filled': true,
        [styles[`mc-anticon-${props.size}`]]: !!props.size,
        [props.className]: !!props.className,
      })}
    >
      <svg
        {...props}
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="none"
        version={1.1}
        viewBox="0 0 20 20"
        className="anticon"
        focusable="false"
        width="1em"
        height="1em"
        aria-hidden="true"
      >
        <defs>
          <clipPath id="master_svg0_4437_82853">
            <path d="M0 0H20V20H0z" />
          </clipPath>
        </defs>
        <g clipPath="url(#master_svg0_4437_82853)">
          <path
            d="M16.50821953125,4.934810000000001L10.419769531250001,2.0935438C10.152499531250001,1.9688187,9.84372953125,1.9688187,9.57645953125,2.0935438L3.48803153125,4.934810000000001C3.38191053125,4.98433,3.29660953125,5.06963,3.24708653125,5.17576C3.13064953125,5.42526,3.23852453125,5.72192,3.48803153125,5.83836L9.57645953125,8.67962C9.84372953125,8.80435,10.152499531250001,8.80435,10.419769531250001,8.67962L16.50821953125,5.83836C16.61431953125,5.78883,16.69961953125,5.703530000000001,16.749119531250003,5.59741C16.865619531249997,5.3479,16.75771953125,5.05125,16.50821953125,4.934810000000001ZM8.68582953125,9.85115L3.23484053125,7.2266C3.16736953125,7.19412,3.09344753125,7.17725,3.01856353125,7.17725C2.74322553125,7.17725,2.52001953125,7.40045,2.52001953125,7.67579L2.52001953125,14.0232C2.52001953125,14.3853,2.71633853125,14.7189,3.03287953125,14.8948L8.50970953125,17.9375C8.583769531249999,17.9786,8.667099531249999,18.0002,8.75182953125,18.0002C9.02715953125,18.0002,9.25036953125,17.777,9.25036953125,17.5017L9.25036953125,10.74953C9.25036953125,10.3665,9.03094953125,10.01732,8.68582953125,9.85115ZM16.76161953125,7.2266L11.31062953125,9.85115C10.96550953125,10.01732,10.74608953125,10.3665,10.74608953125,10.74953L10.74608953125,11.33935Q10.95556953125,11.06731,11.19471953125,10.82094Q11.43386953125,10.57457,11.69954953125,10.35709Q11.96523953125,10.13961,12.25400953125,9.953859999999999Q12.54281953125,9.76811,12.85081953125,9.61651Q13.15891953125,9.46491,13.48221953125,9.349450000000001Q13.80561953125,9.233979999999999,14.14001953125,9.15616Q14.47441953125,9.07834,14.81551953125,9.03917Q15.15661953125,9,15.50001953125,9Q16.51661953125,9,17.476419531250002,9.334869999999999L17.476419531250002,7.67579C17.476419531250002,7.40045,17.25321953125,7.17725,16.977919531250002,7.17725C16.90301953125,7.17725,16.82911953125,7.19412,16.76161953125,7.2266Z"
            fillRule="evenodd"
            fill="currentColor"
          />
          <path
            d="M15.5,19.5Q15.61047,19.5,15.7208,19.49458Q15.831140000000001,19.48916,15.94108,19.47833Q16.051009999999998,19.4675,16.16029,19.45129Q16.26956,19.43508,16.37791,19.41353Q16.48625,19.39198,16.59341,19.36514Q16.70057,19.3383,16.80628,19.30623Q16.91199,19.274160000000002,17.016,19.23695Q17.12001,19.199730000000002,17.222070000000002,19.15746Q17.32413,19.115180000000002,17.424,19.06795Q17.52386,19.02072,17.62128,18.96864Q17.71871,18.91657,17.81346,18.85978Q17.90821,18.80299,18.00006,18.74161Q18.091920000000002,18.680239999999998,18.18065,18.61443Q18.269379999999998,18.54863,18.354770000000002,18.47855Q18.44016,18.40847,18.52201,18.33428Q18.60387,18.260089999999998,18.68198,18.18198Q18.760089999999998,18.10387,18.83428,18.02201Q18.90847,17.94016,18.97855,17.854770000000002Q19.04863,17.769379999999998,19.11443,17.68065Q19.180239999999998,17.591920000000002,19.24161,17.50006Q19.30299,17.40821,19.35978,17.31346Q19.41657,17.21871,19.46865,17.12128Q19.52072,17.02386,19.56795,16.924Q19.615180000000002,16.82413,19.65746,16.722070000000002Q19.699730000000002,16.62001,19.73695,16.516Q19.774160000000002,16.41199,19.80623,16.30628Q19.8383,16.20057,19.86514,16.09341Q19.89198,15.98625,19.91353,15.87791Q19.93508,15.76956,19.95129,15.66029Q19.9675,15.55101,19.97833,15.44108Q19.98916,15.331140000000001,19.99458,15.2208Q20,15.11047,20,15Q20,14.88953,19.99458,14.7792Q19.98916,14.668859999999999,19.97833,14.55892Q19.9675,14.44899,19.95129,14.33971Q19.93508,14.23044,19.91353,14.12209Q19.89198,14.01375,19.86514,13.90659Q19.8383,13.799430000000001,19.80623,13.693719999999999Q19.774160000000002,13.58801,19.73695,13.484Q19.699730000000002,13.37998,19.65746,13.27792Q19.615180000000002,13.17586,19.56795,13.076Q19.52072,12.976140000000001,19.46864,12.87871Q19.41657,12.78129,19.35978,12.68654Q19.30299,12.59179,19.24161,12.499929999999999Q19.180239999999998,12.40808,19.11443,12.31935Q19.04863,12.23062,18.97855,12.14523Q18.90847,12.05984,18.83428,11.97798Q18.760089999999998,11.89613,18.68198,11.81802Q18.60387,11.73991,18.52201,11.66572Q18.44016,11.59153,18.354770000000002,11.52145Q18.269379999999998,11.451372,18.18065,11.385566Q18.091920000000002,11.31976,18.00006,11.258387Q17.90821,11.197013,17.81346,11.140221Q17.71871,11.083429,17.62128,11.031354Q17.52386,10.97928,17.424,10.932048Q17.32413,10.884817,17.222070000000002,10.842542Q17.12001,10.800267,17.016,10.763052Q16.91199,10.725836,16.80628,10.693768Q16.70057,10.661701,16.59341,10.634859Q16.48625,10.608018,16.37791,10.5864662Q16.26956,10.5649148,16.16029,10.5487057Q16.051009999999998,10.5324966,15.94108,10.5216687Q15.831140000000001,10.5108409,15.7208,10.50542045Q15.61047,10.5,15.5,10.5Q15.38953,10.5,15.2792,10.50542045Q15.168859999999999,10.5108409,15.05892,10.5216687Q14.94899,10.5324966,14.83971,10.5487057Q14.73044,10.5649148,14.62209,10.5864662Q14.51375,10.608018,14.40659,10.634859Q14.299430000000001,10.661701,14.193719999999999,10.693768Q14.08801,10.725836,13.984,10.763052Q13.87998,10.800267,13.77792,10.842542Q13.67586,10.884817,13.576,10.932048Q13.476140000000001,10.97928,13.37871,11.031354Q13.28129,11.083429,13.18654,11.140221Q13.09179,11.197013,12.999929999999999,11.258387Q12.90808,11.31976,12.81935,11.385566Q12.73062,11.451372,12.64523,11.52145Q12.55984,11.59153,12.47798,11.66572Q12.39613,11.73991,12.31802,11.81802Q12.23991,11.89613,12.16572,11.97798Q12.09153,12.05984,12.02145,12.14523Q11.951372,12.23062,11.885566,12.31935Q11.81976,12.40808,11.758387,12.499929999999999Q11.697013,12.59179,11.640221,12.68654Q11.583429,12.78129,11.531354,12.87871Q11.47928,12.976140000000001,11.432048,13.076Q11.384817,13.17586,11.342542,13.27792Q11.300267,13.37998,11.263052,13.484Q11.225836,13.58801,11.193768,13.693719999999999Q11.161701,13.799430000000001,11.134859,13.90659Q11.108018,14.01375,11.0864662,14.12209Q11.0649148,14.23044,11.0487057,14.33971Q11.0324966,14.44899,11.0216687,14.55892Q11.0108409,14.668859999999999,11.00542045,14.7792Q11,14.88953,11,15Q11,15.11047,11.00542045,15.2208Q11.0108409,15.331140000000001,11.0216687,15.44108Q11.0324966,15.55101,11.0487057,15.66029Q11.0649148,15.76956,11.0864662,15.87791Q11.108018,15.98625,11.134859,16.09341Q11.161701,16.20057,11.193768,16.30628Q11.225836,16.41199,11.263052,16.516Q11.300267,16.62001,11.342542,16.722070000000002Q11.384817,16.82413,11.432048,16.924Q11.47928,17.02386,11.531354,17.12128Q11.583429,17.21871,11.640221,17.31346Q11.697013,17.40821,11.758387,17.50006Q11.81976,17.591920000000002,11.885566,17.68065Q11.951372,17.769379999999998,12.02145,17.854770000000002Q12.09153,17.94016,12.16572,18.02201Q12.23991,18.10387,12.31802,18.18198Q12.39613,18.260089999999998,12.47798,18.33428Q12.55984,18.40847,12.64523,18.47855Q12.73062,18.54863,12.81935,18.61443Q12.90808,18.680239999999998,12.999929999999999,18.74161Q13.09179,18.80299,13.18654,18.85978Q13.28129,18.91657,13.37871,18.96865Q13.476140000000001,19.02072,13.576,19.06795Q13.67586,19.115180000000002,13.77792,19.15746Q13.87998,19.199730000000002,13.984,19.23695Q14.08801,19.274160000000002,14.193719999999999,19.30623Q14.299430000000001,19.3383,14.40659,19.36514Q14.51375,19.39198,14.62209,19.41353Q14.73044,19.43508,14.83971,19.45129Q14.94899,19.4675,15.05892,19.47833Q15.168859999999999,19.48916,15.2792,19.49458Q15.38953,19.5,15.5,19.5ZM12.84955,15.59445C12.55666,15.301549999999999,12.55666,14.82668,12.84955,14.53379C13.14245,14.24089,13.61732,14.24089,13.91021,14.53379L14.7941,15.417670000000001L17.092190000000002,13.11957C17.385089999999998,12.82668,17.85996,12.82668,18.15285,13.11957C18.44575,13.412469999999999,18.44575,13.88734,18.15285,14.18023L15.5012,16.831879999999998Q15.20831,17.12478,14.7941,17.12478Q14.37988,17.12478,14.08699,16.831879999999998L12.84955,15.59445Z"
            fillRule="evenodd"
            fill="currentColor"
          />
        </g>
      </svg>
    </span>
  );
}
