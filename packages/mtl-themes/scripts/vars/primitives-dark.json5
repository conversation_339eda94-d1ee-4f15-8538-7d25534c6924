{
  fgColor: {
    default: {
      $value: '{base.color.neutral.0}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['fgColor'],
          codeSyntax: {
            web: 'var(--fgColor-default) /* utility class: .color-fg-default */',
          },
        },
      },
      mix: {
        color: '{base.color.neutral.1}',
        weight: 0.25,
      },
    },
    muted: {
      $value: '{base.color.neutral.2}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['fgColor'],
          codeSyntax: {
            web: 'var(--fgColor-muted) /* utility class: .color-fg-muted */',
          },
        },
      },
      mix: {
        color: '{base.color.neutral.3}',
        weight: 0.95,
      },
    },
    onEmphasis: {
      $value: '{base.color.white}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['fgColor'],
          codeSyntax: {
            web: 'var(--fgColor-onEmphasis) /* utility class: .color-fg-on-emphasis */',
          },
        },
      },
    },
    white: {
      $value: '{base.color.white}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'light',
          group: 'semantic',
          scopes: ['fgColor'],
        },
      },
    },
    black: {
      $value: '{base.color.black}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['fgColor'],
        },
      },
    },
    disabled: {
      $value: '{base.color.neutral.4}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['fgColor'],
        },
      },
    },
    link: {
      '@': {
        $value: '{fgColor.accent.@}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-link) /* utility class: .color-fg-accent */',
            },
          },
        },
      },
    },
    neutral: {
      '@': {
        $value: '{base.color.neutral.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
          },
        },
      },
    },
    accent: {
      '@': {
        $value: '{base.color.blue.3}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-accent) /* utility class: .color-fg-accent */',
            },
          },
        },
        mix: {
          color: '{base.color.blue.5}',
          weight: 0.35,
        },
      },
    },
    success: {
      '@': {
        $value: '{base.color.green.3}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-success) /* utility class: .color-fg-success */',
            },
          },
        },
      },
    },
    attention: {
      '@': {
        $value: '{base.color.yellow.3}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-attention) /* utility class: .color-fg-attention */',
            },
          },
        },
      },
    },
    severe: {
      '@': {
        $value: '{base.color.orange.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-severe) /* utility class: .color-fg-severe */',
            },
          },
        },
      },
    },
    danger: {
      '@': {
        $value: '{base.color.red.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-danger) /* utility class: .color-fg-danger */',
            },
          },
        },
      },
    },
    open: {
      '@': {
        $value: '{fgColor.success.@}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-open) /* utility class: .color-fg-open */',
            },
          },
        },
      },
    },
    closed: {
      '@': {
        $value: '{base.color.red.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-closed) /* utility class: .color-fg-closed */',
            },
          },
        },
      },
    },
    done: {
      '@': {
        $value: '{base.color.purple.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-done) /* utility class: .color-fg-done */',
            },
          },
        },
      },
    },
    upsell: {
      '@': {
        $value: '{base.color.purple.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-upsell)',
            },
          },
        },
      },
    },
    sponsors: {
      '@': {
        $value: '{base.color.pink.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['fgColor'],
            codeSyntax: {
              web: 'var(--fgColor-sponsors) /* utility class: .color-fg-sponsors */',
            },
          },
        },
      },
    },
  },
  bgColor: {
    default: {
      $value: '{base.color.neutral.9}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          scopes: ['bgColor', 'borderColor'],
          group: 'semantic',
          codeSyntax: {
            web: 'var(--bgColor-default) /* utility class: .color-bg-default */',
          },
        },
      },
    },
    muted: {
      $value: '{base.color.neutral.8}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          scopes: ['bgColor', 'borderColor'],
          group: 'semantic',
          codeSyntax: {
            web: 'var(--bgColor-muted) /* utility class: .color-bg-muted */',
          },
        },
      },
    },
    inset: {
      $value: '{base.color.black}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          scopes: ['bgColor', 'borderColor'],
          group: 'semantic',
          codeSyntax: {
            web: 'var(--bgColor-inset) /* utility class: .color-bg-inset */',
          },
        },
      },
    },
    emphasis: {
      $value: '{base.color.neutral.4}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['bgColor'],
          codeSyntax: {
            web: 'var(--bgColor-emphasis) /* utility class: .color-bg-emphasis */',
          },
        },
      },
    },
    inverse: {
      $value: '{base.color.white}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['bgColor'],
        },
      },
    },
    white: {
      $value: '{base.color.white}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['bgColor'],
        },
      },
    },
    black: {
      $value: '{base.color.black}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['bgColor'],
        },
      },
    },
    disabled: {
      $value: '{base.color.neutral.7}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['bgColor'],
        },
      },
      alpha: 0.7,
    },
    transparent: {
      $value: '{base.color.transparent}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['bgColor'],
          codeSyntax: {
            web: 'var(--bgColor-transparent) /* utility class: .color-bg-transparent */',
          },
        },
      },
    },
    neutral: {
      muted: {
        $value: '{base.color.neutral.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.neutral.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
          },
        },
      },
    },
    accent: {
      muted: {
        $value: '{base.color.blue.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-accent-muted) /* utility class: .color-bg-accent */',
            },
          },
        },
        alpha: 0.1,
      },
      emphasis: {
        $value: '{base.color.blue.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-accent-emphasis) /* utility class: .color-bg-accent-emphasis */',
            },
          },
        },
      },
    },
    success: {
      muted: {
        $value: '{base.color.green.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-success-muted) /* utility class: .color-bg-success */',
            },
          },
        },
        alpha: 0.15,
      },
      emphasis: {
        $value: '{base.color.green.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-success-emphasis) /* utility class: .color-bg-success-emphasis */',
            },
          },
        },
      },
    },
    attention: {
      muted: {
        $value: '{base.color.yellow.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-attention-muted) /* utility class: .color-bg-attention */',
            },
          },
        },
        alpha: 0.15,
      },
      emphasis: {
        $value: '{base.color.yellow.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-attention-emphasis) /* utility class: .color-bg-attention-emphasis */',
            },
          },
        },
      },
    },
    severe: {
      muted: {
        $value: '{base.color.orange.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-severe-muted) /* utility class: .color-bg-severe */',
            },
          },
        },
        alpha: 0.1,
      },
      emphasis: {
        $value: '{base.color.orange.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-severe-emphasis) /* utility class: .color-bg-severe-emphasis */',
            },
          },
        },
      },
    },
    danger: {
      muted: {
        $value: '{base.color.red.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-danger-muted) /* utility class: .color-bg-danger */',
            },
          },
        },
        alpha: 0.1,
      },
      emphasis: {
        $value: '{base.color.red.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-danger-emphasis) /* utility class: .color-bg-danger-emphasis */',
            },
          },
        },
      },
    },
    open: {
      muted: {
        $value: '{bgColor.success.muted}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-success-muted) /* utility class: .color-bg-success */',
            },
          },
        },
        alpha: 0.1,
      },
      emphasis: {
        $value: '{bgColor.success.emphasis}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-success-emphasis) /* utility class: .color-bg-success-emphasis */',
            },
          },
        },
      },
    },
    closed: {
      muted: {
        $value: '{base.color.red.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-closed-muted) /* utility class: .color-bg-closed */',
            },
          },
        },
        alpha: 0.15,
      },
      emphasis: {
        $value: '{base.color.red.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-closed-emphasis) /* utility class: .color-bg-closed-emphasis */',
            },
          },
        },
      },
    },
    done: {
      muted: {
        $value: '{base.color.purple.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-done-muted) /* utility class: .color-bg-done */',
            },
          },
        },
        alpha: 0.15,
      },
      emphasis: {
        $value: '{base.color.purple.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-done-emphasis) /* utility class: .color-bg-done-emphasis */',
            },
          },
        },
      },
    },
    upsell: {
      muted: {
        $value: '{base.color.purple.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-upsell-muted)',
            },
          },
        },
        alpha: 0.15,
      },
      emphasis: {
        $value: '{base.color.purple.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-upsell-emphasis)',
            },
          },
        },
      },
    },
    sponsors: {
      muted: {
        $value: '{base.color.pink.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-sponsors-muted) /* utility class: .color-bg-sponsors */',
            },
          },
        },
        alpha: 0.1,
      },
      emphasis: {
        $value: '{base.color.pink.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['bgColor'],
            codeSyntax: {
              web: 'var(--bgColor-sponsors-emphasis) /* utility class: .color-bg-sponsors-emphasis */',
            },
          },
        },
      },
    },
  },
  borderColor: {
    default: {
      $value: '{base.color.neutral.6}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['borderColor'],
          codeSyntax: {
            web: 'var(--borderColor-default) /* utility class: .color-border-default */',
          },
        },
      },
    },
    muted: {
      $value: '{borderColor.default}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['borderColor'],
          codeSyntax: {
            web: 'var(--borderColor-muted) /* utility class: .color-border-muted */',
          },
        },
      },
      alpha: 0.7,
    },
    emphasis: {
      $value: '{base.color.neutral.5}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['borderColor'],
        },
      },
    },
    disabled: {
      $value: '{bgColor.disabled}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['borderColor'],
        },
      },
    },
    transparent: {
      $value: '{base.color.transparent}',
      $type: 'color',
      $extensions: {
        'org.primer.figma': {
          collection: 'mode',
          mode: 'dark',
          group: 'semantic',
          scopes: ['borderColor'],
        },
      },
    },
    neutral: {
      muted: {
        $value: '{base.color.neutral.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.neutral.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
          },
        },
      },
    },
    accent: {
      muted: {
        $value: '{base.color.blue.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-accent-muted) /* utility class: .color-border-accent */',
            },
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.blue.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-accent-emphasis) /* utility class: .color-border-accent-emphasis */',
            },
          },
        },
      },
    },
    success: {
      muted: {
        $value: '{base.color.green.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-success-muted) /* utility class: .color-border-success */',
            },
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.green.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-success-emphasis) /* utility class: .color-border-success-emphasis */',
            },
          },
        },
      },
    },
    attention: {
      muted: {
        $value: '{base.color.yellow.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-attention-muted) /* utility class: .color-border-attention */',
            },
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.yellow.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-attention-emphasis) /* utility class: .color-border-attention-emphasis */',
            },
          },
        },
      },
    },
    severe: {
      muted: {
        $value: '{base.color.orange.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-severe-muted) /* utility class: .color-border-severe */',
            },
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.orange.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-severe-emphasis) /* utility class: .color-border-severe-emphasis */',
            },
          },
        },
      },
    },
    danger: {
      muted: {
        $value: '{base.color.red.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-danger-muted) /* utility class: .color-border-danger */',
            },
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.red.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-danger-emphasis) /* utility class: .color-border-danger-emphasis */',
            },
          },
        },
      },
    },
    open: {
      muted: {
        $value: '{borderColor.success.muted}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-open-muted) /* utility class: .color-border-open */',
            },
          },
        },
      },
      emphasis: {
        $value: '{borderColor.success.emphasis}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-open-emphasis) /* utility class: .color-border-open-emphasis */',
            },
          },
        },
      },
    },
    closed: {
      muted: {
        $value: '{base.color.red.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-closed-muted) /* utility class: .color-border-closed */',
            },
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.red.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-closed-emphasis) /* utility class: .color-border-closed-emphasis */',
            },
          },
        },
      },
    },
    done: {
      muted: {
        $value: '{base.color.purple.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-done-muted) /* utility class: .color-border-done */',
            },
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.purple.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-done-emphasis) /* utility class: .color-border-done-emphasis */',
            },
          },
        },
      },
    },
    upsell: {
      muted: {
        $value: '{base.color.purple.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-upsell-muted)',
            },
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.purple.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-upsell-emphasis',
            },
          },
        },
      },
    },
    sponsors: {
      muted: {
        $value: '{base.color.pink.4}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-sponsors-muted) /* utility class: .color-border-sponsors */',
            },
          },
        },
        alpha: 0.4,
      },
      emphasis: {
        $value: '{base.color.pink.5}',
        $type: 'color',
        $extensions: {
          'org.primer.figma': {
            collection: 'mode',
            mode: 'dark',
            group: 'semantic',
            scopes: ['borderColor'],
            codeSyntax: {
              web: 'var(--borderColor-sponsors-emphasis) /* utility class: .color-border-sponsors-emphasis */',
            },
          },
        },
      },
    },
  },
}
