import { readFileSync, writeFileSync } from 'node:fs';
import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';
import { get, camelCase } from 'lodash-es';
import JSON5 from 'json5';
import { TinyColor } from '@ctrl/tinycolor';

const __dirname = dirname(fileURLToPath(import.meta.url));

const PATTERNS_LIST = ['control', 'button'];
const inputFiles = {
  lightBase: join(__dirname, './vars/light.json5'),
  darkBase: join(__dirname, './vars/dark.json5'),
  lightPrimitives: join(__dirname, './vars/primitives-light.json5'),
  darkPrimitives: join(__dirname, './vars/primitives-dark.json5'),
  lightPatterns: join(__dirname, './vars/patterns-light.json5'),
  darkPatterns: join(__dirname, './vars/patterns-dark.json5'),
};


/**
 * 加载解析 JSON5 文件
 */
function readJson5(filePath) {
  const content = readFileSync(filePath, 'utf8');
  return JSON5.parse(content);
}

const [
  lightBase,
  darkBase,
  lightPrimitives,
  darkPrimitives,
  lightPatterns,
  darkPatterns,
] = [
  readJson5(inputFiles.lightBase),
  readJson5(inputFiles.darkBase),
  readJson5(inputFiles.lightPrimitives),
  readJson5(inputFiles.darkPrimitives),
  readJson5(inputFiles.lightPatterns),
  readJson5(inputFiles.darkPatterns),
];

function getThemeJSON(mode) {
  const baseJson = mode === 'light' ? lightBase : darkBase;
  const primitivesJson = mode === 'light' ? lightPrimitives : darkPrimitives;
  const patternsJson = mode === 'light' ? lightPatterns : darkPatterns;

  return [baseJson, primitivesJson, patternsJson];
}

/**
 * 处理引用的色值
 */
function resolveValue(path, mode) {
  const [baseJson, primitivesJson, patternsJson] = getThemeJSON(mode);
  const keys = path.slice(1, -1).split('.');
  if (keys.length === 0) {
    return undefined;
  }

  if (keys[0] === 'base') {
    return get(baseJson, keys);
  }

  if (PATTERNS_LIST.includes(keys[0])) {
    return get(patternsJson, keys);
  }

  return get(primitivesJson, keys);
}

function calcColor(color, mode) {
  const colorValue = color.$value || color['@'].$value;
  let value = '';

  // 需要继续处理引用
  if (colorValue.startsWith('{')) {
    value = calcColor(resolveValue(colorValue, mode), mode);
  } else {
    value = colorValue;
  }

  if (color?.alpha) {
    value = new TinyColor(value).setAlpha(color.alpha).toHex8String();
  }

  if (color?.mix) {
    const mixColor = calcColor(resolveValue(color.mix.color, mode), mode);
    const { weight } = color.mix;
    value = new TinyColor(value).mix(mixColor, weight * 100).toHexString();
  }

  return value;
}

function processColor(topKey, color, mode) {
  if (color?.$value || color?.['@']) {
    return [
      [topKey, calcColor(color, mode)],
    ];
  }

  const items = [];
  Object.keys(color).forEach((key) => {
    const _items = processColor(`${topKey}-${key}`, color[key], mode);
    items.push(..._items);
  });

  return items;
}

function generateVars(mode) {
  const [baseJson, primitivesJson, patternsJson] = getThemeJSON(mode);
  const vars = {};

  // 处理基准色版
  Object.keys(baseJson.base.color).forEach((key) => {
    const variableName = `base-color-${key}`;
    if (baseJson.base.color[key]?.$value) {
      let value = calcColor(baseJson.base.color[key], mode);
      vars[`${variableName}`] = value;
    } else {
      Object.keys(baseJson.base.color[key]).forEach(idx => {
        const variableNameIdx = `${variableName}-${idx}`;
        let value = calcColor(baseJson.base.color[key][idx], mode);
        vars[`${variableNameIdx}`] = value;
      });
    }
  });

  // 处理衍生色版
  Object.keys(primitivesJson).forEach((topKey) => {
    const items = processColor(topKey, primitivesJson[topKey], mode);
    items.forEach((item) => {
      const [key, value] = item;
      vars[`${key}`] = value;
    });
  });

  // 处理 Patterns 色版
  PATTERNS_LIST.forEach(topKey => {
    const items = processColor(topKey, patternsJson[topKey], mode);
    items.forEach((item) => {
      const [key, value] = item;
      vars[`${key}`] = value;
    });
  });

  return vars;
}
function generateLessContent(mode, vars) {
  const lines = [];
  for (const [key, value] of Object.entries(vars)) {
    lines.push(`@${mode}-${key}: ${value};`);
  }

  return lines.join('\n');
}

function generateTsContent(mode, vars) {
  const [baseJson] = getThemeJSON(mode);
  let colorGroups = {};

  for (const [section, values] of Object.entries(baseJson.base.color)) {
    if (values.$value) continue;

    colorGroups[section] = [];
    for (const colorObj of Object.values(values)) {
      let value = calcColor(colorObj, baseJson);
      colorGroups[section].push(value);
    }
  }

  const lines = [];
  lines.push(`
/*
 * DO NOT  MODIFY THIS FILE
 * GENERATED BY THE SCRIPT via "pnpm --filter=mc-uikit run build"
 */
`);
  lines.push('import { PresetColorKey } from \'../interface\';');
  lines.push('export const presetPrimaryColors: Partial<Record<PresetColorKey, string[]>> = {');
  for (const [key, values] of Object.entries(colorGroups)) {
    // Adding aliases for neutral
    if (key === 'neutral') {
      lines.push(`  gray: ['${values.join('\', \'')}'],`);
      lines.push(`  grey: ['${values.join('\', \'')}'],`);
    } else {
      lines.push(`  ${key}: ['${values.join('\', \'')}'],`);
    }
  }
  lines.push('};');
  // lines.push(`export const black = '${baseJson.base.color.black.$value}';`);
  // lines.push(`export const white = '${baseJson.base.color.white.$value}';`);

  for (const [key, value] of Object.entries(vars)) {
    lines.push(`export const ${camelCase(key)} = '${value}';`);
  }

  // 这里兼容一下已有的变量使用
  lines.push('export const black = baseColorBlack;');
  lines.push('export const white = baseColorWhite;');


  return lines.join('\n');
}

export function generateFiles() {
  const lightVars = generateVars('light');
  const darkVars = generateVars('dark');

  const lightLessContent = generateLessContent('light', lightVars);
  const darkLessContent = generateLessContent('dark', darkVars);

  const lightTsContent = generateTsContent('light', lightVars);
  const darkTsContent = generateTsContent('dark', darkVars);

  writeFileSync(join(__dirname, '..', 'src', 'cloud', 'styles', 'vars.less'), `
${lightLessContent}
${darkLessContent}
`);

  writeFileSync(join(
    __dirname,
    '..',
    'src',
    'cloud',
    'theme',
    'vars',
    'light.ts',
  ), lightTsContent);
  writeFileSync(join(
    __dirname,
    '..',
    'src',
    'cloud',
    'theme',
    'vars',
    'dark.ts',
  ), darkTsContent);

  console.log('LESS and TypeScript files generated successfully!');
}
