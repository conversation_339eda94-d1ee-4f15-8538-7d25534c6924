import React, { useEffect, useState, useRef } from 'react';
import { Flex, Input, theme, Pagination, Modal, message } from 'antd';
import styles from './index.module.less';
import { Link } from 'ice';
import { PeopleOutlined, PersonOutlined, OrganizationOutlined } from '@ali/mc-icons';
import { DataEntryList, User, Authorized, ListItem, InlineButton } from '@ali/mc-uikit';
// import { DataEntryFilterType } from '@ali/mc-uikit/esm/FilterGroup';
import { FilterValue } from '@ali/mc-uikit/esm/DataEntryList/interface';
import { DEVELOPMENT_SPACE_STATUS } from '../../common/statusMap';
import { useRequest } from '@ali/mc-request';
import {
  queryCollaborationSpacePage,
  deleteCollaborationSpace,
  type QueryCollaborationSpacePageParams,
} from '@ali/mc-services/CollaborationSpace';
import { PaginationResult, CollaborationSpaceBO } from '@ali/mc-services';
import { setSpaceInfoToLocalStorage } from '@/util/utils';
import SpaceEditFormDrawer from './SpaceEditFormDrawer';
import OpenedIterationCountTag from './OpenedIterationCountTag';
const { confirm } = Modal;

const DEFAULT_PAGE_NUM = 1;
const DEFAULT_PAGE_SIZE = 10;

type ListItemProps = {
  data: CollaborationSpaceBO;
  loading?: boolean;
  refreshList: () => void;
  openSpaceSettingDrawer: () => void;
};

function ListItems(props: ListItemProps) {
  const { data, refreshList, openSpaceSettingDrawer } = props;
  const { token } = theme.useToken();
  const { owner, admins, bizIdentity, id, belong } = (data as CollaborationSpaceBO) || {};
  const validType = 'DEVELOPMENT';

  const deleteSpace = (): void => {
    confirm({
      title: '确认删除当前空间?',
      content: '请谨慎操作',
      onOk() {
        deleteCollaborationSpace({
          spaceId: id as number,
        }).then((res) => {
          if (res) {
            message.success('删除空间成功!');
            refreshList();
          }
        });
      },
    });
  };

  return (
    <ListItem
      key={data?.id}
      icon={belong === 'TEAM' ? <PeopleOutlined /> : <PersonOutlined />}
      title={
        <Link
          onClick={() => {
            id && setSpaceInfoToLocalStorage(`${validType}LOG`, 'space', id);
          }}
          to={belong === 'TEAM' ? `/teamspaces/${id}` : `/spaces/${id}`}
          target={belong === 'TEAM' ? `teamspaces_${id}` : `spaces_${id}`}
        >
          {data?.name}
        </Link>
      }
      extra={<User showAvatar empIds={belong === 'TEAM' ? owner : admins} avatarNum={1} />}
      actions={[
        ['BIZ', 'MANAGER', 'TEAM'].includes(belong!) ? undefined : (
          <Flex key="actions" align="center" gap={token.margin}>
            <Authorized
              authority={'COLLABORATION_SPACE_ADMIN'}
              resourceId={id as number}
              message="您不是当前空间的成员，没有权限操作，请联系该空间的管理员。"
            >
              <InlineButton
                type="text"
                onClick={() => {
                  openSpaceSettingDrawer();
                }}
              >
                编辑
              </InlineButton>
            </Authorized>
            <Authorized
              authority={'COLLABORATION_SPACE_ADMIN'}
              resourceId={id as number}
              message="您不是当前空间的成员，没有权限操作，请联系该空间的管理员。"
            >
              <InlineButton
                type="text"
                onClick={() => {
                  deleteSpace();
                }}
              >
                删除
              </InlineButton>
            </Authorized>
          </Flex>
        ),
      ]}
    >
      <Flex vertical>
        <Flex>{data?.id && <OpenedIterationCountTag spaceId={data?.id} />}</Flex>
        <Flex>
          {bizIdentity?.deptName && (
            <Flex gap={token.marginXXS} align="center">
              <OrganizationOutlined className={styles.icon} />
              <span>{bizIdentity?.deptName}</span>
            </Flex>
          )}
        </Flex>
      </Flex>
    </ListItem>
  );
}

const AllSpaceList = () => {
  const { token } = theme.useToken();
  const [filterData, setFilterData] = useState<FilterValue>({
    status: 'IN_USE',
  });
  const [keyWord, setKeyWord] = useState<string>('');
  const [pageNum, setPageNum] = useState(DEFAULT_PAGE_NUM);
  const [belongStatus, setBelongStatus] = useState<'TEAM' | 'PERSONAL'>(DEVELOPMENT_SPACE_STATUS['TEAM']);
  const [spaceCount, setSpaceCount] = useState({
    teamCount: 0,
    personalCount: 0,
  });
  const [spaceSettingDrawerOpen, setSpaceSettingDrawerOpen] = useState<boolean>(false);
  const drawerRecordRef = useRef<CollaborationSpaceBO | null>(null);
  // const filterList: DataEntryFilterType[] = useMemo(() => {
  //   const commonFilters = [scopeFilter];
  //   return commonFilters;
  // }, []);

  const {
    data: spaceList,
    runAsync: getCollaborationSpacePage,
    loading,
  } = useRequest<PaginationResult<CollaborationSpaceBO>, [QueryCollaborationSpacePageParams]>(
    queryCollaborationSpacePage,
  );

  const getSpaceList = (requestData: QueryCollaborationSpacePageParams = {}) => {
    getCollaborationSpacePage({
      type: 'DEVELOPMENT',
      onlyShowMine: false,
      pageSize: 10,
      pageNum: pageNum,
      ...requestData,
    });
  };

  // 获取团队和个人的空间条数
  const getSpaceListCount = async (curFilterData: any) => {
    const params = {
      ...curFilterData,
      type: 'DEVELOPMENT',
      onlyShowMine: false,
      pageNum: 1,
      pageSize: 1,
    };
    const res = await Promise.all(
      ['TEAM', 'PERSONAL'].map((item) => {
        return queryCollaborationSpacePage({
          ...params,
          belong: DEVELOPMENT_SPACE_STATUS[item],
        });
      }),
    );

    setSpaceCount({
      teamCount: res[0]?.totalCount ?? 0,
      personalCount: res[1]?.totalCount ?? 0,
    });
  };

  useEffect(() => {
    getSpaceListCount(filterData);
    getSpaceList({ pageNum: DEFAULT_PAGE_NUM - 1, belong: belongStatus, ...filterData });
    return () => {};
  }, []);

  const onPaginationChange = (page: number) => {
    setPageNum(page);
    // 后端分页从0开始计算
    getSpaceList({ pageNum: page - 1, belong: belongStatus, name: keyWord, ...filterData });
  };

  const onFilterKeyWord = (value: string) => {
    const newFilterData = { ...filterData, name: value };
    setPageNum(DEFAULT_PAGE_NUM);
    getSpaceList({ pageNum: DEFAULT_PAGE_NUM - 1, belong: belongStatus, ...newFilterData });
    getSpaceListCount(newFilterData);
  };

  const onFilterChange = (value: any) => {
    setFilterData(value);
    setPageNum(DEFAULT_PAGE_NUM);
    getSpaceListCount(value);
    getSpaceList({ pageNum: DEFAULT_PAGE_NUM - 1, belong: belongStatus, name: keyWord, ...value });
  };

  const onDrawerClose = () => {
    setSpaceSettingDrawerOpen(false);
    drawerRecordRef.current = null;
  };

  const openSpaceSettingDrawer = (data: CollaborationSpaceBO) => {
    setSpaceSettingDrawerOpen(true);
    drawerRecordRef.current = data;
  };

  const onUpdate = async () => {
    onDrawerClose();
    getSpaceList({ pageNum: pageNum - 1, belong: belongStatus, name: keyWord, ...filterData });
  };

  return (
    <Flex vertical gap={token.margin} className={styles.allSpaceListWrap}>
      <div className={styles.title}>全部空间</div>
      <Input.Search
        style={{ width: '512px' }}
        placeholder="输入空间关键字"
        allowClear
        onSearch={(value: string) => {
          setKeyWord(value);
          onFilterKeyWord(value);
        }}
      />

      <DataEntryList
        dataSource={spaceList?.items || []}
        loading={loading}
        // filters={filterList}
        filterValue={filterData}
        onFilter={onFilterChange}
        actionTrigger="show"
        title={
          <Flex gap={token.marginLG} align="center">
            <Flex
              className={belongStatus === DEVELOPMENT_SPACE_STATUS['TEAM'] ? styles.active : styles.normal}
              gap="small"
              align="center"
              onClick={() => {
                if (loading) return;
                setBelongStatus(DEVELOPMENT_SPACE_STATUS['TEAM']);
                getSpaceList({
                  belong: DEVELOPMENT_SPACE_STATUS['TEAM'],
                  pageNum: DEFAULT_PAGE_NUM - 1,
                  name: keyWord,
                  ...filterData,
                });
                setPageNum(DEFAULT_PAGE_NUM);
              }}
              style={{ cursor: loading ? 'not-allowed' : 'pointer' }}
            >
              <PeopleOutlined />
              <Flex gap={token.marginXXS}>
                <span>{spaceCount?.teamCount ?? 0}</span>
                <span>团队空间</span>
              </Flex>
            </Flex>
            <Flex
              className={belongStatus === DEVELOPMENT_SPACE_STATUS['PERSONAL'] ? styles.active : styles.normal}
              gap="small"
              align="center"
              onClick={() => {
                if (loading) return;
                setBelongStatus(DEVELOPMENT_SPACE_STATUS['PERSONAL']);
                getSpaceList({
                  belong: DEVELOPMENT_SPACE_STATUS['PERSONAL'],
                  pageNum: DEFAULT_PAGE_NUM - 1,
                  name: keyWord,
                  ...filterData,
                });
                setPageNum(DEFAULT_PAGE_NUM);
              }}
              style={{ cursor: loading ? 'not-allowed' : 'pointer' }}
            >
              <PersonOutlined />
              <Flex gap={token.marginXXS}>
                <span>{spaceCount?.personalCount ?? 0}</span>
                <span>个人空间</span>
              </Flex>
            </Flex>
          </Flex>
        }
        renderItem={(record: CollaborationSpaceBO) => (
          <ListItems
            data={record}
            loading={loading}
            refreshList={() => {
              getSpaceList({ pageNum: DEFAULT_PAGE_NUM - 1, belong: belongStatus, name: keyWord, ...filterData });
            }}
            openSpaceSettingDrawer={() => {
              openSpaceSettingDrawer(record);
            }}
          />
        )}
      />

      <Flex style={{ width: '100%' }} justify="center">
        <Pagination
          total={spaceList?.totalCount ?? 0}
          pageSize={DEFAULT_PAGE_SIZE}
          showSizeChanger={false}
          current={pageNum}
          showTotal={(total: number) => `共有${total}条`}
          onChange={onPaginationChange}
        />
      </Flex>

      {/* 编辑空间抽屉 */}
      {spaceSettingDrawerOpen && (
        <SpaceEditFormDrawer
          onDrawerClose={onDrawerClose}
          open={spaceSettingDrawerOpen}
          defaultValue={drawerRecordRef.current}
          onUpdate={onUpdate}
        />
      )}
    </Flex>
  );
};

export default AllSpaceList;
