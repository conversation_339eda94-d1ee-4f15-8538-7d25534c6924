import React, { useEffect, useMemo, useRef } from 'react';
import { Chart } from '@antv/g2';
import { MetricData } from '@ali/mc-services';
import { Node } from '@antv/g2/lib/api/node';
import { theme } from 'antd';

const colors = ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#E86452', '#6DC8EC', '#945FB9', '#FF9845', '#1E9493'];

type MemberDataItem = {
  owner: string;
  type: string;
  quantity: number;
};

type MemberBarChartProps = {
  data?: MetricData;
};
export default function MemberBarChart(props: MemberBarChartProps) {
  const { data } = props;
  const { token } = theme.useToken();

  const barData = useMemo(() => {
    const items: MemberDataItem[] = [];
    data?.trendData?.trendDetail?.forEach((detail) => {
      const owner: string = detail.owner as unknown as string;
      Object.keys(detail).forEach((key) => {
        if (key !== 'owner') {
          items.push({
            owner,
            type: key,
            quantity: Number(detail[key] as unknown as number),
          });
        }
      });
    });
    return items;
  }, [data]);

  const containerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<Chart>();
  const initialedRef = useRef<boolean>(false);
  useEffect(() => {
    if (!chartRef.current) {
      chartRef.current = new Chart({
        container: containerRef.current!,
        height: 300,
        width: 1230,
        autoFit: true,
      });
    }

    const memberSet = new Set();
    barData?.forEach((item) => {
      memberSet.add(item.owner);
    });
    if (chartRef.current && !!barData?.length && !initialedRef.current) {
      const interval = chartRef.current
        .interval()
        .data(barData)
        .encode({
          x: 'owner',
          y: {
            type: 'field',
            value: 'quantity',
          },
        })
        .encode('color', 'type')
        .style({
          maxWidth: 80,
          stroke: '#fff',
        })
        .legend({
          color: {
            layout: {
              justifyContent: 'center',
              alignItems: 'flex-end',
              flexDirection: 'column',
            },
            position: 'top',
          },
        })
        .axis('y', {
          grid: false,
          title: '模块数',
          position: 'left',
        })
        .axis('x', {
          title: false,
          lineLineDash: [100, 0],
        })
        .tooltip((_d) => ({
          name: _d.type,
          value: _d.quantity,
        }))
        .scale('color', { type: 'ordinal', range: colors })
        .scale({
          x: {
            type: 'band',
          },
        })
        .transform({ type: 'stackY', reverse: true })
        .animate(false);

      chartRef.current
        .lineY()
        .data([0])
        .style('lineWidth', 1)
        .style('stroke', token.colorTextQuaternary)
        .style('strokeOpacity', 1);

      if (memberSet.size > 30) {
        interval.slider('x', {
          values: [0, 30 / memberSet.size],
          formatter: () => '',
        });
      }

      chartRef.current.render().then(() => {
        initialedRef.current = true;
      });
    }

    if (chartRef.current && !!barData.length && initialedRef.current) {
      chartRef.current.getNodesByType('interval').forEach((node: Node) => {
        node?.data(barData);
      });

      if (memberSet.size > 30) {
        chartRef.current.getNodesByType('interval').forEach((node: Node) => {
          node.slider('x', {
            values: [0, 30 / memberSet.size],
            formatter: () => '',
            handle: false,
          });
        });
      }
      chartRef?.current?.render();
    }
  }, [barData, token.colorTextQuaternary]);

  return <div style={{ width: '100%' }} ref={containerRef} />;
}
