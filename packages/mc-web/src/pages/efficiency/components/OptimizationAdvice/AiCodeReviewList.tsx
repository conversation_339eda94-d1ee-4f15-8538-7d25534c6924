import React from 'react';
import { Flex, Badge, Collapse, Pagination, Spin, Empty, theme } from 'antd';
import { LinkExternalOutlined, FileOutlined, CarryOutOutlined } from '@ali/mc-icons';
import { AppPlatform } from '@/components/Icons';
import { ALTER_SHEET_TYPE_MAP } from '@/constants/alterSheet';
import type { CodeReviewIterationSummaryVO } from '@ali/mc-services';
import CodeReviewItem from '@/components/CodeReviewItem';
import { getConciseVersionName } from '../../common/efficiency';
import styles from './index.module.less';

type AiCodeReviewListProps = {
  loading?: boolean;
  data?: CodeReviewIterationSummaryVO[];
  totalCount?: number;
  pageSize?: number;
  pageNum?: number;
  spaceId?: number;
  onPaginationChange?: (value: number) => void;
  onRefresh?: () => void;
};

export default function AiCodeReviewList(props: AiCodeReviewListProps) {
  const { loading, data = [], totalCount, pageSize, pageNum, spaceId, onPaginationChange, onRefresh } = props;
  const { token } = theme.useToken();

  return (
    <Flex vertical gap={token.marginSM}>
      {!loading && !data?.length ? (
        <Empty description="暂无优化建议" />
      ) : (
        <Spin spinning={loading}>
          <div style={{ minHeight: 100 }}>
            {data?.map((item) => (
              <Flex key={item?.alterSheetId} className={styles.AiCrList} vertical>
                <Flex className={styles.AiCrHeader} vertical>
                  <Flex className={styles.AiCrHeaderTitle} gap={token.marginXXS}>
                    <span>迭代：{item?.alterSheetName}</span>
                    <LinkExternalOutlined className={styles.headerTitleIcon} />
                  </Flex>
                  <Flex className={styles.AiCrHeaderDesc} gap={token.marginXS}>
                    {item?.alterSheetType && (
                      <Flex align="center" gap={token.marginXXS}>
                        <FileOutlined />
                        {ALTER_SHEET_TYPE_MAP[item.alterSheetType] || item.alterSheetType}
                      </Flex>
                    )}
                    {item?.appName && (
                      <Flex align="center" gap={token.marginXXS}>
                        <AppPlatform />
                        <a
                          className={styles.linkText}
                          href={`https://mc.alibaba-inc.com/#/apps/${item?.appId}/config`}
                          target="_blank"
                        >
                          {item.appName}
                        </a>
                      </Flex>
                    )}
                    {item?.versionPlanName && (
                      <Flex align="center" gap={token.marginXXS}>
                        <CarryOutOutlined />
                        <a
                          className={styles.linkText}
                          href={`https://mc.alibaba-inc.com/#/MCVersionPlan/detail?appId=${item?.appId}&versionPlanId=${item?.versionPlanId}`}
                          target="_blank"
                        >
                          版本计划 {getConciseVersionName(item.versionPlanName)}
                        </a>
                      </Flex>
                    )}
                  </Flex>
                </Flex>
                {typeof item?.modules?.length === 'number' && item?.modules?.length > 0 && (
                  <Collapse
                    {...(item?.modules[0]?.codeReviews?.length ? { defaultActiveKey: 0 } : {})}
                    bordered={false}
                    items={item?.modules?.map((module, index) => ({
                      ...module,
                      key: index,
                      label: (
                        <Flex className={styles.AiCrItemTitle} gap={token.marginXXS} align="center">
                          <span>{module?.moduleName}</span>
                          <Badge count={module?.codeReviews?.length} size="small" showZero />
                        </Flex>
                      ),
                      children: module?.codeReviews?.map((codeReviewItem) => (
                        <CodeReviewItem
                          key={codeReviewItem?.codeReviewId}
                          recordId={codeReviewItem?.recordId}
                          data={codeReviewItem}
                          spaceId={spaceId}
                          onRefresh={onRefresh}
                          showGateData
                        />
                      )),
                    }))}
                  />
                )}
              </Flex>
            ))}
          </div>
        </Spin>
      )}
      <Pagination
        style={{ marginTop: token.margin }}
        current={pageNum}
        total={totalCount}
        pageSize={pageSize}
        showSizeChanger={false}
        showTotal={(total) => `共有${total}条`}
        onChange={onPaginationChange}
      />
    </Flex>
  );
}
