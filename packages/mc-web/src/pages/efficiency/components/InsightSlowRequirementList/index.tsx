import React, { useEffect, useMemo, useState } from 'react';
import { Col, Flex, Row, Skeleton, Statistic, Table, theme } from 'antd';

import { useRequest } from '@ali/mc-request';
import {
  getSpaceMetricOverview,
  GetSpaceMetricOverviewParams,
} from '@ali/mc-services/Efficiency';
import { MetricComparativeData, SpaceEfficiencyVO, SpaceSlowReqPercentVO } from '@ali/mc-services';
import { Link } from 'ice';
import { ArrowDownOutlined, ArrowUpOutlined, ChevronRightOutlined, PeopleOutlined, SearchInsightOutlined, SortDescOutlined } from '@ali/mc-icons';
import { UnitMap } from '@/util/utils';
import styles from './index.module.less';

type InsightSlowRequirementListProps = {
  applicationId?: number;
  versionPlanId?: number;
};

export default function InsightSlowRequirementList(props: InsightSlowRequirementListProps) {
  const { token } = theme.useToken();

  const [sortType, setSortType] = useState<'SLOW_RATE' | 'DURATION'>('SLOW_RATE');

  const { applicationId, versionPlanId } = props;

  const { runAsync: requestSpaceMetricOverview, data = [], loading } = useRequest<
    SpaceEfficiencyVO[], [GetSpaceMetricOverviewParams]
  >(getSpaceMetricOverview);

  useEffect(() => {
    if (applicationId && versionPlanId) {
      requestSpaceMetricOverview({
        appId: applicationId,
        versionPlanId,
      });
    }
  }, [applicationId, requestSpaceMetricOverview, versionPlanId]);

  const transferHour = (value: number, unit: string | undefined) => {
    if (unit === 'HOUR') {
      return value;
    } else if (unit === 'DAY') {
      return value * 24;
    } else {
      return 0;
    }
  };

  // 慢需求占比从大到小排序
  const slowRateSort = useMemo(() => {
    let sortData: SpaceEfficiencyVO[] = [...data];
    sortData?.sort((item1, item2) => {
      return parseInt(item2?.slowReqPercent?.value as any, 10) - parseInt(item1?.slowReqPercent?.value as any, 10);
    }) || [];
    return sortData;
  }, [data]);

  // 平台全流程耗时从大到小排序
  const durationSort = useMemo(() => {
    let sortData: SpaceEfficiencyVO[] = [...data];
    sortData?.sort((item1, item2) => {
      if (item1?.spaceId && item2?.spaceId) {
        const data2 = item2?.processDuration || {};
        const data1 = item1?.processDuration || {};
        return transferHour(parseFloat(data2?.value as any), data2?.unit) -
          transferHour(parseFloat(data1?.value as any), data1?.unit);
      } else {
        return -1;
      }
    }) || [];
    return sortData;
  }, [data]);

  const items = sortType === 'SLOW_RATE' ? slowRateSort : durationSort;

  const columns = [
    {
      title: '团队',
      dataIndex: 'spaceName',
      key: 'spaceName',
      render: (text: string, record: SpaceSlowReqPercentVO) => {
        return (
          <Flex gap={token.marginXXS} className={styles.title}>
            <PeopleOutlined className={styles.icon} />
            <span>{text}</span>
            <Link
              to={
                `/efficiency/${record.spaceId}/insights?applicationId=${record.appId}&versionPlanId=${record.versionPlanId}&tabKey=slow`
              }
              target={`efficiency-${record.spaceId}`}
              style={{ color: token.colorText }}
            >
              <ChevronRightOutlined />
            </Link>
          </Flex>
        );
      },
    },
    {
      title: (
        <Flex
          gap={token.marginXS}
          align="center"
        >
          <span>慢需求占比</span>
          <SortDescOutlined
            onClick={() => { setSortType('SLOW_RATE'); }}
            style={{
              cursor: 'pointer',
              color: sortType === 'SLOW_RATE' ? token.colorSuccessActive : token.colorTextDescription,
            }}
          />
        </Flex>
      ),
      dataIndex: 'slowReqPercent',
      key: 'slowReqPercent',
      render: (_: MetricComparativeData, record: SpaceEfficiencyVO) => {
        const { slowReqPercent = {} } = record;
        const unit = UnitMap[slowReqPercent?.unit || 'PERCENT'] || slowReqPercent?.unit;
        const hasDiffValue = slowReqPercent?.diffValue !== undefined;

        return (
          <Flex
            align="center"
            gap={token.margin}
            className={styles.metricData}
          >
            <Flex
              style={{ minWidth: '100px' }}
              gap={token.marginXS}
              className={styles.title}
            >
              <Flex style={{ minWidth: '30px' }}>{record?.slowReqPercent?.value as any}</Flex>
              <Flex>
                （{slowRateSort?.findIndex((item: SpaceEfficiencyVO) => item.spaceId === record.spaceId) + 1}/
                {items?.length}）
              </Flex>
            </Flex>
            <Flex
              align="center"
              gap="small"
              style={{
                visibility: slowReqPercent?.comparativeValue ? 'visible' : 'hidden',
              }}
            >
              <span>对比{slowReqPercent?.baseline}</span>
              <Statistic
                value={hasDiffValue ? (slowReqPercent?.diffValue as any) : '-'}
                precision={2}
                valueStyle={
                  (hasDiffValue && slowReqPercent?.flag !== 'EQUAL')
                    ? { color: slowReqPercent?.flag === 'DOWN' ? token.colorSuccess : token.colorError }
                    : undefined
                }
                prefix={
                  (slowReqPercent?.flag && hasDiffValue && slowReqPercent?.flag !== 'EQUAL')
                    ? slowReqPercent.flag === 'UP'
                      ? <ArrowUpOutlined />
                      : <ArrowDownOutlined />
                    : undefined
                }
              />
              <span>
                ({slowReqPercent?.comparativeValue as any} {unit})
              </span>
            </Flex>
          </Flex>
        );
      },
    },
    {
      title: (
        <Flex
          gap={token.marginXS}
          align="center"
        >
          <span>平台全流程耗时</span>
          <SortDescOutlined
            onClick={() => { setSortType('DURATION'); }}
            style={{
              cursor: 'pointer',
              color: sortType === 'DURATION' ? token.colorSuccessActive : token.colorTextDescription,
            }}
          />
        </Flex>
      ),
      dataIndex: 'slowReqPercent',
      key: 'slowReqPercent',
      render: (_: MetricComparativeData, record: SpaceEfficiencyVO) => {
        const { processDuration = {} } = record;
        const duration = processDuration?.value || '-';
        const durationUnit = processDuration?.unit || undefined;
        const unit = UnitMap[processDuration?.unit || 'PERCENT'] || processDuration?.unit;
        const hasDiffValue = processDuration?.diffValue !== undefined;

        return (
          <Flex
            align="center"
            gap={token.margin}
            className={styles.metricData}
          >
            <Flex
              style={{ minWidth: '100px' }}
              gap={token.marginXS}
              className={styles.title}
            >
              <Flex style={{ minWidth: '50px' }}>{duration}{durationUnit && UnitMap[durationUnit || 'DAY']}</Flex>
              <Flex>
                （{durationSort?.findIndex((item: SpaceSlowReqPercentVO) => item.spaceId === record.spaceId) + 1}/
                {items?.length}）
              </Flex>
            </Flex>
            <Flex
              align="center"
              gap="small"
              style={{
                visibility: processDuration?.comparativeValue ? 'visible' : 'hidden',
              }}
            >
              <span>对比{processDuration?.baseline}</span>
              <Statistic
                value={hasDiffValue ? (processDuration?.diffValue as any) : '-'}
                precision={2}
                valueStyle={
                  (hasDiffValue && processDuration?.flag !== 'EQUAL')
                    ? { color: processDuration?.flag === 'DOWN' ? token.colorSuccess : token.colorError }
                    : undefined
                }
                prefix={
                  (processDuration?.flag && hasDiffValue && processDuration?.flag !== 'EQUAL')
                    ? processDuration.flag === 'UP'
                      ? <ArrowUpOutlined />
                      : <ArrowDownOutlined />
                    : undefined
                }
              />
              <span>
                ({processDuration?.comparativeValue as any} {unit})
              </span>
            </Flex>
          </Flex>
        );
      },
    },
  ];

  return (
    <Flex vertical gap={token.margin}>
      <span>
        慢需求占比
      </span>
      <Flex style={{ fontSize: token.fontSizeSM }} justify="space-between">
        <Flex align="center" gap={token.marginXXS}>
          <SearchInsightOutlined />
          <span>慢需求占比TOP3的团队是</span>
          {slowRateSort.slice(0, 3).map((item: SpaceEfficiencyVO, index) => {
            const unit = item?.slowReqPercent?.unit ? UnitMap[item?.slowReqPercent?.unit] : item?.slowReqPercent?.unit;
            return (
              <span key={index}>
                {item.spaceName}({item?.slowReqPercent?.value}{unit})
                {index === 2 ? '，' : '、'}
              </span>
            );
          })}
          <span>请下钻分析各团队慢需求原因并治理。</span>
        </Flex>
      </Flex>
      <Row gutter={[token.marginSM, token.marginSM]}>
        {loading ? (
          <Col span={6} style={{ height: '109px', overflowY: 'hidden' }}>
            <Skeleton active title={false} paragraph={{ rows: 3 }} />
          </Col>
        ) : (
          <Table
            bordered
            dataSource={items || []}
            columns={columns}
            className={styles.table}
            pagination={false}
          />
        )}
      </Row>

    </Flex>
  );
}
