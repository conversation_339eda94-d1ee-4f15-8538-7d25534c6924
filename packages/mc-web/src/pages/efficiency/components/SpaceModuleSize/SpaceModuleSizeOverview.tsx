import React, { useState, useEffect } from 'react';
import {
  Flex,
  Space,
  Divider,
  Card,
  theme,
  Tooltip,
  Tabs,
  Table,
  Pagination,
  type TableProps,
} from 'antd';
import { Line } from '@ant-design/plots';
import { QuestionCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useRequest } from '@ali/mc-request';
import { getSpaceLastReleaseModuleSizes, querySpaceModuleSize } from '@ali/mc-services/ModuleSize';
import { CollaborationSpaceModuleSizeVO } from '@ali/mc-services/DataContracts';
import { PrefixSelect, TaoTianApplicationSelect, InlineButton } from '@ali/mc-uikit';
import styles from './index.module.less';
import {
  VERSION_TAB_ITEMS,
  APP_VERSION_LABEL_MAP,
  VERSION_TAB_KEY,
  MODULE_LINE_SELECT_ITEMS,
  VERSION_TAB_TYPE_MAP,
  MODULE_SIZE_APPLY_STATUS_MAP,
} from '@/constants/efficiency';
import { getNumberWithUnit, getTextTokenColor, transferToPercentage } from '@/util/number';
import BalanceAuditModal from '../BalanceAuditModal';
import type { IQuerySpaceData } from '../../types/index';
import { getConciseVersionName } from '../../common/efficiency';
import { getFullPathWithContext } from '../../common/searchParams';
import { useGateRecordContext } from '../../components/GateRecordContext';
import BizSpaceSelect from '../SpaceSelect/BizSpaceSelect';

const DEFAULT_LINE_SELECT = 'moduleSizeBalance';
const DEFAULT_LINE_CONFIG = {
  height: 110,
  xField: 'appVersion',
  yField: 'moduleSizeBalance',
  autoFit: true,
  axis: {
    // eslint-disable-next-line id-length
    y: {
      labelFormatter: (size: number) => getNumberWithUnit(size),
    },
  },
  tooltip: {
    items: [
      { channel: 'x', name: '版本' },
      { channel: 'y', name: '包大小余额' },
    ],
  },
};
const DEFAULT_PAGE_NUM = 1;
const DEFAULT_PAGE_SIZE = 10;


interface SpaceModuleSizeOverviewProps {
  onItemClick: (record?: IQuerySpaceData) => void;
  spaceId?: number;
  bizSpaceId?: number;
  versionTabKey?: VERSION_TAB_KEY;
  appId?: number;
  onChange?: ({ versionTabKey, appId }: { versionTabKey?: VERSION_TAB_KEY; appId?: number }) => void;
  onBizSpaceIdChange?: (value?: number) => void;
}

interface OverviewListItem extends CollaborationSpaceModuleSizeVO {
  lineSelect: string;
  lineConfig: {
    height: number;
    xField: string;
    yField: string;
  };
}

export default function SpaceModuleSizeOverview(props: SpaceModuleSizeOverviewProps) {
  const {
    spaceId,
    bizSpaceId,
    appId: currentAppId,
    versionTabKey: currentVersionTabKey,
    onItemClick = () => {},
    onChange = () => {},
    onBizSpaceIdChange = () => {},
  } = props;
  const { token } = theme.useToken();
  const { recordData } = useGateRecordContext();
  const [currentPageNum, setCurrentPageNum] = useState(DEFAULT_PAGE_NUM);
  const [overviewList, setOverviewList] = useState<OverviewListItem[]>([]);
  const [isAdjustBalanceModalOpen, setIsAdjustBalanceModalOpen] = useState(false);
  const [modalItem, setModalItem] = useState<OverviewListItem>();
  const { runAsync: requestSpaceLastReleaseModuleSizes } = useRequest(getSpaceLastReleaseModuleSizes);
  const {
    runAsync: requestSpaceModuleSize,
    loading: spaceModuleSizeLoading,
    data: spaceModuleSizeData,
  } = useRequest(querySpaceModuleSize);

  const getLineColumns = (tabKey: VERSION_TAB_KEY) => {
    const lineColumns: TableProps['columns'] = [
      {
        title: APP_VERSION_LABEL_MAP[tabKey].title,
        dataIndex: APP_VERSION_LABEL_MAP[tabKey].dataIndex,
        render: (value) => (
          <Flex>
            {tabKey === VERSION_TAB_KEY.PIPELINE_INSTANCE_SLIMMING_REQUEST ? getConciseVersionName(value) : value}
          </Flex>
        ),
      },
      {
        title: '包大小',
        dataIndex: 'moduleSize',
        key: 'moduleSizeAndRate',
        render: (value) => <Flex>{getNumberWithUnit(value)}</Flex>,
      },
      {
        title: '包大小余额',
        dataIndex: 'moduleSizeBalance',
        render: (value) => <Flex style={{ color: token[getTextTokenColor(value)] }}>{getNumberWithUnit(value)}</Flex>,
      },
      {
        title: (
          <Flex gap={token.marginXXS}>
            实际优化
            <Tooltip title="当前项目所有模块在本次版本中实际共优化的包大小。">
              <QuestionCircleOutlined />
            </Tooltip>
          </Flex>
        ),
        dataIndex: 'moduleSizeDiff',
        render: (value) => <Flex style={{ color: token[getTextTokenColor(value)] }}>{getNumberWithUnit(value)}</Flex>,
      },
      {
        title: (
          <Flex gap={token.marginXXS}>
            架构回收
            <Tooltip title="架构组会按照比例回收一部分包大小优化额度，每个项目具体比例由架构组配置。">
              <QuestionCircleOutlined />
            </Tooltip>
          </Flex>
        ),
        dataIndex: 'moduleSizeRecycle',
        render: (value) => <Flex style={{ color: token[getTextTokenColor(value)] }}>{getNumberWithUnit(value)}</Flex>,
      },
      {
        title: (
          <Flex gap={token.marginXXS}>
            贡献余额
            <Tooltip title="贡献余额 = 实际优化 - 架构回收">
              <QuestionCircleOutlined />
            </Tooltip>
          </Flex>
        ),
        dataIndex: 'moduleSizeDiffAfterRecycle',
        render: (value) => <Flex style={{ color: token[getTextTokenColor(value)] }}>{getNumberWithUnit(value)}</Flex>,
      },
      {
        title: tabKey === VERSION_TAB_KEY.PIPELINE_INSTANCE_SLIMMING_REQUEST ? '变更模块' : '相关模块',
        dataIndex: 'moduleNum',
      },
      {
        title: '统计时间',
        dataIndex: 'gmtModified',
        render: (value) => <Flex>{dayjs(value)?.format('YYYY-MM-DD HH:mm:ss')}</Flex>,
      },
      {
        title: '操作',
        dataIndex: 'action',
        render: (_value, item) => (
          <Space>
            <InlineButton
              type="text"
              onClick={() =>
                onItemClick({
                  ...item,
                  spaceModuleSizeId: item?.id,
                  type: VERSION_TAB_TYPE_MAP[
                    currentVersionTabKey ?? VERSION_TAB_KEY.PIPELINE_INSTANCE_SLIMMING_REQUEST
                  ],
                  appId: currentAppId,
                })}
            >
              模块列表
            </InlineButton>
            {tabKey === VERSION_TAB_KEY.RELEASE_SLIMMING_REQUEST && (
              <InlineButton
                type="text"
                href={getFullPathWithContext({
                  path: `/#/efficiency/${spaceId}/overview`,
                  searchParams: {
                    tabKey: 'module-size',
                    appId: currentAppId,
                    appVersion: item?.appVersion,
                  },
                  context: recordData,
                })}
              >
                团队包大小视图
              </InlineButton>
            )}
          </Space>
        ),
      },
    ];
    return lineColumns;
  };

  const updateSpaceModuleSize = async (data: {
    versionTabKey?: VERSION_TAB_KEY;
    appId?: number;
    pageNum?: number;
    bizSpaceId?: number;
  }) => {
    const { versionTabKey = currentVersionTabKey, appId = currentAppId, pageNum = currentPageNum } = data;
    if (data?.bizSpaceId || bizSpaceId) {
      requestSpaceModuleSize(
        {
          pageNum,
          pageSize: DEFAULT_PAGE_SIZE,
        },
        {
          appId,
          spaceId: data?.bizSpaceId ?? bizSpaceId,
          // 是否是进行中版本
          ...(versionTabKey === VERSION_TAB_KEY.PIPELINE_INSTANCE_SLIMMING_REQUEST
            ? {
                versionPlanIdNotNull: true,
                releaseIdNotNull: false,
              }
            : {
                versionPlanIdNotNull: false,
                releaseIdNotNull: true,
              }),
        },
      );
    }
  };

  const updateOverviewListData = async (data: { bizSpaceId?: number }) => {
    const overviewListResp = await requestSpaceLastReleaseModuleSizes({
      spaceId: data?.bizSpaceId ?? bizSpaceId ?? 0,
    });
    setOverviewList(
      (overviewListResp ?? [])?.map((item) => ({
        ...item,
        lineSelect: DEFAULT_LINE_SELECT,
        lineConfig: DEFAULT_LINE_CONFIG,
      })),
    );
  };

  const onLinePrefixSelectChange = (index: number, value: string) => {
    const newOverviewList = [...overviewList];
    newOverviewList[index] = {
      ...newOverviewList[index],
      lineSelect: value,
      lineConfig: {
        ...newOverviewList[index].lineConfig,
        yField: value,
      },
    };
    setOverviewList(newOverviewList);
  };

  const onTabsChange = (key: VERSION_TAB_KEY) => {
    updateSpaceModuleSize({ versionTabKey: key });
    onChange({ versionTabKey: key });
  };

  const onAppSelectChange = (selectedAppId: number) => {
    updateSpaceModuleSize({ appId: selectedAppId });
    onChange({ appId: selectedAppId });
  };

  const showAdjustBalanceModal = (item: OverviewListItem) => {
    setModalItem(item);
    setIsAdjustBalanceModalOpen(true);
  };

  const openAdjustBalanceAuditUrl = (item: OverviewListItem) => {
    window.open(item?.moduleSizeApplyUrl);
  };

  const onActionClick = (action: { isShowAdjustModal?: boolean }, overviewItem: OverviewListItem) => {
    if (action.isShowAdjustModal) {
      showAdjustBalanceModal(overviewItem);
    } else {
      openAdjustBalanceAuditUrl(overviewItem);
    }
  };

  const onPaginationChange = (page: number) => {
    setCurrentPageNum(page);
    updateSpaceModuleSize({ pageNum: page });
  };

  const onBizSelectChange = (value?: number) => {
    onBizSpaceIdChange(value);
    updateOverviewListData({ bizSpaceId: value });
    updateSpaceModuleSize({ pageNum: DEFAULT_PAGE_NUM, bizSpaceId: value });
  };

  useEffect(() => {
    updateSpaceModuleSize({ pageNum: DEFAULT_PAGE_NUM });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentAppId, currentVersionTabKey]);
  return (
    <Flex className={styles.SpaceModuleSizeOverview} vertical gap="middle">
      <Flex justify="space-between" className={styles.header}>
        <span>项目包大小</span>
        <BizSpaceSelect value={bizSpaceId} spaceId={spaceId} onChange={onBizSelectChange} />
      </Flex>
      <Divider className={styles.divider} />
      {overviewList?.length > 0 && (
        <Card style={{ width: '100%' }}>
          <Flex gap={token.marginXXS} align="center" className={styles.cardHeader}>
            总览
          </Flex>
          <Flex>
            {overviewList.map((item, index) => (
              <Flex
                vertical
                className={styles.overviewItem}
                key={index}
                style={{ width: transferToPercentage(1 / overviewList.length, false) }}
              >
                <Flex gap={token.marginXXS} align="center" className={styles.cardHeader}>
                  {item.appName}
                </Flex>
                <Flex vertical className={styles.overviewItemContent} gap={16}>
                  <Flex vertical gap={8}>
                    <Flex align="center" gap={token.marginXXS} className={styles.overviewItemLabel}>
                      <span>包大小余额</span>
                      <span>:</span>
                      <span
                        style={{
                          color:
                            typeof item.moduleSizeBalance === 'number' && item.moduleSizeBalance >= 0
                              ? token.colorSuccess
                              : token.colorError,
                        }}
                      >
                        {getNumberWithUnit(item.moduleSizeBalance)}
                      </span>
                      {MODULE_SIZE_APPLY_STATUS_MAP.find((config) =>
                        config.statusList.includes(item.moduleSizeApplyStatus ?? 'CANCELED'),
                      )?.actions.map((action) => (
                        <a
                          key={action?.label}
                          color={action.label}
                          className={styles.overviewItemAction}
                          onClick={() => onActionClick(action, item)}
                        >
                          {action.label}
                        </a>
                      ))}
                    </Flex>
                    <Flex gap={token.marginXXS} className={styles.overviewItemLabel}>
                      <span>架构回收</span>
                      <Tooltip title="架构组会按照比例回收一部分包大小优化额度，每个项目具体比例由架构组配置。">
                        <QuestionCircleOutlined />
                      </Tooltip>
                      <span>:</span>
                      <span
                        style={{
                          color:
                            typeof item.moduleSizeRecycle === 'number' && item.moduleSizeRecycle >= 0
                              ? token.colorSuccess
                              : token.colorError,
                        }}
                      >
                        {getNumberWithUnit(item.moduleSizeRecycle)}
                      </span>
                    </Flex>
                  </Flex>
                  <PrefixSelect
                    style={{ width: 'fit-content' }}
                    prefix="趋势图："
                    defaultValue={item.lineSelect}
                    options={MODULE_LINE_SELECT_ITEMS}
                    onChange={(value) => onLinePrefixSelectChange(index, value)}
                  />
                  <Line
                    {...item.lineConfig}
                    data={item.moduleSizeTrend?.map((trendItem) => ({
                      ...trendItem,
                      moduleSizeDiff: trendItem.moduleSizeDiff ?? 0,
                    }))}
                  />
                </Flex>
              </Flex>
            ))}
          </Flex>
        </Card>
      )}
      <Tabs
        style={{ width: '100%' }}
        type="card"
        activeKey={currentVersionTabKey}
        onChange={(key) => onTabsChange(key as VERSION_TAB_KEY)}
        tabBarExtraContent={
          <TaoTianApplicationSelect prefix="客户端：" value={currentAppId} onSelect={onAppSelectChange} />
        }
        items={VERSION_TAB_ITEMS?.map((item) => ({
          ...item,
          children: (
            <Flex vertical align="center" key={item.key}>
              <Table
                loading={spaceModuleSizeLoading}
                style={{ width: '100%' }}
                columns={getLineColumns(item.key as VERSION_TAB_KEY)}
                dataSource={spaceModuleSizeData?.items?.map((tableItem, index) => ({ ...tableItem, key: index }))}
                pagination={false}
              />
              <Pagination
                style={{ marginTop: token.margin }}
                defaultCurrent={currentPageNum}
                total={spaceModuleSizeData?.totalCount}
                pageSize={DEFAULT_PAGE_SIZE}
                showSizeChanger={false}
                showTotal={(total) => `共有${total}条`}
                onChange={onPaginationChange}
              />
            </Flex>
          ),
        }))}
      />
      <BalanceAuditModal
        open={isAdjustBalanceModalOpen}
        data={modalItem}
        success={(url, data) => {
          if (url) {
            setOverviewList(
              overviewList.map((item) =>
                (data?.appId === item.appId
                  ? {
                      ...item,
                      moduleSizeApplyStatus: 'IN_APPROVAL',
                      moduleSizeApplyUrl: url,
                    }
                  : item),
              ),
            );
          }
        }}
        onChange={(open) => setIsAdjustBalanceModalOpen(open)}
      />
    </Flex>
  );
}
