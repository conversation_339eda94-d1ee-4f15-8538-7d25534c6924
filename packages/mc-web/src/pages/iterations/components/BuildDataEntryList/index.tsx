import React, { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.module.less';
import {
  calculateInterval,
  GitCommitBO,
  PipelineInstanceExecuteSummary,
  TaskErrorSolution,
  TaskInstanceExecuteSummary,
} from '@ali/mc-services';
import { Flex, theme } from 'antd';
import { Copy, DataEntryList, ListItem } from '@ali/mc-uikit';
import { CopyOutlined, TemplateOutlined, VersionsOutlined } from '@ali/mc-icons';
import CommitPopover from '@/components/CommitPopover';
import BuildStatusTag, { BUILD_STATUS_TYPE } from '@/components/BuildStatusTag';
import { BundleItems } from '@/components/BundleItem';
import ErrorTooltip from '../ErrorTooltip';
import BuildLogDrawer from '../BuildLogDrawer';
import { useRequest } from '@ali/mc-request';
import { queryPipelineInstanceExecuteSummary } from '@ali/mc-services/PipelineExecute';
import EmptyCard from '../EmptyCard';

export interface ItemsProps {
  buildVersion?: string;
  coordinators?: string;
  errorMsg?: string;
  fileInfos?: any[];
  fileSize?: number;
  fileType?: string;
  jobEndTime?: number;
  id?: number;
  jobInstanceId?: number;
  jobName?: string;
  jobNameForDropdown?: string;
  jobStartTime?: number;
  logAnalyzeResultId?: number;
  name?: string;
  rootCauseArr?: any[];
  status?: BUILD_STATUS_TYPE;
  scmTag?: string;
  gitCommit?: GitCommitBO;
  aiErrorMsg?: string | null;
  taskExecuteSummaries?: TaskInstanceExecuteSummary[];
  taskErrorSolution?: TaskErrorSolution;
  stageId?: number;
}

// TODO: 这段处理数据的逻辑是直接从老代码copy过来的，等逻辑熟悉了之后最好重写一下
const getBuildData = ({
  data,
  isMainFrameworkDeploy,
  buildType,
}: {
  data: PipelineInstanceExecuteSummary;
  isMainFrameworkDeploy: boolean;
  buildType: 'appBuild' | 'moduleBuild' | 'mainFrameworkBuild';
}) => {
  const result: ItemsProps[] = [];

  (data?.stageExecuteSummaries ?? []).forEach((stageExecuteSummary) => {
    // buildType tab切换值  moduleBuild === 模块包构建  build === 整包构建
    if (
      ((buildType === 'moduleBuild' || buildType === 'mainFrameworkBuild') && stageExecuteSummary.appType === 'MODULE') ||
      (buildType === 'appBuild' &&
        stageExecuteSummary.appType === 'CLIENT' &&
        data?.pipelineType !== 'Pipeline-Type-Module')
    ) {
      (stageExecuteSummary?.jobExecuteSummaries ?? []).forEach((jobExecuteSummary) => {
        const {
          id,
          jobExecuteStatus,
          buildVersion,
          jobStartTime,
          jobEndTime,
          appName,
          name,
          gitCommit,
          taskExecuteSummaries,
          taskErrorSolution,
        } = jobExecuteSummary;
        // 壳工程发布没有产物 只有scmTag
        if (isMainFrameworkDeploy && jobExecuteSummary.jobType === 'NORMAL') {
          result.push({
            id,
            jobInstanceId: id,
            jobName: name,
            buildVersion,
            scmTag: jobExecuteSummary.scmTag,
            status: jobExecuteStatus,
            jobEndTime,
            jobStartTime,
            gitCommit,
            taskExecuteSummaries,
            taskErrorSolution,
            stageId: stageExecuteSummary?.id,
          });
        } else if (jobExecuteStatus !== 'SKIP_SUCCEEDED') {
          // https://project.aone.alibaba-inc.com/v2/project/951276/req/63534287
          // 主产物和过程产物不再分开展示，统一展示在同一个弹窗
          const fileInfos: any = [];
          (jobExecuteSummary.jobFileInfos ?? [])
            .forEach((jobFileInfo) => {
              fileInfos.push({
                ...jobFileInfo,
                jobName: stageExecuteSummary?.name || jobExecuteSummary.name,
                jobNameForDropdown: jobExecuteSummary.name,
                buildVersion: jobExecuteSummary.buildVersion,
                scmTag: jobExecuteSummary.scmTag,
                jobEndTime: jobExecuteSummary.jobEndTime,
                jobStartTime: jobExecuteSummary.jobStartTime,
                jobInstanceId: jobExecuteSummary.id,
                status: jobExecuteSummary.jobExecuteStatus,
              });
            });

          const fileInfo = fileInfos && fileInfos.length > 0 ? fileInfos[0] : {};

          if (jobExecuteSummary.jobType !== 'GATE') {
            let errorMsg = taskErrorSolution?.bestMatchKnowledgeSolution?.errorSolutionChatInfo?.answer ||
            taskErrorSolution?.bestMatchKnowledgeSolution?.solution?.errorSolution;

            let aiErrorMsg = taskErrorSolution?.agentsInteractions?.result ?? null;
            result.push({
              id,
              jobInstanceId: id,
              status: jobExecuteStatus,
              buildVersion,
              jobStartTime,
              jobEndTime,
              name: appName,
              jobName: stageExecuteSummary?.name || name,
              jobNameForDropdown: name,
              logAnalyzeResultId: taskErrorSolution?.logAnalyzeResultId,
              errorMsg,
              aiErrorMsg,
              fileType: fileInfo.fileType,
              fileSize: fileInfo.fileSize,
              fileInfos,
              gitCommit,
              rootCauseArr: taskErrorSolution?.rootCauseDetail?.rootCause
                ? taskErrorSolution?.rootCauseDetail?.rootCause.split(',')
                : [],
              coordinators: taskErrorSolution?.rootCauseDetail?.coordinators || '',
              taskExecuteSummaries,
              taskErrorSolution,
              stageId: stageExecuteSummary?.id,
              scmTag: jobExecuteSummary?.scmTag,
            });
          }
        }
      });
    }
  });
  return result;
};

export interface BuildDataEntryListProps {
  isMainFrameworkDeploy: boolean;
  pipelineInstanceId?: number;

  /**
   * 模块包构建moduleBuild / 整包构建appBuild
   */
  buildType: 'appBuild' | 'moduleBuild' | 'mainFrameworkBuild';
  title?: ReactNode;
  extra?: ReactNode;
  pipelineInstanceExecuteSummary: PipelineInstanceExecuteSummary;
  refreshPipelineInstanceExecuteSummary: () => Promise<any>;
  pageType?: string;
  mainFrameworkName? : string
}

const BuildDataEntryList = ({
  pipelineInstanceId,
  isMainFrameworkDeploy,
  buildType,
  pipelineInstanceExecuteSummary,
  refreshPipelineInstanceExecuteSummary,
  title,
  extra,
  pageType,
  mainFrameworkName,
}: BuildDataEntryListProps) => {
  const { token } = theme.useToken();
  // 这里的请求是临时兼容一下DevTestInfoList组件中的引用（pipelineInstanceId存在），后续应该还是要把请求逻辑移到父组件
  const {
    runAsync: requestInstanceExecuteSummary,
    loading: instanceExecuteSummaryLoading,
    refreshAsync: refreshInstanceExecuteSummary,
  } = useRequest<PipelineInstanceExecuteSummary, [number]>(queryPipelineInstanceExecuteSummary);

  const [instanceExecuteSummary, setInstanceSummary] = useState<PipelineInstanceExecuteSummary>({});

  const getInstanceExecuteSummary = useCallback(
    async (pipelineInstanceId_: number) => {
      const res = await requestInstanceExecuteSummary(pipelineInstanceId_);
      if (res) {
        setInstanceSummary(res);
      }
    },
    [requestInstanceExecuteSummary],
  );

  useEffect(() => {
    if (pipelineInstanceId) {
      getInstanceExecuteSummary(pipelineInstanceId);
    }
  }, [pipelineInstanceId, getInstanceExecuteSummary]);

  const dataSource = useMemo(() => {
    return getBuildData({
      data: pipelineInstanceId ? instanceExecuteSummary : pipelineInstanceExecuteSummary ?? {},
      isMainFrameworkDeploy,
      buildType,
    });
  }, [pipelineInstanceExecuteSummary, isMainFrameworkDeploy, buildType, pipelineInstanceId, instanceExecuteSummary]);
  return (
    <DataEntryList
      locale={{ emptyText: <EmptyCard description="暂无数据" /> }}
      key={pipelineInstanceId || pipelineInstanceExecuteSummary?.id}
      loading={pipelineInstanceId ? instanceExecuteSummaryLoading : false}
      title={title}
      className={styles.buildDataEntryList}
      dataSource={pageType === 'kmpDevelopment' ? dataSource?.filter(item => {
        // KMP 跨端迭代 壳模块构建tab只显示壳模块name和当前name相同的构建模块
        if (buildType === 'mainFrameworkBuild') {
          return item?.name === mainFrameworkName;
        }
        // KMP 跨端迭代 模块构建tab不显示壳模块name和当前name相同的构建模块
        if (buildType === 'moduleBuild') {
          return item?.name !== mainFrameworkName;
        }
        return item;
      }) : dataSource}
      extra={extra}
      renderItem={(item) => {
        const isRightHigher = item?.fileInfos !== undefined && item.fileInfos.length > 3;
        return (
          <ListItem
            className={isRightHigher ? styles.leftAlignSelfStyle : ''}
            key={item.id}
            title={
              <Flex gap="small" align="center" wrap>
                {(buildType === 'moduleBuild' || buildType === 'mainFrameworkBuild') ? item?.name : item?.jobName}
                {item?.status && <BuildStatusTag status={item.status} />}
                {item?.errorMsg && (buildType === 'appBuild' || buildType === 'moduleBuild' || buildType === 'mainFrameworkBuild') && <ErrorTooltip record={item} />}
              </Flex>
            }
            extra={
              <Flex align="center" gap={2 * token.marginXL}>
                <BundleItems fileInfos={item?.fileInfos ?? []} />
                <Flex gap="small">
                  <BuildLogDrawer
                    stageExecuteSummaries={
                      pipelineInstanceId
                      ? instanceExecuteSummary?.stageExecuteSummaries
                      : pipelineInstanceExecuteSummary?.stageExecuteSummaries
                    }
                    instanceId={pipelineInstanceId || pipelineInstanceExecuteSummary?.id}
                    activeStageId={item?.stageId || 0}
                    refreshPipelineInstanceExecuteSummary={
                      pipelineInstanceId ? refreshInstanceExecuteSummary : refreshPipelineInstanceExecuteSummary
                    }
                  />
                </Flex>
              </Flex>
            }
            showActions
          >
            <Flex vertical>
              <Flex className={styles.info}>
                {item?.jobInstanceId && (
                <Flex align="center">
                  {`#${item?.jobInstanceId}`}
                  <Copy text={String(item?.jobInstanceId)}>
                    <CopyOutlined style={{ fontSize: token.fontSize }} />
                  </Copy>
                </Flex>
              )}
                {item?.jobEndTime && item?.jobStartTime && (
                <Flex>
                  <span>构建时长</span>
                  {calculateInterval(item?.jobEndTime, item?.jobStartTime)}
                </Flex>
              )}
              </Flex>
              <Flex wrap>
                {
                (buildType === 'moduleBuild' || buildType === 'mainFrameworkBuild') && item?.jobName && <Flex>
                  <TemplateOutlined />
                  {item?.jobName}
                </Flex>
              }
                {item?.buildVersion && (
                <Flex>
                  <VersionsOutlined />
                  {item?.buildVersion}
                </Flex>
              )}
                {item?.gitCommit && (
                <Flex>
                  <CommitPopover gitCommit={item?.gitCommit} scmTag={item?.scmTag} />
                </Flex>
              )}
              </Flex>
            </Flex>
          </ListItem>
      );
}}
    />
  );
};

export default BuildDataEntryList;
