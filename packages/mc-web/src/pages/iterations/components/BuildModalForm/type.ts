import { PipelineExecuteParams } from '@ali/mc-services';
import React, { ReactNode } from 'react';

export interface BuildModalFormProps{
    pipelineId: number;
    pipelineBuildType?: string;
    deployForIntegration?: boolean;
    scope?: string;
    alterSheetId?: number;
    refreshPipelineInstanceList: () => any;
    buttonStyle?: React.CSSProperties;
    pageType?: string;
    isRunDisabled?: boolean;
    spaceId?: number;
    btnType?: 'default' | 'inline';
    btnText?: ReactNode;
    applicationId?: number;
  }

export interface PipelineExecuteParamsWithUuid extends PipelineExecuteParams {
    uuid: string;
}

export interface BuildModalContentProps extends BuildModalFormProps{
    open: boolean;
    onCancel: () => void;
}
