/* eslint-disable @typescript-eslint/member-delimiter-style */
import React from 'react';
import { Checkbox, Flex, theme, Card, List, Popover } from 'antd';
import styles from '../DevIntegration/IntegrationModuleCheckboxList.module.less';
import { ALTER_TYPE, ALTER_MODE, BRANCH_MODEL_TYPE } from '@/constants/devIntegration';
import { AlterSheetModuleBO } from '@ali/mc-services';
import { PrefixSelect } from '@ali/mc-uikit';
import { QuestionOutlined } from '@ali/mc-icons';

interface IntegrationModuleCheckboxProps {
  setModuleList: (moduleList: AlterSheetModuleBO[]) => void;
  moduleList: AlterSheetModuleBO[];
}

const IntegrationModuleCheckboxList = (props: IntegrationModuleCheckboxProps) => {
  const { token } = theme.useToken();
  const { setModuleList, moduleList } = props;

  const handleChange = (type: string, value: any, id?: number) => {
    const newModuleList = moduleList.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          [type]: value,
        };
      }
      return item;
    });
    setModuleList(newModuleList);
  };

  return (
    <Flex vertical className={styles.IntegrationModuleCheckboxList}>
      <List
        className={styles.list}
        grid={{ column: 1 }}
        dataSource={moduleList}
        renderItem={(item) => {
          const {
            id,
            branchModel,
            alterType,
            deployVersions,
            selectVersion,
            alterMode,
            checked,
          } = item;
          return (
            <List.Item key={item.id}>
              <Card
                size="small"
                title={
                  <Flex justify="space-between" className={styles.cardTitleWrap}>
                    <Flex align="center">
                      <Checkbox
                        checked={checked}
                        onChange={e => handleChange('checked', e.target.checked, item.id)}
                      />
                      <span style={{ marginLeft: token.marginXS }}>{item?.module?.name}</span>
                    </Flex>
                    <Flex gap={token.marginXS}>
                      <Flex style={{ maxWidth: 300 }}>
                        {`${ALTER_MODE[alterMode as string]}(${ALTER_TYPE[alterType as string]})`}
                      </Flex>
                      <Flex align="center" >
                        {
                          branchModel
                            ? <Flex gap={token.marginXXS} align="center" >
                              <span>{BRANCH_MODEL_TYPE[branchModel]}</span>
                              {branchModel === 'INTEGRATION' && (
                                <Popover
                                  content={
                                    <span>
                                      1.模块的代码分支由平台规范化管理，模块的代码分支拉取和合并等在平台上操作，模块发布正式版本之前以及模块集成成功之后需要进行代码合并 <br />
                                      2.“集成版本号”必须是由集成分支或者集成区发布分支所发布的正式版本号。<br />
                                      <a href={'https://yuque.antfin-inc.com/mtl4/product/qwa8b6#WT2oM'} target="_blank" rel="noopener noreferrer">
                                        {'点击查看详情>>'}
                                      </a>
                                    </span>
                                  }
                                >
                                  <QuestionOutlined />
                                </Popover>
                              )}
                            </Flex>
                            : <Flex gap={token.marginXXS}>
                              <span>无分支模式</span>
                              <Popover
                                content={
                                  <span>
                                    1. 默认为无分支模式，即不开启“集成分支模式”，模块的代码分支由用户线下自行管理，代码分支拉取、代码合并等均线下手动操作。<br />
                                    2. 开启“集成分支模式”，模块的代码分支由平台规范化管理，模块的代码分支拉取和合并等在平台上操作，
                                    模块发布正式版本之前以及模块集成成功之后需要进行代码合并。<br />
                                    3. 如果需要修改模块的分支管理模式，可到模块的基本配置内修改。<br />
                                    <a href={'https://yuque.antfin-inc.com/mtl4/product/qwa8b6#Knnb8'} target="_blank" rel="noopener noreferrer">
                                      {'点击查看详情>>'}
                                    </a>
                                  </span>
                                }
                              >
                                <QuestionOutlined />
                              </Popover>
                            </Flex>
                        }
                      </Flex>
                    </Flex>
                  </Flex>
                }
                style={{ width: '100%' }}
              >
                <Flex vertical gap={token.marginXS} flex={1}>
                  <Flex align="center">
                    <span className={styles.selectTitle}>集成版本：</span>
                    <PrefixSelect
                      disabled={alterType === 'BINARY'}
                      style={{ width: '600px' }}
                      onChange={(value) => {
                        handleChange('selectVersion', value, id);
                      }}
                      value={selectVersion}
                      options={
                        deployVersions?.map((versionCode: string) => ({ label: versionCode, value: versionCode }))
                      }
                    />
                  </Flex>
                </Flex>
              </Card>
            </List.Item>
          );
        }}
      />
    </Flex>
  );
};

export default IntegrationModuleCheckboxList;
