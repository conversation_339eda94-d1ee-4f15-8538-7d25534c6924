import React, { useState } from 'react';
import { But<PERSON>, Drawer } from 'antd';
import IntegrationForm from './IntegrationForm';

interface IProps {
  alterSheetDetail: any;
  refresh?: () => void;
  executedCompleted?: boolean;
}

const IntegrationDrawer = ({ alterSheetDetail, refresh, executedCompleted }: IProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const handleOpen = () => {
    setOpen(true);
  };

  return (<>
    <Button disabled={!executedCompleted} type="primary" onClick={handleOpen}>
      去集成
    </Button>
    <Drawer
      width={1100}
      open={open}
      onClose={() => { setOpen(false); }}
      destroyOnClose
      maskClosable={false}
      title="集成 klib"
    >
      <IntegrationForm
        alterSheetDetail={alterSheetDetail}
        onClose={() => { setOpen(false); }}
        onReload={refresh}
      />
    </Drawer>
  </>);
};
export default IntegrationDrawer;
