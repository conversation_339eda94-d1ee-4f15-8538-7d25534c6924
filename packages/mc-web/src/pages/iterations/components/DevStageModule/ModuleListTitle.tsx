import { Flex, theme } from 'antd';
import React from 'react';
import styles from './index.module.less';
import { PackageOutlined, ShellProjectOutlined } from '@ali/mc-icons';
import classNames from 'classnames';

interface ModuleListTitleProps {
  scope: string;
  setTab: (v: 'module' | 'mainFramework') => void;
  tab: 'module' | 'mainFramework';
  mainFrameworkListCount: number;
  mainModuleCount: number;
  sourceModuleCount: number;
  binaryModuleCount: number;
}
const ModuleListTitle = React.memo((props: ModuleListTitleProps) => {
  const {
    scope,
    tab,
    setTab,
    mainModuleCount,
    sourceModuleCount,
    binaryModuleCount,
    mainFrameworkListCount,
  } = props;
  const { token } = theme.useToken();
  if (scope === 'BIZ_DYNAMIC_DEVELOPMENT') {
    return (
      <Flex align="center" className={styles.titleWrap}>
        <Flex
          align="center"
          style={{
            fontSize: token.fontSize,
            fontWeight: token.fontWeightStrong,
            minHeight: token.controlHeight,
          }}
        >
          迭代模块
        </Flex>
      </Flex>

    );
  }
  return (
    <Flex className={styles.titleWrap} align="center">
      <Flex
        gap={token.margin}
        style={{
          minHeight: token.controlHeight,
        }}
      >
        <Flex
          gap={token.marginXXS}
          align="center"
          onClick={() => {
            setTab('module');
          }}
          className={classNames(styles.moduleListTab, tab === 'module' && styles.activeTab)}
        >
          <PackageOutlined />
          <span>
            {mainModuleCount} 个模块
            {mainModuleCount > 0 &&
              `（${sourceModuleCount > 0 ? `${sourceModuleCount} 个源码依赖${binaryModuleCount > 0 ? '，' : ''}` : ''}${binaryModuleCount > 0 ? `${binaryModuleCount} 个外部依赖` : ''}）`}
          </span>
        </Flex>
        {scope !== 'KMP_DEVELOPMENT' && (
          <Flex
            gap={token.marginXXS}
            align="center"
            onClick={() => {
              setTab('mainFramework');
            }}
            className={classNames(styles.moduleListTab, tab === 'mainFramework' && styles.activeTab)}
          >
            <ShellProjectOutlined />
            <span>{mainFrameworkListCount} 个壳工程</span>
          </Flex>
        )}
      </Flex>
    </Flex>
  );
});

export default ModuleListTitle;
