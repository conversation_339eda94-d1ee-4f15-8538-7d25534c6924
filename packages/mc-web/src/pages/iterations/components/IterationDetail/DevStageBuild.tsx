import { useIterationContext } from '@/components/Iteration/IterationProvider';
import { Checkbox, Flex, Skeleton, theme, Alert } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './iterationDetail.module.less';
import { InfoOutlined, StopOutlined } from '@ali/mc-icons';
import { useDevStageBuildContext } from '../DevStageBuild/DevStageBuildProvider';
import PipelineStatusOverview from '../DevStageBuild/PipelineStatusOverview';
import { useSearchParams } from 'ice';
import PipelineCollapse from '../DevStageBuild/PipelineCollapse';
import { findMostRecentPipelineId, isClosed, isFinished } from '../../common/utils';
import ModulesListAiCrContentModal from './ModulesListAiCrContentModal';
import { findUnPassModule, type FindUnPassModuleParams } from '@ali/mc-services/ModuleIncrCodeScanGate';
import { UnCrPassModuleBO } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';


export const getMixedScope = ({ scope, alterSheetType }: { scope: string; alterSheetType: string }) => {
  let mixedScope = scope;
  // 新研发模式构建页面需要同时透出混合流水线和单测流水线
  if (alterSheetType === 'AGILE_CI' && scope === 'AGILE_CI_ALTER_SHEET_BUILD') {
    // 兼容旧的scope ALTER_SHEET_BUILD
    mixedScope = 'ALTER_SHEET_BUILD,AGILE_CI_ALTER_SHEET_BUILD,ALTER_SHEET_UNIT_TEST';
  }
  if (alterSheetType === 'NATIVE_DYNAMIC' && scope === 'NATIVE_DYNAMIC_ALTER_SHEET_BUILD') {
    // 兼容旧的scope ALTER_SHEET_BUILD
    mixedScope = 'ALTER_SHEET_BUILD,NATIVE_DYNAMIC_ALTER_SHEET_BUILD';
  }
  return mixedScope;
};

const DevStageBuild = () => {
  const { data: iterationVO } = useIterationContext();
  const { mainEntity } = iterationVO || {};
  const { id: alterSheetId, type: alterSheetType, status: alterSheetStatus } = mainEntity || ({} as any);
  const { token } = theme.useToken();
  const [onlyShowMine, setOnlyShowMine] = useState<boolean>(false);
  const [modulesListAiCrContentOpen, setModulesListAiCrContentOpen] = useState<boolean>(false);
  const [searchParams] = useSearchParams();
  const urlPipelineId = searchParams.get('pipeline') ? parseInt(searchParams.get('pipeline') as string, 10) : undefined;

  const {
    runAsync: findUnPassModules,
    data: unPassModule,
  } = useRequest<UnCrPassModuleBO[], [FindUnPassModuleParams]>(findUnPassModule);


  const {
    requestPipelineList,
    pipelineList,
    pipelineListLoading,
    requestRegressionInfoList,
    refreshPipelineList,
    regressionInfoList,
  } = useDevStageBuildContext();
  let scope = 'ALTER_SHEET_BUILD';
  if (alterSheetType === 'AGILE_CI') {
    scope = 'AGILE_CI_ALTER_SHEET_BUILD';
  } else if (alterSheetType === 'NATIVE_DYNAMIC') {
    scope = 'NATIVE_DYNAMIC_ALTER_SHEET_BUILD';
  }
  const mixedScope = getMixedScope({ scope, alterSheetType });
  const isModuleDeploy =
    scope === 'ALTER_SHEET_DEPLOY' && alterSheetType !== 'MAIN_FRAMEWORK' && alterSheetType !== 'NORMAL';
  const isRunDisabled = isModuleDeploy ? isFinished(alterSheetStatus) : isClosed(alterSheetStatus);


  useEffect(() => {
    requestPipelineList({
      id: alterSheetId,
      pageSize: 100,
      scope: mixedScope,
      onlyShowMine,
    });
  }, [alterSheetId, mixedScope, onlyShowMine, requestPipelineList]);

  useEffect(() => {
    if (alterSheetId) {
      requestRegressionInfoList({
        id: alterSheetId,
      });
      findUnPassModules({
        alterSheetId,
      });
    }
  }, [alterSheetId, requestRegressionInfoList, findUnPassModules]);

  const mostRecentPipelineId = useMemo(() => findMostRecentPipelineId(pipelineList), [pipelineList]);

  return (
    <Flex flex="1" vertical className={styles.devStageBuildContent}>
      {/* AI CR 卡口不通过，模块无法提测，请尽快处理，模块及问题详情可点击查看 */}
      {unPassModule && unPassModule?.length > 0 && <Alert
        showIcon
        type="error"
        style={{ marginBottom: token.margin }}
        icon={<StopOutlined />}
        message={<Flex>
          AI CR 卡口不通过，模块无法提测，请尽快处理，模块及问题详情可点击
          <a
            onClick={() => {
              setModulesListAiCrContentOpen(true);
            }}
          >查看</a>。
        </Flex>}
      />}
      <Flex justify="space-between" align="center" className={styles.actionArea}>
        <Flex>
          {pipelineList && (
            <Flex gap={token.marginXS} align="center">
              <InfoOutlined style={{ color: token.colorTextSecondary }} />
              <PipelineStatusOverview pipelines={pipelineList} />
            </Flex>
          )}
        </Flex>
        <Flex>
          <Checkbox checked={onlyShowMine} onChange={(e) => setOnlyShowMine(e.target.checked)}>
            只显示我的模块构建流水线
          </Checkbox>
        </Flex>
      </Flex>
      <Skeleton loading={pipelineListLoading}>
        {
          (pipelineList ?? []).map((pipeline, idx) => {
            const activePipeline = urlPipelineId
              ? urlPipelineId === pipeline?.id : mostRecentPipelineId
                ? pipeline?.id === mostRecentPipelineId : idx === 0;
            return (<PipelineCollapse
              activePipeline={activePipeline}
              key={`build_pipeline_${pipeline?.id}`}
              iterationVO={iterationVO}
              pipelineJSON={JSON.stringify(pipeline ?? {})}
              isFirst={idx === 0}
              isLast={idx === pipelineList?.length - 1}
              isRunDisabled={isRunDisabled}
              scope={scope}
              refreshPipelineList={refreshPipelineList}
              regressionInfoList={regressionInfoList}
            />);
          })
        }
      </Skeleton>
      <ModulesListAiCrContentModal
        unPassModule={unPassModule}
        modalOpen={modulesListAiCrContentOpen}
        closeModal={() => { setModulesListAiCrContentOpen(false); }}
      />
    </Flex>
  );
};

export default DevStageBuild;
