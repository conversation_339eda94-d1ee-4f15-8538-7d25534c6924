import React from 'react';
import styles from './iterationDetail.module.less';
import { ALTER_MODE_TYPE_MAP, ALTER_TYPE_MAP } from '@/constants/alterSheet';
import { GitBranchSelect } from '@ali/mc-uikit';
import { Flex, Form, Input, Modal, message, theme } from 'antd';

import { updateAlterSheetModule } from '@ali/mc-services/AlterSheet';
import { AlterSheetModuleVO } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';

interface EditStageModuleModalProps {
  editItem: any;
  modalOpen: boolean;
  closeModal: (isRefresh?: boolean) => void;
}
const EditStageModuleModal = (props: EditStageModuleModalProps) => {
  const { editItem, modalOpen, closeModal } = props;
  const [form] = Form.useForm();
  const { token } = theme.useToken();

  const { runAsync: requestUpdateModule, loading: editLoading } = useRequest<number, [AlterSheetModuleVO]>(
    updateAlterSheetModule,
  );

  const editHandler = (val: { version: string; branch: string }) => {
    requestUpdateModule({
      ...editItem,
      ...val,
    }).then((res) => {
      if (res) {
        message.success('更新成功');
        closeModal(true);
      }
    });
  };
  return (
    <Modal
      title="编辑模块"
      open={modalOpen}
      onOk={form.submit}
      onCancel={() => {
        closeModal(false);
      }}
      confirmLoading={editLoading}
      destroyOnClose
      afterOpenChange={() => {
        form.resetFields();
      }}
      className={styles.editModuleModal}
    >
      <Flex className={styles.moduleName}>
        <Flex>模块名称：</Flex>
        {editItem?.module?.name}
      </Flex>
      <Flex gap={token.margin} style={{ marginBottom: token.margin }}>
        <Flex>
          <Flex className={styles.itemLabel}>迭代类型：</Flex>
          {ALTER_TYPE_MAP[editItem?.alterType as ALTER_TYPE]}
        </Flex>
        <Flex>
          <Flex className={styles.itemLabel}>变更模式：</Flex>
          {ALTER_MODE_TYPE_MAP[editItem?.alterMode as ALTER_MODE_TYPE]}
        </Flex>
      </Flex>
      <Form
        name="basic"
        form={form}
        layout="vertical"
        autoComplete="off"
        initialValues={editItem}
        onFinish={editHandler}
      >
        <Form.Item label="所属版本：" name="version" rules={[{ required: true, message: '请填写模块所属版本！' }]}>
          <Input />
        </Form.Item>
        {editItem?.alterType === 'SOURCE' && <Form.Item label="分支：" name="branch" rules={[{ required: true, message: '请选择模块分支！' }]}>
          <GitBranchSelect scmAddress={editItem?.module?.codeLibraryAddress ?? ''} disabled={editItem?.subBranches?.length > 1} />
        </Form.Item>}
      </Form>
    </Modal>
  );
};

export default EditStageModuleModal;
