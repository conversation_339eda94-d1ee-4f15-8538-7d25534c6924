import React, { useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Drawer,
  Flex,
  Form,
  FormInstance,
  Input,
  Popover,
  Radio,
  Table,
  Tooltip,
  message,
  theme,
} from 'antd';
import styles from './FeedbackSubmitTestResult.module.less';
import { FileOutlined } from '@ali/mc-icons';
import { feedbackTestResult, FeedbackTestResultParams } from '@ali/mc-services/TestSheet';
import { TestReport } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import { InlineButton } from '@ali/mc-uikit';
import TestReportPanel from '../TestReport/TestReportPanel';
import useDrawerWidth from '@/hooks/useDrawerWidth';

type FieldType = {
  status: 'TEST_PASS' | 'TEST_FAIL';
  feedback: string;
};

interface FeedbackSubmitTestResultProps {
  btnText: string;
  config: any;
  onOk?: () => void;
}

const FeedbackSubmitTestResult = (props: FeedbackSubmitTestResultProps) => {
  const { token } = theme.useToken();
  const [form] = Form.useForm();
  const reportRef = useRef<FormInstance>();

  const [visible, setVisible] = useState<boolean>(false);
  const selectedStatus = Form.useWatch('status', form);

  const [drawerWidth] = useDrawerWidth();

  const { runAsync: requestFeedbackTestResult, loading } = useRequest<number, [FeedbackTestResultParams, TestReport]>(
    feedbackTestResult,
  );

  const { config, btnText, onOk } = props;
  const { moduleList, alterSheet, testType = true } = config;
  const changedModuleList = [] as any[];
  let displayModules = false;
  if (moduleList) {
    for (const item of moduleList) {
      const { latestCommitOnBranch, commitId } = item;
      if (latestCommitOnBranch && latestCommitOnBranch.id !== commitId) {
        changedModuleList.push(item);
      }
    }
  }
  if (changedModuleList.length > 0) {
    displayModules = true;
  }

  const getExtraDisplay = () => {
    if (!displayModules) {
      return null;
    }

    return (
      <Flex vertical gap={token.marginXS}>
        <Alert showIcon type="error" message="以下模块提测分支的CommitID已经不是最新，是否提醒开发重新提测?" />
        <Table dataSource={changedModuleList} rowKey="id" pagination={false}>
          <Table.Column
            title="变更模块名"
            dataIndex="moduleName"
            render={(moduleName: string, record: any) => {
              return (
                <a href={`/#/app/${record.moduleId}/detail/overview`} target="_blank" rel="noopener noreferrer">
                  {moduleName}
                </a>
              );
            }}
          />

          <Table.Column
            title="提测分支（Commit ID）"
            dataIndex="branch"
            ellipsis
            render={(branch: string, record: any) => {
              return (
                <span>
                  {branch}（{record.gitCommit && record.gitCommit.shortId}）
                </span>
              );
            }}
          />

          <Table.Column
            title="最新分支（CommitId）"
            dataIndex="latestCommitOnBranch"
            render={(latestCommitOnBranch: any) => {
              return (
                <span>
                  {latestCommitOnBranch && latestCommitOnBranch.branch}（
                  <Popover content={latestCommitOnBranch && latestCommitOnBranch.id}>
                    {latestCommitOnBranch && latestCommitOnBranch.shortId}
                  </Popover>
                  ）
                </span>
              );
            }}
          />
        </Table>
      </Flex>
    );
  };

  const onSubmit = async () => {
    let result;
    let report;
    try {
      result = await form.validateFields();
    } catch(err) {
      if (err?.errorFields?.length) {
        form.scrollToField(err.errorFields[0].name, {
          behavior: 'smooth',
          block: 'center'
        });
      }
      return;
    }

    try {
      report = await reportRef.current?.validateFields();
    } catch(err) {
      if (err?.errorFields?.length) {
        reportRef?.current?.scrollToField(err.errorFields[0].name, {
          behavior: 'smooth',
          block: 'center'
        });
      }
      return;
    }
    try {
      const res = await requestFeedbackTestResult(
        {
          ...result,
          id: config.id,
        },
        {
          ...report,
        },
      )
      if (res) {
        message.success('结果反馈提交成功');
        setVisible(false);
        onOk && onOk();
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <Flex>
      <Tooltip placement="top" title={!testType && '暂无测试报告相关数据，可联系测试执行人'}>
        {testType ? (
          <Button
            onClick={() => {
              setVisible(true);
            }}
            type="primary"
            disabled={!testType}
          >
            结果反馈
          </Button>
        ) : (
          <InlineButton
            onClick={() => {
              setVisible(true);
            }}
            className={testType ? styles.inlineButton : styles.disableButton}
            disabled={!testType}
            type="text"
          >
            <FileOutlined className={styles.icon} />
            <span>{btnText}</span>
          </InlineButton>
        )}
      </Tooltip>
      <Drawer
        width={drawerWidth}
        open={visible}
        title={testType ? '测试结果反馈' : '测试报告详情'}
        onClose={() => setVisible(false)}
        footer={
          <Flex gap={token.marginXS}>
            {testType && (
              <Button onClick={onSubmit} type="primary" loading={loading} htmlType="submit">
                {displayModules ? '确定，仍使用提测分支' : '确认'}
              </Button>
            )}
            <Button
              type={testType ? 'default' : 'primary'}
              onClick={() => {
                setVisible(false);
              }}
              className={testType ? styles.cancel : styles.save}
            >
              {testType ? (displayModules ? '取消，提醒开发重新提测' : '取消') : '确定'}
            </Button>
          </Flex>
        }
      >
        <Flex vertical>
          <Flex vertical gap="middle">
            {testType && (
              <>
                {alterSheet.type === 'AGILE_CI' && (
                  <Alert
                    message="测试通过之后代码评审和合并、模块发布正式版和集成会自动执行，请谨慎操作！"
                    type="warning"
                    showIcon
                  />
                )}
              </>
            )}
            <Form form={form} layout="vertical">
              {testType && (
                <>
                  <Form.Item<FieldType>
                    label="测试结果"
                    name="status"
                    rules={[{ required: true, message: '请选择测试结果!' }]}
                  >
                    <Radio.Group>
                      <Flex vertical gap="small">
                        <Radio key="TEST_PASS" value="TEST_PASS">
                          <span>测试通过</span>
                        </Radio>
                        <Radio key="TEST_FAIL" value="TEST_FAIL">
                          <span>测试不通过</span>
                        </Radio>
                      </Flex>
                    </Radio.Group>
                  </Form.Item>

                  {selectedStatus === 'TEST_FAIL' && (
                    <Form.Item<FieldType>
                      label="不通过原因"
                      name="feedback"
                      rules={[
                        {
                          required: true,
                          message: '请输入测试不通过原因',
                        },
                        {
                          min: 10,
                          message: '原因不能少于10个字符',
                        },
                        {
                          max: 500,
                          message: '原因不能多于500个字符',
                        },
                      ]}
                    >
                      <Input.TextArea
                        autoSize={{
                          minRows: 5,
                          maxRows: 8,
                        }}
                        placeholder="请输入"
                      />
                    </Form.Item>
                  )}
                  {getExtraDisplay()}
                </>
              )}
            </Form>
          </Flex>
          <Divider style={{ marginTop: 0 }} />
          {testType && (
            <TestReportPanel
              id={config?.id}
              ref={reportRef}
            />
          )}
        </Flex>
      </Drawer>
    </Flex>
  );
};

export default FeedbackSubmitTestResult;
