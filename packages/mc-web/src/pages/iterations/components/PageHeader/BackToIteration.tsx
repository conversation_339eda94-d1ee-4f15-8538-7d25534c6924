import { useRequest } from '@ali/mc-request';
import { getMcAlterSheetPath, GetMcAlterSheetPathParams } from '@ali/mc-services/Common';
import { Flex, Form, Input, message, Modal, Radio, Space, theme, Typography } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import styles from './index.module.less';
import { ArrowSwitchOutlined, LinkExternalOutlined, MegaphoneOutlined } from '@ali/mc-icons';
import aes from '@/util/aes';
import { User } from '@ali/mc-uikit';
import { Link } from 'ice';

const ReasonOptions = [
  {
    label: '功能不可用',
    value: 'bug',
  },
  {
    label: '功能缺失 ',
    value: 'absence',
  },
  {
    label: '操作不习惯 ',
    value: 'habits',
  },
];

const BackToIteration = ({ alterSheetId }: { alterSheetId: number }) => {
  const { token } = theme.useToken();
  const [form] = Form.useForm();
  const [open, setOpen] = useState<boolean>(false);
  const { runAsync: getMcAlterSheetUrl } = useRequest<string, [GetMcAlterSheetPathParams]>(getMcAlterSheetPath);
  const [oldUrl, setOldUrl] = useState<string>('');
  const selectedReason = Form.useWatch('reason', form);

  useEffect(() => {
    if (alterSheetId) {
      getMcAlterSheetUrl({
        alterSheetId,
      }).then((url) => {
        const path = url?.split('/#')?.[1];
        setOldUrl(`${window.location.origin}/#${path}`);
      });
    }
  }, [alterSheetId, getMcAlterSheetUrl]);

  const handleSubmit = useCallback(
    ({ isOk }: { isOk: boolean }) => {
      if (isOk) {
        form
          .validateFields()
          .then((values) => {
            const { reason, detail } = values;
            aes.sendEvent('newIterationsFeedback', {
              et: 'CLK',
              c1: reason,
              ...(detail && { c2: detail }),
            });
            setOpen(false);
            message.success('感谢反馈，即将跳转到旧版迭代页面～');
            setTimeout(() => {
              window.open(oldUrl, '_self');
            }, 500);
          })
          .catch((err) => {
            console.error(err);
          });
      } else {
        aes.sendEvent('skipFeedbackCounts', {
          et: 'CLK',
          c1: 1,
        });
        setOpen(false);
        setTimeout(
          () => {
            window.open(oldUrl, '_self');
          },
          isOk ? 500 : 300,
        );
      }
    },
    [oldUrl, form],
  );

  const handleClick = () => {
    setOpen(true);
    aes.sendEvent('clickBackToOldIterations', {
      et: 'CLK',
      c1: 1,
    });
  };

  return (
    <>
      {/* <Popconfirm
      icon=""
      trigger={['click']}
      placement="bottomRight"
      destroyTooltipOnHide
      // showCancel={false}
      okButtonProps={{
        disabled: !selectedReason,
        onClick: () => handleSubmit({ isOk: true }),
      }}
      title={null}
      description={
        <Form form={form} preserve={false} layout="vertical">
          <Form.Item
            name="reason"
            label="请选择您切回旧版的原因"
            rules={[{ required: true, message: '请选择原因' }]}
            validateTrigger={['onFinish']}
            style={{
              marginBottom: 12,
            }}
          >
            <Radio.Group>
              <Space direction="vertical" style={{ paddingLeft: 12 }}>
                {
                  ReasonOptions.map(item => <Radio key={item.value} value={item.value}>{item.label}</Radio>)
                }
              </Space>
            </Radio.Group>
          </Form.Item>
        </Form>
      }
    >
      <Flex onClick={handleClick} className={styles.entryBtn} align="center">
        <ArrowSwitchOutlined style={{ marginInlineEnd: 4 }} />
        返回旧版迭代
      </Flex>

    </Popconfirm> */}
      <Flex onClick={handleClick} className={styles.entryBtn} align="center">
        <ArrowSwitchOutlined style={{ marginInlineEnd: 4 }} />
        返回旧版迭代
      </Flex>
      <Modal
        destroyOnClose
        // closable={false}
        title={
          <Flex>
            返回旧版迭代原因（
            <Flex style={{ color: token.colorError }} gap={token.marginXXS}>
            <MegaphoneOutlined />旧版迭代将会在2025年03月31日正式下架
            </Flex>
            ）
          </Flex>
        }
        open={open}
        cancelText="取消"
        okText="确认"
        okButtonProps={{
          disabled: !selectedReason,
          onClick: () => {
            handleSubmit({ isOk: true });
          },
        }}
        // footer={
        //   <Flex justify="end">
        //     <Button type="primary" disabled={!selectedReason} onClick={() => { handleSubmit({ isOk: true }); }}>
        //       确认
        //     </Button>
        //   </Flex>
        // }
        // onCancel={() => handleSubmit({ isOk: false })}
        onOk={() => handleSubmit({ isOk: true })}
        onCancel={() => setOpen(false)}
        styles={{
          body: {
            paddingTop: 0,
          },
        }}
      >
        <Form form={form} preserve={false} layout="vertical">
          <Form.Item
            name="reason"
            label={<div style={{ fontWeight: 500 }}>原因</div>}
            rules={[{ required: true, message: '请选择原因' }]}
            validateTrigger={['onFinish']}
            style={{
              marginBottom: 12,
            }}
          >
            <Radio.Group>
              <Space direction="vertical">
                {ReasonOptions.map((item) => (
                  <Radio key={item.value} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            name="detail"
            label={<div style={{ fontWeight: 500 }}>问题描述/反馈意见</div>}
            rules={[{ required: true, message: '请描述具体问题' }]}
            style={{
              marginBottom: 12,
            }}
          >
            <Input.TextArea rows={3} placeholder="请描述具体问题，以便我们进行快速优化～" />
          </Form.Item>
          <Typography.Text type="secondary">
            为了能够更好地服务您，我们会尽全力不断提升用户体验。如果您在使用过程中遇到任何问题，或者有任何宝贵的意见和建议，恳请您能让我们获知。
          </Typography.Text>
          <Flex gap="small" vertical style={{ marginTop: token.marginSM }}>
            <Flex>
              <span>PD：</span>
              <Flex gap="small" style={{ color: token.colorLink }}>
                <User empIds="440842" showAvatar /> <User empIds="440318" showAvatar />{' '}
                <User empIds="65738" showAvatar />
              </Flex>
            </Flex>
            <Flex>
              <span>答疑群：</span>
              <Link
                to="dingtalk://dingtalkclient/action/joingroup?code=v1,k1,y7Vj6ZflhCIq0cwEJ87hsRQWE7eeQqTII3LwI7318C0="
                target="_blank"
              >
                <Flex gap={token.marginXXS}>
                  淘天MTL Cloud答疑
                  <LinkExternalOutlined />
                </Flex>
              </Link>
            </Flex>
          </Flex>
        </Form>
      </Modal>
    </>
  );
};

export default BackToIteration;
