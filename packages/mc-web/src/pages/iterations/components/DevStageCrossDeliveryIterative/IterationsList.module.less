.integrationAgileCiIterations {
  flex: 1;

  .integrationAgileCiIterationsList {
    width: 100%;

    .header {
      font-size: var(--mc-font-size);
      font-weight: 600;
    }

    .action {
      cursor: pointer;
      color: var(--mc-color-text);
    }

    .title {
      font-size: var(--mc-font-size);
      line-height: var(--mc-line-height);
      margin-bottom: var(--mc-margin-xs);
      font-weight: 600;
      color: var(--mc-color-text);
    }

    .info {
      font-size: var(--mc-font-size-sm);
      line-height: var(--mc-line-height-sm);
      margin-bottom: var(--mc-margin-xxs);
      color: var(--mc-color-text-tertiary);
    }
  }
}

.inlineSkeleton{
  height: 20px !important;
}
