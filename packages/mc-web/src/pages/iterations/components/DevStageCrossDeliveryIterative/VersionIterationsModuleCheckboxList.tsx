import React, { memo } from 'react';
import { Checkbox, Flex, theme, Card, List, Radio, Select, RadioChangeEvent, Empty, Button } from 'antd';
import { ALTER_TYPE, ALTER_MODE } from '@/constants/devIntegration';
import styles from '../DevIntegration/IntegrationModuleCheckboxList.module.less';
import { InfoOutlined } from '@ali/mc-icons';
import AoneRequestSelect from '@/components/AoneRequestSelect';
import { ApplicationVO } from '@ali/mc-services';

interface VersionIterationsModuleCheckboxListProps {
  loading?: boolean;
  value?: any[];
  onChange?: (value: any[]) => void;
  appDetail?: ApplicationVO;
  versionPlanId?: number;
}

const VersionIterationsModuleCheckboxList = (props: VersionIterationsModuleCheckboxListProps) => {
  const { token } = theme.useToken();
  const {
    onChange,
    value: moduleList = [],
    loading,
    appDetail,
    versionPlanId,
  } = props;

  // 单个选择
  const handleChange = (id: number, type: string, key: any) => {
    let newModuleList = moduleList?.map((item) => {
      const moduleId = item.id;
      if (moduleId === id) {
        return {
          ...item,
          [type]: key,
        };
      } else {
        return item;
      }
    });
    onChange && onChange(newModuleList as any[]);
  };

  // 全选
  const handleAllChange = (event: RadioChangeEvent) => {
    let newModuleList = moduleList?.map((item) => {
      return {
        ...item,
        checked: event.target.checked ?? true,
      };
    });
    onChange && onChange(newModuleList as any[]);
  };

  const checked = moduleList?.every((item) => item?.checked);

  if (moduleList?.length === 0 && appDetail?.platformType === 'ANDROID' && versionPlanId && !loading) {
    return (
      <Empty
        description="当前迭代内KMP跨端模块未关联native模块，无法创建Android客户端迭代，请前往关联。"
        image="https://picasso-work.alibaba-inc.com/i1/O1CN01NxdtFq1JiRf046yxh_!!6000000001062-2-tps_intranet-150-120.png"
      >
        <Button
          onClick={() => { window.open('https://alidocs.dingtalk.com/i/nodes/gvNG4YZ7Jnxop15OCRgw9xNPW2LD0oRE?utm_scene=team_space', '_blank'); }}
        >使用说明</Button>
      </Empty>
    );
  }

  return (
    <Flex
      vertical
      gap={token.marginXS}
      className={styles.IntegrationModuleCheckboxList}
    >
      {moduleList?.length > 0 && (
        <Flex gap={token.marginXXS} align="center">
          <span style={{ color: token.colorError }}>*</span>
          <span>当前迭代内KMP跨端模块关联的native模块</span>
          <Checkbox checked={checked} onChange={handleAllChange}>
            全选
          </Checkbox>
          <a target="_blank" rel="noreferrer" href="https://alidocs.dingtalk.com/i/nodes/gvNG4YZ7Jnxop15OCRgw9xNPW2LD0oRE?utm_scene=team_space">{'使用说明>>'}</a>
        </Flex>
      )}
      <List
        className={styles.list}
        loading={loading}
        grid={{ column: 1 }}
        dataSource={moduleList as any[]}
        renderItem={item => {
          const {
            id,
            alterType,
            alterModes,
          } = item;

          return (
            <List.Item key={item.id}>
              <Card
                title={
                  <Flex gap={token.margin} className={styles.cardTitleWrap}>
                    <Flex align="center">
                      <Checkbox
                        checked={item?.checked || false}
                        onChange={e => handleChange(id, 'checked', e.target.checked)}
                      />
                      <span style={{ marginLeft: token.marginXS }}>{item?.name}</span>
                    </Flex>
                    <Flex>（已关联{item?.relatedModule?.name}，根据所选客户端自动添加）</Flex>
                  </Flex>
                }
                style={{ width: '100%' }}
              >
                <Flex justify="space-between">
                  <Flex vertical gap={token.margin} flex={1}>
                    <Flex align="center">
                      <span className={styles.selectTitle}>{'版本号(snapshot版本）：'}</span>
                      <Select style={{ width: 372 }} disabled value={item?.version} />
                    </Flex>
                    <Flex align="center">
                      <span className={styles.selectTitle}>变更类型：</span>
                      <Radio.Group disabled value={alterType}>
                        {Object.entries(ALTER_TYPE)?.map(([key]) => (
                          <Radio value={key} key={key}>
                            {ALTER_TYPE[key]}
                          </Radio>
                        ))}
                      </Radio.Group>
                    </Flex>
                    <Flex align="center">
                      <span className={styles.selectTitle}>变更模式：</span>
                      <Radio.Group
                        onChange={(event: RadioChangeEvent) =>
                          handleChange(id, 'alterMode', event.target.value)}
                        value={item?.alterMode}
                      >
                        {alterModes?.map((alterMode_: string) => (
                          <Radio value={alterMode_} key={alterMode_}>
                            {ALTER_MODE[alterMode_] || alterMode_}
                          </Radio>
                        ))}
                      </Radio.Group>
                    </Flex>
                    <Flex vertical gap={token.marginXS}>
                      <span>当前模块变更所对应的需求</span>
                      <AoneRequestSelect
                        onChange={(value) =>
                          handleChange(id, 'aoneRequestItems', value.map((aone: any) => {
                            return {
                              moduleIds: [id],
                              requirementId: aone.id,
                              requirementName: aone.label,
                            };
                          }))}
                      />
                      <Flex gap={token.marginXXS} align="center" style={{ color: token.colorTextSecondary }} >
                        <InfoOutlined />
                        <span>应PMO要求，变更模块须关联到对应的需求。</span>
                      </Flex>
                    </Flex>
                  </Flex>
                </Flex>
              </Card>
            </List.Item>
          );
        }}
      />
    </Flex>
  );
};

export default memo(VersionIterationsModuleCheckboxList);
