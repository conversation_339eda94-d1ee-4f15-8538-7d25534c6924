import React, { useState } from 'react';
import { Button, Flex, Spin, theme, Modal } from 'antd';
import { DescriptionOutlined } from '@ali/mc-icons';
import { useRequest } from '@ali/mc-request';
import { findIntegrateSheetIntegrateReasonList } from '@ali/mc-services/IntegrateSheet';
import { IntegrateSheetInnerBO } from '@ali/mc-services';
import { InlineButton } from '@ali/mc-uikit';
import App from '@/components/App/app';
import styles from './index.module.less';
import IntegrateAreaStatusTag from '@/components/IntegrateArea/IntegrateAreaStatusTag';

interface IntegrationDetailModalProps {
  integrateItem: IntegrateSheetInnerBO;
}

const IntegrationDetailModal = (props: IntegrationDetailModalProps) => {
  const { token } = theme.useToken();
  const [visible, setVisible] = useState<boolean>(false);
  const { integrateItem } = props;
  const { integrateArea, applicationId, description, integrateReasonCategory } = integrateItem;

  const { data: integrateReasonCategoryMap, loading } = useRequest(findIntegrateSheetIntegrateReasonList, {
    manual: false,
  });

  return (
    <Flex>
      <InlineButton
        onClick={() => { setVisible(true); }}
        className={styles.inlineButton}
        type="text"
      >
        <DescriptionOutlined className={styles.icon} />
        <span>集成详情</span>
      </InlineButton>
      <Modal
        title="集成详情"
        open={visible}
        onCancel={() => setVisible(false)}
        footer={
          <Button type="primary" onClick={() => setVisible(false)}>关闭</Button>
        }
      >
        <Flex vertical>
          <Flex align="center">
            {
              integrateArea?.status && (
                <IntegrateAreaStatusTag status={integrateArea?.status} />
              )
            }
            <span style={{ fontWeight: 600, marginLeft: token.marginXS }}>
              {integrateArea?.name}
            </span>
            <img
              style={{ marginLeft: token.marginMD / 2, cursor: 'pointer' }}
              onClick={() => {
                window.open(`/#/app/${integrateArea?.integrateApplicationId}/detail/integration/${integrateArea?.id}`, '_blank', 'noopener');
              }}
              src="https://img.alicdn.com/imgextra/i4/O1CN01jgc7Od1CclFuOHRqP_!!6000000000102-55-tps-16-16.svg"
            />
          </Flex>
          <Flex
            style={{
              marginTop: token.marginSM,
              fontSize: token.fontSizeSM,
            }}
          >
            <Flex>
              <span style={{ color: token.colorTextTertiary }}>集成客户端：</span>
              <span><App appId={applicationId} /></span>
            </Flex>
            <Flex style={{ marginLeft: token.marginLG }}>
              <span style={{ color: token.colorTextTertiary }}>集成原因：</span>
              <span>
                <Spin spinning={loading} />
                {(!loading && integrateReasonCategoryMap && integrateReasonCategory) &&
                  integrateReasonCategoryMap[integrateReasonCategory]}
              </span>
            </Flex>
          </Flex>
          <Flex
            style={{
              marginTop: token.marginXS,
              fontSize: token.fontSizeSM,
            }}
          >
            <span style={{ color: token.colorTextTertiary }}>集成说明：</span>
            <span>{description}</span>
          </Flex>
        </Flex>
      </Modal>
    </Flex>
  );
};


export default IntegrationDetailModal;
