import React from 'react';
import { Button, Flex, Input, Radio, theme } from 'antd';
import { Reload, SearchIcon } from '@/components/Icons';
import { InsightItemListQryVO } from '@ali/mc-services';
import { radioOptions } from '../../../common/release';
import styles from '../index.module.less';
interface ReleaseInfoHeaderProps {
  mySelf: string;
  setMySelf: (mySelf: string) => void;
  listLoading: boolean;
  query: InsightItemListQryVO;
  setQuery: (query: InsightItemListQryVO) => void;
  getStatusCount: (params: { mySelf: boolean; status?: string; type?: string }) => void;
}

// 列表头部筛选
export default function ReleaseInfoHeader(props: ReleaseInfoHeaderProps) {
  const { mySelf, listLoading, setQuery, query, setMySelf, getStatusCount } = props;
  const { token: themeToken } = theme.useToken();

  return (
    <Flex justify="space-between" className={styles.header}>
      <Radio.Group
        optionType="button"
        buttonStyle="solid"
        options={radioOptions}
        value={mySelf}
        onChange={async (e) => {
          if (!listLoading) {
            setQuery({
              ...query,
              isFirstCrash: undefined,
              crashType: undefined,
              crashCountAsc: false,
              pageNum: 0,
            });
            setMySelf(e.target.value);
            getStatusCount({ mySelf: e.target.value === 'my' });
          }
        }}
      />
      <Flex gap={themeToken.marginXS}>
        <Input
          style={{ width: 462 }}
          placeholder="请输入验证内容"
          value={query.keyword}
          prefix={
            <SearchIcon
              className={styles.searchIcon}
              style={{ fontSize: themeToken.fontSizeLG + 2 }}
            />
          }
          onChange={(e) => {
            if (!listLoading) {
              setQuery({
                ...query,
                keyword: e.target?.value || undefined,
                pageNum: 0,
              });
            }
          }}
        />
        <Button
          disabled={listLoading}
          onClick={() => {
            // 刷新不需要重新做定位 保留到当前的tab 需传入当前的 mySelf status type
            getStatusCount({
              mySelf: mySelf === 'my',
              status: query.status,
              type: query.type,
            });
          }}
          icon={<Reload />}
        />
      </Flex>
    </Flex>
  );
}
