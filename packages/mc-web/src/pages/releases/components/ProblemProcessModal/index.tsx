import { Button, Flex, Form, Input, Modal, Timeline, message, theme } from 'antd';
import React from 'react';
import { StatusTag, User } from '@ali/mc-uikit';
import styles from './index.module.less';
import dayjs from 'dayjs';
import { LEVEL, PLAN_MAP } from '../../common/statusMap';
import { AoneBindAlterInfo, FeedbackProgress, InsightItemDTO, InsightItemListUpdateVO } from '@ali/mc-services';
import { updateItems } from '@ali/mc-services/Insight';
import { useAppData } from 'ice';
import { useRequest } from '@ali/mc-request';
import { useForm } from 'antd/es/form/Form';

interface ProblemProcessModalProps {
  visible: boolean;
  record: InsightItemDTO;
  status: string;
  onCancel: () => void;
  integrateAreaId: number;
  onProcess: () => void;
}

const ProblemProcessModal = (props: ProblemProcessModalProps) => {
  const appData = useAppData() || {};
  const user = appData?.user || {};
  const { token } = theme.useToken();
  const [form] = useForm();
  const { record = {}, status, visible, onCancel, onProcess } = props;
  const { feedbackList = [] } = record;

  const { runAsync: requestUpdateItems, loading } = useRequest<
    AoneBindAlterInfo, [InsightItemListUpdateVO[]]
  >(updateItems);

  const onSubmit = () => {
    form.validateFields().then((values) => {
      if (values?.text) {
        requestUpdateItems([
          {
            id: record.id,
            feedbackList: [
              ...feedbackList,
              {
                desc: values?.text,
                creator: user?.empId,
                createTime: JSON.stringify(dayjs().valueOf()),
              },
            ],
          },
        ]).then((res) => {
          if (res) {
            message.success('提交成功');
            onProcess && onProcess();
            onCancel && onCancel();
          }
        });
      } else {
        message.warning('评论不能为空');
      }
    });
  };

  const onReset = () => {
    form.setFieldsValue({ text: '' });
  };

  return (
    <Modal
      width={930}
      title="问题进展"
      open={visible}
      footer={null}
      onCancel={onCancel}
    >
      <Flex className={styles.desc} vertical gap={token.margin}>
        <Flex gap={token.marginXS} className={styles.header} align="center">
          <span className={styles.title}>
            {record?.content?.title}
          </span>
        </Flex>
        <Flex gap={token.margin}>
          {record?.creator && (<Flex><span>反馈人：</span><User empIds={record.creator} showAvatar /></Flex>)}
          {record?.gmtCreate && (<Flex><span>反馈时间：</span>{dayjs(record.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}</Flex>)}
          {record?.processor && (<Flex><span>处理人：</span><User empIds={record.processor} showAvatar /></Flex>)}
          {record?.gmtProcess && (<Flex><span>处理时间：</span>{dayjs(record.gmtProcess).format('YYYY-MM-DD HH:mm:ss')}</Flex>)}
        </Flex>
        <Flex>
          <span>问题标签：</span>
          <Flex gap={token.marginXS}>
            {record?.status === 'UNDO' && status === 'ALL' && (
              <StatusTag bordered color="error">
                待处理
              </StatusTag>
            )}
            {record?.level === 'P0' && (
              <StatusTag bordered={false} color={LEVEL[record.level]?.color}>
                {LEVEL[record.level]?.label}
              </StatusTag>
            )}
            {record?.plan === 'URGENT_INTEGRATE' && (
              <StatusTag bordered={false} color={PLAN_MAP[record.plan]?.color}>
                {PLAN_MAP[record.plan]?.label}
              </StatusTag>
            )}
          </Flex>
        </Flex>
      </Flex>
      <Flex className={styles.process}>
        <Timeline
          items={feedbackList.map((item: FeedbackProgress) => {
            return {
              dot: <Flex><User empIds={item?.creator} showName={false} showAvatar /></Flex>,
              children: (
                <Flex vertical>
                  <Flex className={styles.header} gap={token.margin}>
                    <User empIds={item?.creator} />
                    {item?.createTime ? (
                      <span>{dayjs(item?.createTime).format('YYYY-MM-DD HH:mm:ss')}</span>
                    ) : '-'}
                  </Flex>
                  <Flex className={styles.text}>
                    {item?.desc}
                  </Flex>
                </Flex>
              ),
            };
          })}
        />
      </Flex>
      <Form form={form}>
        <Form.Item
          label=""
          name="text"
          noStyle
        >
          <Input.TextArea
            rows={6}
            placeholder="请输入评论"
          />
        </Form.Item>
      </Form>
      <Flex
        gap={token.marginXS}
        justify="end"
        style={{ paddingTop: token.padding, paddingBottom: token.padding }}
      >
        <Button loading={loading} onClick={onReset}>
          重置
        </Button>
        <Button loading={loading} type="primary" onClick={onSubmit}>
          提交评论
        </Button>
      </Flex>
    </Modal>
  );
};

export default ProblemProcessModal;
