import { Flex, message, Popover, theme } from 'antd';
import { isEmpty } from 'lodash-es';
import React from 'react';
import styles from './index.module.less';
import { GitCommitBO } from '@ali/mc-services';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import dayjs from 'dayjs';
import { GitCommitOutlined, TagOutlined } from '@ali/mc-icons';
export interface CommitPopoverProps {
  gitCommit?: GitCommitBO;
  scmTag?: string;
  label?: string;
  hideIcon?: boolean;
}
const PopoverContent = ({ gitCommit, scmTag }: CommitPopoverProps) => {
  const { token } = theme.useToken();
  if (isEmpty(gitCommit)) return null;
  const { gitUrl } = gitCommit;
  let gitHttpUrl = 'http://gitlab.alibaba-inc.com';
  if (gitUrl && gitUrl.indexOf('*************************') > -1) {
    gitHttpUrl = 'http://gitlab.alipay-inc.com';
  }
  return (
    <Flex vertical key={gitCommit?.id} className={styles.popoverContentWrap}>
      <Flex>
        <Flex className={styles.labelStyle}>提交人</Flex>
        <span>{gitCommit?.authorName}</span>
      </Flex>
      <Flex>
        <Flex className={styles.labelStyle}>Commit ID</Flex>
        <a
          href={`https://code.aone.alibaba-inc.com/${gitCommit?.gitPath}/commit/${gitCommit?.id}`}
          target="_blank"
          className={styles.linkStyle}
          style={{ marginRight: 'var(--mc-margin-xs)' }}
        >
          {gitCommit?.shortId}
        </a>
      </Flex>
      <Flex>
        <Flex className={styles.labelStyle}>说明</Flex>
        <span>
          <a
            href={`https://code.aone.alibaba-inc.com/${gitCommit?.gitPath}/commit/${gitCommit?.id}`}
            target="_blank"
            className={styles.linkStyle}
            style={{ marginRight: token.marginXS }}
          >
            {gitCommit?.shortId}
          </a>
          <span> {gitCommit.message}</span>
        </span>
      </Flex>
      <Flex>
        <Flex className={styles.labelStyle}>时间</Flex>
        <span>{gitCommit?.committedDate && dayjs(gitCommit?.committedDate).format('YYYY-MM-DD HH:mm:ss')}</span>
      </Flex>
      <Flex>
        <Flex className={styles.labelStyle}>git地址</Flex>
        <span>{gitCommit?.gitUrl}</span>
      </Flex>
      {gitCommit?.branch && (
        <Flex>
          <Flex className={styles.labelStyle}>分支</Flex>
          <span>{gitCommit?.branch}</span>
          <CopyToClipboard
            text={`${gitHttpUrl}/${gitCommit?.gitPath}/tree/${gitCommit?.branch}`}
            onCopy={() => message.success('复制成功')}
          >
            <a className={styles.linkStyle} style={{ marginLeft: token.marginXS }}>
              复制分支地址
            </a>
          </CopyToClipboard>
        </Flex>
      )}
      {scmTag && (
        <Flex>
          <Flex className={styles.labelStyle} style={{ minWidth: 'fit-content' }}>
            <TagOutlined />
          </Flex>
          <span>{scmTag}</span>
          <CopyToClipboard
            text={`${gitHttpUrl}/${gitCommit?.gitPath}/tags/${scmTag}`}
            onCopy={() => message.success('复制成功')}
          >
            <a className={styles['linkStyle']} style={{ marginLeft: token.marginXS }}>
              复制tag地址
            </a>
          </CopyToClipboard>
        </Flex>
      )}
    </Flex>
  );
};
const CommitPopover = React.memo(({ gitCommit, scmTag, label, hideIcon }: CommitPopoverProps) => {
  const { token } = theme.useToken();
  // if (isEmpty(gitCommit)) return null;
  return (
    <Popover
      trigger={'hover'}
      content={isEmpty(gitCommit) ? '' : <PopoverContent gitCommit={gitCommit} scmTag={scmTag} />}
      overlayClassName={styles.commitPopover}
      title={
        isEmpty(gitCommit) ? '' : <Flex className={styles.popoverTitle} justify="flex-start" align="center">
          Commit信息
        </Flex>
      }
    >
      {gitCommit?.gitPath && gitCommit?.id && (label || gitCommit?.shortId) ? (
        <a
          href={`https://code.aone.alibaba-inc.com/${gitCommit.gitPath}/commit/${gitCommit.id}`}
          target="_blank"
        >
          {hideIcon !== true && <GitCommitOutlined style={{ marginInlineEnd: token.marginXXS }} />}
          {label || gitCommit.shortId}
        </a>
      ) : (
        <>
          {hideIcon !== true && <GitCommitOutlined style={{ marginInlineEnd: token.marginXXS }} />}
          {label || gitCommit?.shortId}
        </>
      )}
    </Popover>
  );
});

export default CommitPopover;
