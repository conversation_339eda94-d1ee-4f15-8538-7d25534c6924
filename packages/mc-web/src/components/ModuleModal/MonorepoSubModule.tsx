import { useRequest } from '@ali/mc-request';
import { IntegrateAreaModuleBO, MainModuleSubmoduleRelation } from '@ali/mc-services';
import { getMainModuleSubmodule } from '@ali/mc-services/Application';
import { getIntegrateAreaSubModules, GetIntegrateAreaSubModulesParams } from '@ali/mc-services/IntegrationArea';
import { Divider, Form, Table, theme } from 'antd';
import React, { useEffect, useState } from 'react';

interface MonorepoSubModuleProps {
  appId: number;
  moduleId: number;
  alterMode: string;
  onSubModuleChange: (subModules: MainModuleSubmoduleRelation[]) => void;
  selectedSubModules?: number[];
}
const MonorepoSubModule = (props: MonorepoSubModuleProps) => {
  const { appId, moduleId, alterMode, onSubModuleChange, selectedSubModules } = props;
  const { token } = theme.useToken();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>(selectedSubModules ?? []);
  const { runAsync: getSubModuleDependency, data: subModules } = useRequest<MainModuleSubmoduleRelation[], [number]>(
    getMainModuleSubmodule,
  );

  const { runAsync: requestIntegrateAreaSubModules } = useRequest<
    IntegrateAreaModuleBO[],
    [GetIntegrateAreaSubModulesParams]
  >(getIntegrateAreaSubModules);

  useEffect(() => {
    if (appId && moduleId) {
      Promise.all([
        getSubModuleDependency(moduleId),
        requestIntegrateAreaSubModules({
          applicationId: appId,
          mainModuleId: moduleId,
        }),
      ]).then((res) => {
        let integrateModules = res?.[1];
        let result = (integrateModules ?? []).map((item) => item?.moduleId) ?? [];
        if (result?.length) {
          setSelectedRowKeys(result as any);
          onSubModuleChange(integrateModules as any);
        }
      });
    }
  }, [appId, moduleId, getSubModuleDependency, requestIntegrateAreaSubModules, onSubModuleChange]);

  useEffect(() => {
    if (alterMode === 'DELETE') {
      let result = (subModules ?? []).map((item) => item?.moduleId);
      setSelectedRowKeys(result as any);
      onSubModuleChange(subModules as any);
    }
  }, [alterMode, onSubModuleChange, subModules]);

  const columns = [
    {
      title: '子模块名称',
      render: (record: any) => {
        return <span>{record?.module?.name}</span>;
      },
    },
    {
      title: '应用唯一标志',
      render: (record: any) => {
        return <span>{record?.module?.identifier}</span>;
      },
    },
  ];

  const rowSelection = {
    onChange: (_selectedRowKeys: string[], selectedRows: MainModuleSubmoduleRelation[]) => {
      let result = selectedRows.map((item) => item?.moduleId);
      setSelectedRowKeys(result as any);
      onSubModuleChange(selectedRows as any);
    },
    selectedRowKeys,
    getCheckboxProps() {
      return {
        disabled: alterMode === 'DELETE',
      };
    },
  };

  if (subModules?.length === 0) {
    return null;
  }

  return (
    <Form.Item name="selectedSubModules">
      <Divider style={{ marginBlock: token.marginXS }} />
      <div style={{ marginBottom: token.marginXS }}>
        添加关联子模块(共{subModules?.length}个，已选{selectedRowKeys?.length}个)：
      </div>
      <Table
        dataSource={subModules ?? []}
        columns={columns}
        rowSelection={rowSelection as any}
        pagination={{
          pageSize: 4,
        }}
        rowKey={(row) => Number(row?.moduleId)}
        scroll={{ x: true }}
      />
    </Form.Item>
  );
};
export default MonorepoSubModule;
