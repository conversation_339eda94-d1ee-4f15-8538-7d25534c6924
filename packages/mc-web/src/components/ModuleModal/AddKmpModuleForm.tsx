import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Flex, Form, FormInstance, Input, Popover, Radio, theme } from 'antd';
import { StatusTag, TaoTianApplicationSelect } from '@ali/mc-uikit';
import { ALTER_MODE_TYPE, ALTER_MODE_TYPE_MAP } from '@/constants/alterSheet';
import GitBranchSelect from '@/components/Git/GitBranchSelect';
import { camelCase, isEmpty } from 'lodash-es';
import dayjs from 'dayjs';
import { useNewMode, UseNewModeParams } from '@ali/mc-services/ContinuousIntegration';
import {
  getClientModuleBranchModalInfo,
  getClientModuleIntegrationBranchesByClient,
  getClientModuleIntegrationBranchesForDynamic,
  GetClientModuleIntegrationBranchesForDynamicParams,
} from '@ali/mc-services/Application';
import { hasOwnPropertySafe, noSpaceRule, requiredRule } from '@/util/utils';
import {
  findModuleDefaultAlterVersion,
  FindModuleDefaultAlterVersionParams,
  GetAlterModesParams,
  getAlterModes as queryAlterModes,
} from '@ali/mc-services/AlterSheet';
import { getAppConfigValue, GetAppConfigValueParams } from '@ali/mc-services/ApplicationConfiguration';
import styles from './index.module.less';
import ModuleSelector from './ModuleSelector';
import { checkPermission, CheckPermissionParams, getBranchList, GetBranchListParams } from '@ali/mc-services/Gitlab';
import BasicConfigLink from './BasicConfigLink';
import { useRequest } from '@ali/mc-request';
import { Link, useAppData } from 'ice';
import { LinkExternalOutlined, QuestionOutlined, SyncOutlined } from '@ali/mc-icons';
import MonorepoSubModule from './MonorepoSubModule';
import { SelectedModuleInfoProps } from './type';
import DeliveryEfficiencyCard from './DeliveryEfficiencyCard';
import { BranchModelInfo, GitBranchInfo, IterationVO, MainModuleSubmoduleRelation } from '@ali/mc-services';
import AoneRequestSelect from '@/components/AoneRequestSelect';
import { InfoCircleOutlined } from '@ant-design/icons';
import CheckModuleIntegrateRiskTag from '../CheckModuleIntegrateRiskTag';
interface AddModuleFormProps {
  form: FormInstance;
  iterationVO: IterationVO;
}
const AddModuleForm = ({ form, iterationVO }: AddModuleFormProps) => {
  const { token } = theme.useToken();
  const { mainEntity, workflow } = iterationVO || {};
  const pageType = camelCase(workflow?.workflowScope?.identifier ?? '');
  const { type: alterSheetType, id: alterSheetId, mode = '', applicationId, versionPlanId } = mainEntity || ({} as any);
  const appId = Number(applicationId?.toString() ?? '');
  const alterType = Form.useWatch('alterType', form);
  const branchChangeType = Form.useWatch('branchChangeType', form);
  const moduleInfo = Form.useWatch('moduleInfo', form);
  const sourceBranch = Form.useWatch('sourceBranch', form);
  const alterMode = Form.useWatch('alterMode', form);
  const branch = Form.useWatch('branch', form);
  const moduleId = moduleInfo?.moduleId;
  const appData = useAppData() || {};
  const empId = appData?.user?.empId;
  const [refreshModuleList, setRefreshModuleList] = useState<boolean>(false);
  const [selectedModuleInfo, setSelectedModuleInfo] = useState<SelectedModuleInfoProps>({
    branches: [], // 模块分支；
    branchModel: '', //
    scmAddress: '',
    integrationBranchName: '', // 模块集成分支
    useIntegrationBranchAsSourceBranch: false,
    alterModes: [],
    commonDevVersion: '',
  });

  useEffect(() => {
    if (selectedModuleInfo) {
      if (alterType === 'SOURCE') {
        form.setFieldValue('scmAddress', selectedModuleInfo?.scmAddress);
      }
    }
  }, [selectedModuleInfo, form, branchChangeType, alterType]);

  useEffect(() => {
    if (branchChangeType === 'NEW') {
      form.setFieldValue(
        'sourceBranch',
        selectedModuleInfo?.branchModel === 'INTEGRATION' ? selectedModuleInfo?.integrationBranchName : 'master',
      );
    }
  }, [branchChangeType])

  useEffect(() => {
    form.setFieldValue('branchChangeType', alterSheetType === 'AGILE_CI' ? 'NEW' : 'EXISTED');
  }, [alterSheetType, form]);

  const { runAsync: getIsUseNewMode, data: isUseNewMode = false } = useRequest<boolean, [UseNewModeParams]>(useNewMode);

  useEffect(() => {
    if (pageType === 'sdkAgileCiIntegration' && appId) {
      getIsUseNewMode({ appId });
    }
  }, [pageType, appId, getIsUseNewMode]);

  useEffect(() => {
    form.setFieldsValue({
      ...form.getFieldsValue(),
      applicationId: appId,
    });
  }, [appId, form]);

  const { runAsync: doCheckPermission } = useRequest<boolean, [CheckPermissionParams]>(checkPermission);

  // 获取模块面向某个客户端的集成分支
  const {
    runAsync: getBranchModelInfo,
    data: branchModelInfo = {},
    refresh: refreshBranchModelInfo,
  } = useRequest<BranchModelInfo, [number, number]>(getClientModuleBranchModalInfo);

  // 查找创建变更时模块默认的版本
  const { runAsync: getModuleDefaultVersion, data: defaultVersion } = useRequest<
    string,
    [FindModuleDefaultAlterVersionParams]
  >(findModuleDefaultAlterVersion);

  useEffect(() => {
    if (defaultVersion) {
      form.setFieldValue('version', defaultVersion);
    }
  }, [defaultVersion, form]);

  // 获取模块常用开发版本号
  const { runAsync: getCommonDevVersion } = useRequest<string, [GetAppConfigValueParams]>(getAppConfigValue);

  // 获取模块的变更模式
  const { runAsync: doGetAlterModes } = useRequest<string[], [GetAlterModesParams]>(queryAlterModes);

  const getAlterModes = useCallback(
    async ({ moduleId_ }: { moduleId_: number }) => {
      const res = await doGetAlterModes({
        applicationId: appId,
        moduleId: moduleId_,
        alterSheetId,
        versionPlanId,
      });
      if (res.length) {
        form.setFieldsValue({
          ...form.getFieldsValue(),
          alterMode: res?.[0],
        });
      }
      return res;
    },
    [appId, alterSheetId, versionPlanId, form, doGetAlterModes],
  );

  const { runAsync: getModuleIntegrationBranchesByClient } = useRequest<string, [number, number]>(
    getClientModuleIntegrationBranchesByClient,
  );

  const {
    runAsync: getModuleIntegrationBranchesForDynamic,
  } = useRequest<
    string, [number, number, GetClientModuleIntegrationBranchesForDynamicParams]
  >(getClientModuleIntegrationBranchesForDynamic);

  const { runAsync: getBranches } = useRequest<GitBranchInfo[], [GetBranchListParams]>(getBranchList);

  const getSelectedModuleInfo = useCallback(async ({
    moduleId_,
    codeLibraryAddress,
    branchModel,
    appId_,
  }: {
    moduleId_: number;
    codeLibraryAddress: string;
    branchModel: string;
    appId_: number;
  }) => {
    const requestList: Promise<any>[] = [
      getBranches({ scnAddress: codeLibraryAddress }),
      getCommonDevVersion({
        appId: moduleId_,
        configType: 'APPLICATION_DEV_CONFIG',
        attributeName: 'COMMON_DEVELOPMENT_VERSION',
      }),
      getAlterModes({ moduleId_ }),
    ];
    if (branchModel === 'INTEGRATION' && alterSheetId) {
      if (pageType === 'bizDynamicDevelopment') {
        requestList.push(getModuleIntegrationBranchesForDynamic(moduleId_, appId_, { alterSheetId }));
      } else {
        requestList.push(getModuleIntegrationBranchesByClient(moduleId_, appId_));
      }
    }
    const [branches, commonDevVersion, alterModes, integrationBranchName] = await Promise.all(requestList);
    return {
      branches,
      commonDevVersion,
      alterModes,
      integrationBranchName,
    };
  }, [
    alterSheetId,
    getAlterModes,
    getBranches,
    getCommonDevVersion,
    getModuleIntegrationBranchesByClient,
    getModuleIntegrationBranchesForDynamic,
    pageType,
  ]);

  const handleCommonDevVersionFill = () => {
    form.setFieldsValue({
      ...form.getFieldsValue(),
      version: selectedModuleInfo?.commonDevVersion,
    });
  };

  const onChangeSourceBranch = (value: string) => {
    // 这里有点拿不准，原来的逻辑如下：
    // onChangeSourceBranch = (value) => {
    //   const { branchModel, integrationBranchName } = this.state;
    //   if (branchModel === 'INTEGRATION' && integrationBranchName === value) {
    //     this.setState({ useIntegrationBranchAsSourceBranch: true });
    //   } else {
    //     this.setState({ useIntegrationBranchAsSourceBranch: false });
    //   }
    //   this.props.onChangeSourceBranch && this.props.onChangeSourceBranch(value);
    // };
    setSelectedModuleInfo((s_) => ({
      ...s_,
      useIntegrationBranchAsSourceBranch: !!(s_.branchModel === 'INTEGRATION' && s_.integrationBranchName === value),
    }));
  };

  const moduleInfoValidator = useCallback(
    (_rule, value, callback) => {
      if (alterType === 'BINARY') return callback();
      // if (!value) return callback();
      // let list = filterArray(selectModuleList, alterSheetModuleItemList);
      // let scmAddress = list.filter(item => { return item.moduleId === value; })[0].module.codeLibraryAddress;
      // let git = scmAddress.split(':')[1].slice(0, -4);
      // let url = scmAddress.indexOf('alibaba') !== -1 ? ` https://code.alibaba-inc.com/${git}` : `https://code.alipay.com/${git}`;
      // 以上是原来的逻辑，看不懂怎么回事。理解是取一些权限信息查询参数，直接从当前模块取就行。修改后的逻辑如下:
      const { codeLibraryAddress: scmAddress } = value?.module || {};
      const git = scmAddress.split(':')[1].slice(0, -4);
      const url =
        scmAddress.indexOf('alibaba') > 0 ? ` https://code.alibaba-inc.com/${git}` : `https://code.alipay.com/${git}`;
      const data1 = {
        scmAddress,
        empId: 'wirelessread',
      };
      const data2 = {
        scmAddress,
        empId,
        accessLevel: 'Reporter' as any,
      };

      doCheckPermission(data1).then((res) => {
        if (res) {
          doCheckPermission(data2).then((res2) => {
            if (res2) {
              callback();
            } else {
              callback(
                <span>
                  无法获取分支列表，请申请开发者以上权限
                  <SyncOutlined
                    onClick={() => {
                      doCheckPermission(data2).then((res3) => {
                        if (res3) {
                          callback();
                        }
                      });
                    }}
                  />
                  <a href={url} target="_bank">
                    {url}
                  </a>
                </span>,
              );
            }
          });
        } else {
          callback(
            <span>
              请在代码库添加 wirelessread 为管理员
              <a href={url} target="_bank">
                {url}
              </a>
            </span>,
          );
        }
      });
    },
    [alterType, doCheckPermission, empId],
  );

  const revalidateModuleId = () => {
    form
      .validateFields(['moduleInfo'])
      .then((res) => {
        console.log(res);
      })
      .catch((err) => {
        if (err) {
          return;
        }
      });
  };

  const onValuesChange = async (changedValues: any, allValues: any) => {
    if (hasOwnPropertySafe(changedValues, 'moduleInfo')) {
      const moduleInfo_ = changedValues?.moduleInfo || {};
      const moduleId_ = Number(moduleInfo_?.moduleId);
      if (moduleId_) {
        getBranchModelInfo(moduleId_, Number(appId));
        getModuleDefaultVersion({
          moduleId: moduleId_,
          alterType: allValues?.alterType,
        });
        const { module } = moduleInfo_;
        const { codeLibraryAddress, branchModel, integrationBranchName: integrationBranchName_ } = module || {};
        const { branches, commonDevVersion, alterModes, integrationBranchName } = await getSelectedModuleInfo({
          moduleId_,
          branchModel,
          codeLibraryAddress,
          appId_: pageType === 'kmpDevelopment' ? appId : allValues?.applicationId, // kmp 开发模式下, 没有appSelect 要从mainEntity 取
        });
        setSelectedModuleInfo({
          branches,
          scmAddress: codeLibraryAddress,
          integrationBranchName: branchModel === 'INTEGRATION' ? integrationBranchName : integrationBranchName_,
          useIntegrationBranchAsSourceBranch: branchModel === 'INTEGRATION',
          branchModel,
          alterModes,
          commonDevVersion,
        });
      }
      form.setFieldsValue({
        ...form.getFieldsValue(),
        branch: '',
      });
    }
    if (hasOwnPropertySafe(changedValues, 'branchChangeType')) {
      // 变更分支改变的时候，重置分支
      form.setFieldsValue({
        ...form.getFieldsValue(),
        branch: undefined,
      });
    }

    if (hasOwnPropertySafe(changedValues, 'alterType')) {
      const alterType_ = changedValues?.alterType;
      revalidateModuleId();
      if (allValues?.moduleInfo?.moduleId) {
        getModuleDefaultVersion({
          moduleId: allValues?.moduleInfo?.moduleId,
          alterType: alterType_,
        });
      }
    }

    if (hasOwnPropertySafe(changedValues, 'branch') && allValues?.branchChangeType === 'EXISTED') {
      if (selectedModuleInfo?.branches?.length > 0) {
        onChangeSourceBranch(changedValues?.branch);
      }
    }

    if (hasOwnPropertySafe(changedValues, 'sourceBranch') && allValues?.branchChangeType === 'NEW') {
      onChangeSourceBranch(changedValues?.sourceBranch);
    }
  };

  const onSubModuleChange = useCallback(
    (subModules: MainModuleSubmoduleRelation[]) => {
      form.setFieldValue('selectedSubModules', subModules);
    },
    [form],
  );

  const basicConfigLinkType = useMemo(() => {
    if (!isEmpty(selectedModuleInfo)) {
      const { branchModel } = selectedModuleInfo;
      if (alterSheetType === 'AGILE_CI' && selectedModuleInfo?.branchModel !== 'INTEGRATION') {
        return 'open';
      }
      if (
        alterSheetType === 'AGILE_CI' &&
        branchModel === 'INTEGRATION' &&
        !branchModelInfo.branchModel &&
        !branchModelInfo.integrationBranch
      ) {
        return 'update';
      }
      if (pageType !== 'sdkVerifyIntegration' && branchModel !== 'INTEGRATION') {
        return 'link';
      }
    }
    return '';
  }, [selectedModuleInfo, alterSheetType, branchModelInfo, pageType]);

  const onConfirm = useCallback(
    (integrationBranchName: string) => {
      if (!basicConfigLinkType) return;
      if (basicConfigLinkType === 'open') {
        setRefreshModuleList((s) => !s);
      }
      if (basicConfigLinkType === 'update') {
        refreshBranchModelInfo?.();
      }
      setSelectedModuleInfo((s) => ({
        ...s,
        useIntegrationBranchAsSourceBranch: true,
        branchModel: 'INTEGRATION',
        integrationBranchName,
      }));
      form.setFieldValue('sourceBranch', integrationBranchName);
      form.setFieldValue('moduleInfo', {
        ...form.getFieldsValue()?.moduleInfo,
        module: {
          ...form.getFieldsValue()?.moduleInfo?.module,
          useIntegrationBranchAsSourceBranch: true,
          branchModel: 'INTEGRATION',
          integrationBranchName,
        },
      });
    },
    [form, refreshBranchModelInfo, basicConfigLinkType],
  );

  const handleBasicConfigLinkRefresh = useCallback(() => {
    setRefreshModuleList((s) => !s);
    refreshBranchModelInfo?.();
  }, [refreshBranchModelInfo]);

  return (
    <Form form={form} layout="vertical" onValuesChange={onValuesChange} className={styles.addModuleForm} clearOnDestroy>
      {pageType === 'kmpDevelopment' ? (
        <Form.Item label="对应客户端">
          <TaoTianApplicationSelect disabled placeholder="不支持选择" />
        </Form.Item>
      ) : (
        <Form.Item name="applicationId" label="对应客户端">
          <TaoTianApplicationSelect disabled />
        </Form.Item>
      )}
      <Form.Item name="alterType" label="变更类型" initialValue={'SOURCE'}>
        <Radio.Group>
          <Radio value="SOURCE">源码依赖</Radio>
          <Radio value="BINARY">外部依赖</Radio>
        </Radio.Group>
      </Form.Item>
      {
        alterSheetType !== 'MAIN_FRAMEWORK' && (
          <Form.Item
            name="moduleInfo"
            label={
              <Flex gap="small" align="flex-end">
                <span>模块选择</span>
                {pageType === 'kmpDevelopment' && <span style={{ color: token.colorTextSecondary }}>（此处仅支持选择跨端模块）</span>}
                {mainEntity?.application?.platformType === 'IOS' && <Popover
                  title="源码构建"
                  overlayStyle={{
                    width: 400,
                  }}
                  content={
                    <>
                      iOS 模块已支持源码构建模式，当模块开启该模式时，模块前将显示 <StatusTag bordered muted>支持源码构建</StatusTag>，【整包】构建会引入模块源码执行构建。
                      <Link to="https://yuque.alibaba-inc.com/mtl-cloud/handbook/hvh4vsmunyk3oipo" target="_blank">
                        <Flex>
                          了解更多
                          <LinkExternalOutlined />
                        </Flex>
                      </Link>
                    </>
                  }
                >
                  <Flex
                    align="center"
                    gap={token.marginXXS}
                    style={{
                      fontSize: token.fontSizeSM,
                      color: selectedModuleInfo?.supportSourceCodeBuild ? token.colorLink : token.colorWarning
                    }}
                  >
                    iOS 模块源码构建
                    <QuestionOutlined />
                  </Flex>
                </Popover>}
              </Flex>
            }
            rules={[
              {
                required: true,
                message: '请选择模块',
              },
              {
                validator: moduleInfoValidator,
              },
            ]}
          >
            <ModuleSelector
              showSearch
              placeholder="请选择模块"
              appId={Number(appId)}
              alterType={alterType}
              alterSheetMode={mode}
              shouldRefresh={refreshModuleList}
              alterSheetModuleItemList={mainEntity?.alterSheetModuleList ?? []}
              filterOption={(input, option) => {
                const inputValue = input?.toLowerCase();
                const moduleInfo = option?.data?.module || {};
                return ((moduleInfo?.name ?? '')).toLowerCase().includes(inputValue) || ((moduleInfo?.depKey ?? '')).toLowerCase().includes(inputValue);
              }}
              showSourceBuildTag
              pageType={pageType}
            />
          </Form.Item>
        )
      }
      {
        alterSheetType !== 'MAIN_FRAMEWORK' && (
          <Form.Item
            name="version"
            label={
              <Flex>
                <Flex>版本</Flex>
                <Flex>
                  {alterType === 'SOURCE' &&
                    moduleId !== undefined &&
                    (selectedModuleInfo?.commonDevVersion ? (
                      <a style={{ marginLeft: '8px' }} onClick={handleCommonDevVersionFill}>
                        填入常用开发版本号
                      </a>
                    ) : (
                      <a
                        style={{ marginLeft: '8px' }}
                        href={`#/app/${moduleId}/detail/setting/?tab=other`}
                        target="_blank"
                      >
                        {'如需设置常用开发版本号，可前往设置>>>'}
                      </a>
                    ))}
                </Flex>
              </Flex>
            }
            rules={[
              {
                required: true,
                message: '请输入版本',
              },
              noSpaceRule('版本'),
            ]}
          >
            <Input placeholder="请输入版本号" />
          </Form.Item>
        )
      }
      {/* 代码库地址：alterType === 'SOURCE'时展示，选中模块后自动填入且不允许修改 */}
      {
        alterType === 'SOURCE' && (
          <Form.Item name="scmAddress" label="代码库地址">
            <Input placeholder="请输入代码库地址" disabled />
          </Form.Item>
        )
      }
      {
        alterSheetType !== 'MAIN_FRAMEWORK' && (
          <Form.Item name="alterMode" label="变更模式">
            <Radio.Group className={styles.alterModeRadioGroup}>
              {isEmpty(selectedModuleInfo?.alterModes) ? (
                <Radio>{'未获取到数据'}</Radio>
              ) : (
                (selectedModuleInfo?.alterModes ?? []).map((alterMode_) => (
                  <Radio
                    value={alterMode_}
                    key={alterMode_}
                    disabled={alterMode_ === 'DELETE' && alterType === 'SOURCE'}
                    style={{ marginBottom: 'var(--mc-margin-xs)' }}
                  >
                    {ALTER_MODE_TYPE_MAP[alterMode_ as ALTER_MODE_TYPE] || alterMode_}
                    {alterMode_ === 'DELETE' && alterType === 'SOURCE'
                      ? '(该选项只能通过外部依赖方式进行，请重新选择成“外部依赖”)'
                      : ''}
                  </Radio>
                ))
              )}
            </Radio.Group>
          </Form.Item>
        )
      }
      {/* 变更分支 */}
      {
        alterType === 'SOURCE' && (
          <Form.Item
            name="branchChangeType"
            label={
              <Flex justify="flex-start" align="center" gap="8px">
                <Flex>变更分支</Flex>
                {moduleId && (
                  <Flex key={moduleId}>
                    <BasicConfigLink
                      type={basicConfigLinkType}
                      appId={applicationId}
                      moduleId={moduleId}
                      branchModalInfoId={branchModelInfo?.id}
                      scmAddress={selectedModuleInfo?.scmAddress}
                      onConfirm={onConfirm}
                      refresh={handleBasicConfigLinkRefresh}
                    />
                  </Flex>
                )}
              </Flex>
            }
          >
            {/* 后端不消费此字段 */}
            <Radio.Group>
              <Radio value="NEW">新建分支</Radio>
              <Radio value="EXISTED">已有分支</Radio>
            </Radio.Group>
          </Form.Item>
        )
      }
      {
        branchChangeType === 'EXISTED' && (
          <>
            {selectedModuleInfo?.branches?.length === 0 && (
              <Form.Item
                name="branch"
                rules={[requiredRule, noSpaceRule('分支名')]}
                extra={
                  <CheckModuleIntegrateRiskTag
                    versionPlanId={versionPlanId}
                    moduleId={moduleId}
                    useNewMode={isUseNewMode}
                    branch={branch}
                  />
                }
              >
                <Input placeholder="请输入分支名" />
              </Form.Item>
            )}
            {selectedModuleInfo?.branches?.length > 0 && (
              <Form.Item
                name="branch"
                rules={[{ required: true, message: '请选择已有分支' }]}
                extra={
                  <CheckModuleIntegrateRiskTag
                    versionPlanId={versionPlanId}
                    moduleId={moduleId}
                    useNewMode={isUseNewMode}
                    branch={branch}
                  />
                }
              >
                <GitBranchSelect scmAddress={selectedModuleInfo?.scmAddress} />
              </Form.Item>
            )}
          </>
        )
      }
      {/* 变更分支选择-新建分支-基于源分支：
       *** 1、选中的分支为集成分支：extra展示【当前选中的分支为集成分支】，分支名称固定加前缀，同时更新当前模块的branchModel信息（BasicConfigLink要用到）；
       *** 2、选中的分支为非集成分支，分支名称随意
       */}
      {
        branchChangeType === 'NEW' && (
          <>
            <Form.Item
              label="基于源分支/标签"
              name="sourceBranch"
              rules={[{ required: true, message: '请选择源分支/标签' }]}
              extra={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {selectedModuleInfo?.useIntegrationBranchAsSourceBranch && sourceBranch ? (
                    <div style={{ marginRight: 8 }}>{'当前选中的分支为集成分支'}</div>
                  ) : null}
                  <CheckModuleIntegrateRiskTag
                    versionPlanId={versionPlanId}
                    moduleId={moduleId}
                    useNewMode={isUseNewMode}
                    // 原逻辑:
                    // branch={selectedModuleInfo?.branchModel === 'INTEGRATION'
                    // ? selectedModuleInfo?.integrationBranchName : 'master'}
                    // 新逻辑:
                    branch={sourceBranch}
                  />
                </div>
              }
            >
              <GitBranchSelect
                style={{ width: '100%' }}
                scmAddress={selectedModuleInfo?.scmAddress}
              />
            </Form.Item>
            <Form.Item hasFeedback={false} name="branch" rules={[{ required: true, message: '请填写分支名称' }]}>
              {selectedModuleInfo?.useIntegrationBranchAsSourceBranch ? (
                <Input addonBefore={`feature/${dayjs().format('YYYYMMDD')}_变更单id_`} />
              ) : (
                <Input placeholder="请填写分支名称" />
              )}
            </Form.Item>
          </>
        )
      }

      {/* 当前模块变更所对应的需求 */}
      {
        pageType !== 'kmpDevelopment' && (
          <Form.Item
            name="aoneRequestItems"
            label="当前模块变更所对应的需求"
            style={{
              paddingBottom: token.padding,
            }}
            help={<Flex gap={token.paddingXXS}>
              <InfoCircleOutlined />
              应PMO要求，需求必须关联到对应的变更模块。
            </Flex>}
            rules={[
              {
                required: true,
                message: '请选择需求',
              }]}
          >
            <AoneRequestSelect />
          </Form.Item>
        )
      }
      {/* mc中没有MAIN_FRAMEWORK类型的迭代了，下面的逻辑先注释掉 */}
      {/* {alterSheetType === 'MAIN_FRAMEWORK' && (
      <Form.Item
        hasFeedback={false}
        label="集成区选择"
        name="integrateAreaId"
        rules={[requiredRule]}
      >
        <IntegrateAreaSelector appId={appId} />
      </Form.Item>
    )}
    {alterSheetType === 'MAIN_FRAMEWORK' && (
      <Form.Item
        hasFeedback={false}
        label="变更分支"
        name="branch"
        rules={[requiredRule, noSpaceRule('分支名')]}
      >
        <Input
          addonBefore="dev/迭代ID_"
          placeholder="请填写分支名称"
        />
      </Form.Item>
    )} */}
      {
        (pageType === 'sdkAgileCiIntegration' || pageType === 'sdkVerifyIntegration') && applicationId && moduleId && (
          <div className={styles.addModuleFormExtra}>
            <MonorepoSubModule
              appId={appId}
              moduleId={moduleId}
              alterMode={alterMode}
              onSubModuleChange={onSubModuleChange}
            />
            <DeliveryEfficiencyCard applicationId={appId} moduleId={moduleId} />
          </div>
        )
      }
    </Form >
  );
};

export default React.memo(AddModuleForm);
