/* eslint-disable max-len */
import React, { useEffect } from 'react';
import { Popover, theme } from 'antd';
import Markdown from 'react-markdown';
import rehypeExternalLinks from 'rehype-external-links';
import { StatusTag } from '@ali/mc-uikit';
import { useRequest } from '@ali/mc-request';
import { CopilotChatAgentDTO, CopilotChatAgentResultDTO } from '@ali/mc-services';
import { getCopilotChatAgentResult } from '@ali/mc-services/CopilotChat';
import { RightOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import styles from './index.module.less';

type CodeReviewAgentTagProps = {
  /**
   * AI 评审建议
   */
  agentResult?: CopilotChatAgentResultDTO;
  /**
   * 变更单 ID
   */
  alterSheetId?: number;
  /**
   * 模块 ID
   */
  moduleId?: number;
  /**
   * Merge Request ID
   */
  mergeRequestId?: number;
  /**
   * slot
   */
  rightSlot?: React.ReactNode;
};
export default function CodeReviewAgentTag(props: CodeReviewAgentTagProps) {
  const { token } = theme.useToken();
  const { agentResult, alterSheetId, moduleId, mergeRequestId, rightSlot } = props;
  const { data: agentResponse, run: requestCopilotChatAgentResult } = useRequest<CopilotChatAgentResultDTO, [CopilotChatAgentDTO]>(
    getCopilotChatAgentResult,
  );
  const data = agentResult ?? agentResponse;

  useEffect(() => {
    if (!agentResult && alterSheetId && moduleId && mergeRequestId) {
      requestCopilotChatAgentResult({
        agentIdentifier: 'CodeReviewAgent',
        contentMeta: {
          alterSheetId,
          mergeRequestId,
          moduleId,
        },
      });
    }
  }, [agentResult, alterSheetId, moduleId, mergeRequestId, requestCopilotChatAgentResult]);

  if (!data?.latestInstanceStatus || !['FINAL', 'TEMP'].includes(data?.latestInstanceStatus)) return null;

  let title: string;
  let text: string;
  let color: string;
  if (data?.latestInstanceStatus === 'FINAL') {
    if (data?.latestInstanceSubStatus === 'PASS') {
      title = 'AI 评审概述';
      text = 'AI 评审建议通过';
      color = 'purple';
    } else {
      title = 'AI 评审概述';
      text = 'AI 评审建议谨慎通过';
      color = 'orange';
    }
  } else {
    title = '正在生成评审建议';
    text = '正在生成评审建议';
    color = 'blue';
  }

  if (!data) return null;

  return (
    <>
      <StatusTag className={styles.aicrTag} color={color} bordered={false}>
        <span>{text}</span>
        <Popover
          placement="topLeft"
          content={
            data?.latestInstanceTooltip && (
              <>
                <Markdown rehypePlugins={[[rehypeExternalLinks, { target: '_blank' }]]}>
                  {data.latestInstanceTooltip.replace('${code_summary}', '暂时未产出相应的代码总结。')}
                </Markdown>
                <a
                  style={{ display: 'inline-block', marginTop: token.marginSM }}
                  href="https://yuque.alibaba-inc.com/mtl/ro4u3y/apilivsz3v9pmv7b"
                  target="_blank"
                  rel="noreferrer"
                >
                  如对我们生成的评审意见存在任何问题，请在此反馈 <RightOutlined />{' '}
                </a>
              </>
            )
          }
          title={<strong>{title}</strong>}
          overlayClassName={styles.overlayStyle}
          arrow={{ pointAtCenter: true }}
        >
          <QuestionCircleOutlined />
        </Popover>
      </StatusTag>
      {rightSlot}
    </>
  );
}
