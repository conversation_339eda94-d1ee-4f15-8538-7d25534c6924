import { Flex, Skeleton, theme } from 'antd';
import React, { useEffect } from 'react';
import { findAppById } from '@ali/mc-services/Application';
import { ApplicationVO } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';

interface AppProps {
  appId?: number | string;
  showAvatar?: boolean;
}

const App = (props: AppProps) => {
  const { appId, showAvatar = false } = props;
  const { token } = theme.useToken();

  const { data, loading, runAsync: requestFindAppById } = useRequest<
    ApplicationVO, [number]
  >(findAppById);

  useEffect(() => {
    if (appId) {
      requestFindAppById(Number(appId));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appId]);

  if (loading) return <Skeleton.Input size="small" />;
  return (
    <Flex align="center">
      <a style={{ color: token.colorText }} target="_blank" href={`#/app/${appId}/detail/`} rel="noopener noreferrer">
        {showAvatar && (
          <img
            style={{
              width: 20,
              height: 20,
              borderRadius: token.borderRadiusSM,
              marginRight: token.marginXS,
            }}
            src={data?.iconUrl}
          />
        )}
        <span>{data?.name === undefined ? appId : data?.name}</span>
      </a>
    </Flex>
  );
};

export default App;
