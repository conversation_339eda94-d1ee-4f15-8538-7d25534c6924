import React, { useEffect, useState, useCallback } from 'react';

import { Typography, Spin, type SelectProps } from 'antd';
import { FindAoneRequestListParams, findAoneRequestList, queryAssignedToOrCreatedAoneRequestPage, QueryAssignedToOrCreatedAoneRequestPageParams } from '@ali/mc-services/Aone';
import { AoneRequest, PaginationResult } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import { PrefixSelect, StatusTag } from '@ali/mc-uikit';
const { Text } = Typography;
const STAMP_TYPE_MAP: Record<string, React.ReactNode> = {
  Req: <StatusTag bordered={false} muted color="processing">需求</StatusTag>,
  Bug: <StatusTag bordered={false} muted color="warning">缺陷</StatusTag>,
};
interface AoneOption extends AoneRequest {
  requirementId?: number;
  requirementName?: string;
}
interface SpaceScopeSelectProps extends SelectProps {
  defaultOptions?: AoneOption[];
  id?: string;
}

const RenderLabelItem = ({ item }: { item: any }) => {
  const { stamp, reqName } = item ?? {};
  return (<Text style={{ width: '100%' }} ellipsis={{ tooltip: reqName }}><span>{STAMP_TYPE_MAP[stamp as string]}</span> {reqName}</Text>);
};

const AoneRequestSelect = (props: SpaceScopeSelectProps) => {
  const { onChange: onChanges, value: propsValue, ...otherProps } = props;


  const [options, setOptions] = useState<any[]>([]);

  const {
    runAsync: requestAoneRequestList,
    loading,
  } = useRequest<AoneRequest[], [FindAoneRequestListParams]>(findAoneRequestList);

  const {
    runAsync: requestAoneRequirements,
    loading: getAoneRequirementLoading,
  } = useRequest<PaginationResult<AoneRequest>, [QueryAssignedToOrCreatedAoneRequestPageParams]>(
    queryAssignedToOrCreatedAoneRequestPage,
    {
      debounceWait: 300,
    },
  );


  const getOptions = useCallback((res: any) => {
    if (res?.length > 0) {
      setOptions(res);
    } else {
      setOptions([]);
    }
  }, []);

  const getOneselfRequestAoneRequirements = useCallback(async ({ search }: FindAoneRequestListParams): Promise<void> => {
    const res = await requestAoneRequestList({ search });
    getOptions(res);
  }, [getOptions, requestAoneRequestList]);

  const getAllRequestAoneRequirements = useCallback(async ({ search }: QueryAssignedToOrCreatedAoneRequestPageParams): Promise<void> => {
    const params: QueryAssignedToOrCreatedAoneRequestPageParams = {
      search,
      pageNum: 0,
      pageSize: 10,
    };
    const res = await requestAoneRequirements(params);
    const { items } = res ?? {};
    getOptions(items);
  }, [getOptions, requestAoneRequirements]);

  useEffect(() => {
    getOneselfRequestAoneRequirements({ search: '' });
  }, []);

  const handleOptionChange = (selectedOptions: any) => {
    const transformedResult = selectedOptions?.map(item => ({
      ...item?.info,
      value: item?.value,
      label: item?.info?.reqName,
    }));
    if (onChanges) {
      onChanges(transformedResult);
    }
  };

  return (<PrefixSelect
    id={otherProps?.id}
    showSearch
    placeholder="支持关键字搜索，请选择"
    showArrow
    mode="multiple"
    style={{ width: '100%' }}
    notFoundContent={(loading || getAoneRequirementLoading) ? <Spin size="small" /> : null}
    onSearch={(name) => {
      if (name.length > 0) {
        getAllRequestAoneRequirements({ search: name });
      } else {
        getOneselfRequestAoneRequirements({ search: '' });
      }
    }}
    loading={loading || getAoneRequirementLoading}
    filterOption={false}
    {...({
      otherProps,
      onChange: (_, option) => {
        handleOptionChange(option);
      },
    }
    )}
    labelRender={(props_: any) => {
      const { label: item } = props_;
      return <RenderLabelItem item={item} />;
    }}
    options={(options ?? []).map(item => ({
      value: item?.id,
      info: item,
      label: <RenderLabelItem item={item} />,
    }))}
    optionLabelProp="info"
    value={propsValue?.map((item: any) => parseInt(item?.value, 10))}
  />
  );
};

export default AoneRequestSelect;
