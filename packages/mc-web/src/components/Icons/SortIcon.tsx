import * as React from 'react';
import type { SVGProps } from 'react';
const SvgSortIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    viewBox="0 0 7.293882369995117 3.896474838256836"
    className="anticon"
    focusable="false"
    width="1em"
    height="1em"
    aria-hidden="true"
    {...props}
  >
    <path
      d="M0.0737243,0.42678L3.4702,3.8232C3.56783,3.9209,3.72612,3.9209,3.82375,3.8232L7.22014,0.42678C7.37764,0.26928,7.26614,0,7.04334,0L0.250504,0C0.0277739,0,-0.083766,0.26929,0.0737243,0.42678Z"
      fill="currentColor"
      fillOpacity={1}
    />
  </svg>
);
export default SvgSortIcon;
