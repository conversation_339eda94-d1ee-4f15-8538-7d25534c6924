import React, { useEffect, useMemo, useState, forwardRef, useImperativeHandle, useCallback } from 'react';
import styles from './ModuleRegister.module.less';

import {
  USAGE_TYPE,
  PLATFORM_TYPE,
  DEPKEY_PLACEHOLDER,
  MAVEN_TYPE,
  HARMONY_TYPE,
  CROSS_PLATFORM_TYPE,
} from '../common/moduleFormMap';

import {
  Button,
  Checkbox,
  Flex,
  Form,
  Input,
  Radio,
  Select,
  Tooltip,
  theme,
  message,
  Modal,
  Divider,
  SelectProps,
} from 'antd';
import { ChevronDownOutlined, ChevronRightOutlined, InfoOutlined, QuestionOutlined } from '@ali/mc-icons';
import { values } from 'lodash-es';
import { GitProjectInput, UserSelect } from '@ali/mc-uikit';

import { ApplicationBO, ApplicationRegisterRequest, RepoCheckResult } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import {
  PreCheckRepoParams,
  createApp,
  findAppById,
  getHarmonyGroupResult,
  preCheckRepo,
} from '@ali/mc-services/Application';
import { useAppData } from 'ice';
import { _ } from 'numeral';

function ModelTips() {
  const { token } = theme.useToken();

  return (
    <Flex className={styles.formTips_content} gap={token.marginXS} vertical>
      <span>
        1.默认为无分支模式，即不开启“集成分支模式”，模块的代码分支由用户线下自行管理，代码分支拉取、代码合并等均线下手动操作
      </span>
      <span>
        2.开启“集成分支模式”，模块的代码分支由平台规范化管理，模块的代码分支拉取和合并等在平台上操作，模块发布正式版本之前以及模块集成成功之后需要进行代码合并
      </span>
      <span>3.如果需要修改模块的分支管理模式，可到模块的基本配置内修改</span>
      <a href={'https://yuque.antfin-inc.com/mtl4/product/qwa8b6#Knnb8'} target="_blank" rel="noreferrer">
        {'点击查看详情>>'}
      </a>
    </Flex>
  );
}

function BranchTips() {
  const { token } = theme.useToken();
  return (
    <Flex className={styles.formTips_content} gap={token.marginXS} vertical>
      <span>
        模块的代码分支由平台规范化管理，模块的代码分支拉取和合并等在平台上操作，模块发布正式版本之前以及模块集成成功之后需要进行代码合并
      </span>
      <a href={'https://yuque.antfin-inc.com/mtl4/product/qwa8b6#WT2oM'} target="_blank" rel="noreferrer">
        {'点击查看详情>>'}
      </a>
    </Flex>
  );
}

interface ModuleRegisterProps {
  platformType: PLATFORM_TYPE;
  scaffoldId?: number | 'noCli';
}

export type RegisterModuleResult = {
  moduleInfo: ApplicationBO;
  requestParams: ApplicationRegisterRequest
};

const ModuleRegister = (
  props: ModuleRegisterProps,
  ref: React.Ref<{ registerModule: () => Promise<RegisterModuleResult> }>,
) => {
  const { platformType, scaffoldId } = props;
  const [form] = Form.useForm();
  const { token } = theme.useToken();
  const [modal, contextHolder] = Modal.useModal();
  const { user } = useAppData() || {};

  const needCreateGitProject = Form.useWatch('needCreateGitProject', form);
  const moduleGroupTagSupport = Form.useWatch('moduleGroupTagSupport', form);

  const [showMoreForm, setShowMoreForm] = useState<boolean>(false);
  const [gitNameSpaceId, setGitNameSpaceId] = useState<number>();

  const {
    data: harmonyGroupResult = {},
    runAsync: requestHarmonyGroupResult,
    loading: getHarmonyGroupResultLoading,
  } = useRequest<Record<string, string>, []>(getHarmonyGroupResult); // 获取鸿蒙模块的分组
  const {
    data: preCheckResult = {
      existed: true,
      initialized: false,
    },
    runAsync: requestPreCheckRepo,
  } = useRequest<RepoCheckResult, [PreCheckRepoParams]>(preCheckRepo, {
    debounceMaxWait: 300,
  }); // 应用代码库预校验
  const { runAsync: requestCreateApp } = useRequest<number, [ApplicationRegisterRequest]>(createApp); // 创建应用
  const { runAsync: requestModuleInfo } = useRequest<ApplicationBO, [number]>(findAppById);

  const moreFormItem = useMemo(() => {
    // if (!showMoreForm) return null;
    return (
      <Form.Item noStyle hidden={!showMoreForm}>
        <Form.Item
          label={platformType === PLATFORM_TYPE.HARMONY ? '产品标志' : '唯一标志'}
          name="identifier"
          rules={[
            { required: true },
            {
              pattern: /^[a-zA-Z0-9-_]+$/,
              message: '产品标识必须是英文、数字、中划线、下划线构成',
            },
          ]}
        >
          <Input
            placeholder={
              platformType === PLATFORM_TYPE.HARMONY
                ? '默认系统会根据GroupID和ArtifactID自动填写，可修改'
                : `英文标志，${platformType === PLATFORM_TYPE.ANDROID ? 'Android 填写 ArtifactId' : 'iOS 填写 cocoapods 模块名称'}`
            }
          />
        </Form.Item>
        {![PLATFORM_TYPE.HARMONY, PLATFORM_TYPE.CROSS_PLATFORM].includes(platformType) && (
          <Form.Item
            label={platformType === PLATFORM_TYPE.ANDROID ? 'Bundle名' : '包名'}
            name="packageName"
            rules={[{ required: true }]}
          >
            <Input placeholder="如：com.taobao.taobao" />
          </Form.Item>
        )}
        {platformType === PLATFORM_TYPE.IOS && (
          <Form.Item label="库类型：" name="isDynamic" rules={[{ required: true }]}>
            <Radio.Group>
              <Radio value={false}>静态库</Radio>
              <Radio value>动态库</Radio>
            </Radio.Group>
          </Form.Item>
        )}
        {![PLATFORM_TYPE.HARMONY, PLATFORM_TYPE.CROSS_PLATFORM].includes(platformType) && (
          <Form.Item label="模块是否跨终端/平台：" name="moduleGroupTagSupport">
            <Radio.Group disabled={platformType === PLATFORM_TYPE.CROSS_PLATFORM}>
              <Radio value={false}>单一终端</Radio>
              <Radio value>跨终端</Radio>
            </Radio.Group>
          </Form.Item>
        )}
        {moduleGroupTagSupport && (
          <Form.Item label="模块构建模式：" name="crossPlatformIntegrateType" rules={[{ required: true }]}>
            <Radio.Group>
              <Radio value="MixIn">
                <Flex gap={4}>
                  <span>混合模式</span>
                  <Tooltip
                    placement="top"
                    title="构建策略：同时生成“进包产物”（集成至手淘包）与“远程化产物”（独立交付）。（2）运行逻辑：应用启动时优先加载远程化服务接口的Bundle版本，若远程服务存在则执行远程逻辑；若无远程服务则自动启用进包逻辑。"
                  >
                    <QuestionOutlined />
                  </Tooltip>
                </Flex>
              </Radio>
              <Radio value="PureRemote">
                <Flex gap={4}>
                  <span>仅远程化模式</span>
                  <Tooltip
                    placement="top"
                    title="构建策略：仅生成“进包产物”（无进手淘包集成）。（2）运行逻辑：应用启动时检测远程化服务接口的Bundle版本，若远程服务存在则执行远程逻辑;若无远程服务则不执行任何逻辑。"
                  >
                    <QuestionOutlined />
                  </Tooltip>
                </Flex>
              </Radio>
            </Radio.Group>
          </Form.Item>
        )}
        {moduleGroupTagSupport && (
          <Form.Item label="模块基础标识：" name="moduleGroupTag" help="多个跨终端产品需使用相同标识，推荐驼峰式命名">
            <Input placeholder="如：AliDatabaseES" />
          </Form.Item>
        )}
        <Form.Item label="保密类型：" name="secretType" rules={[{ required: true }]}>
          <Radio.Group>
            <Radio value="PUBLIC">公开</Radio>
            <Radio value="PRIVATE">集团机密，不能被搜索</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="代码仓库类型：" name="scmType" rules={[{ required: true }]}>
          <Select>
            <Select.Option value="GIT">Git</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item
          label={
            <Flex align="center">
              <span>分支管理模式：</span>
              <Tooltip placement="top" title={<ModelTips />} color={token.colorBgBase}>
                <InfoOutlined />
              </Tooltip>
            </Flex>
          }
          name="branchModel"
        >
          <Flex>
            <Checkbox>集成分支模式</Checkbox>
            <Tooltip placement="top" title={<BranchTips />} color={token.colorBgBase}>
              <InfoOutlined />
            </Tooltip>
          </Flex>
        </Form.Item>
        <Form.Item label="唯一归属责任人：" name="owner" rules={[{ required: true }]}>
          <UserSelect />
        </Form.Item>
        <Form.Item label="管理员：" name="admins" rules={[{ required: true }]}>
          <UserSelect />
        </Form.Item>
        <Form.Item label="开发人员：" name="developers">
          <UserSelect />
        </Form.Item>
        <Form.Item label="测试人员：" name="testers">
          <UserSelect />
        </Form.Item>
      </Form.Item>
    );
  }, [showMoreForm, platformType, moduleGroupTagSupport]);

  useImperativeHandle(ref, () => ({
    registerModule: () => {
      return new Promise((resolve, reject) => {
        form
          .validateFields()
          .then((res) => {
            modal.confirm({
              title: '注册提示',
              content:
                '产品注册成功之后，会自动跳转至下一步进行构建流水线等应用配置，我们会为您设置默认配置，但在正式使用前，您仍需在流水线配置中重新设置构建模板及流水线模板的插件参数',
              onOk: () => {
                let requestParams = {
                  ...res,
                  gitNameSpaceId,
                  platformType,
                  branchModel: 'FREE',
                  type: 'MODULE',
                  moduleType: platformType === PLATFORM_TYPE.IOS ? 'SDK' : 'BUNDLE',
                  appScaffoldId: scaffoldId === 'noCli' ? null : scaffoldId,
                };

                requestCreateApp(requestParams)
                  .then((appId) => {
                    message.success('注册成功！');
                    return requestModuleInfo(appId);
                  })
                  .then((data: ApplicationBO) => {
                    resolve({
                      moduleInfo: data,
                      requestParams,
                    });
                  })
                  .catch((err) => {
                    console.error(err);
                    reject();
                  });
              },
            });
          })
          .catch((err) => {
            const hideFormFields = [
              'identifier',
              'packageName',
              'isDynamic',
              'secretType',
              'scmType',
              'owner',
              'admins',
            ];
            if (err.errorFields.some((item: { name: string[] }) => hideFormFields.includes(item.name[0]))) {
              setShowMoreForm(true);
            }
            reject(false);
          });
      });
    },
  }));

  const repoPreCheck = useCallback((address?: string) => {
    if (address) {
      requestPreCheckRepo({
        codeLibraryAddress: address,
      });
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // 自动完整填入depKey
  const autoFillDepKey = () => {
    const formData = form.getFieldsValue();
    if (platformType === PLATFORM_TYPE.IOS) {
      const { identifier, name } = formData;
      if (identifier) {
        form.setFieldsValue({
          depKey: identifier,
        });
      }
      if (name) {
        form.setFieldsValue({
          identifier: name,
          packageName: name,
          depKey: name,
        });
      }
    } else {
      const { groupId, artifactId } = formData;
      if (artifactId) {
        form.setFieldsValue({
          identifier: `${artifactId}`,
        });
      }

      if (groupId) {
        form.setFieldsValue({
          packageName: `${groupId}`,
        });
      }

      if (groupId && artifactId) {
        if (platformType === PLATFORM_TYPE.HARMONY) {
          form.setFieldsValue({
            depKey: `${groupId}/${artifactId}.version`,
            identifier: `${groupId}/${artifactId}`,
          });
        } else {
          form.setFieldsValue({
            depKey: `${groupId}.${artifactId}.version`,
          });
        }
      }
    }
  };

  useEffect(() => {
    if (platformType === PLATFORM_TYPE.HARMONY) {
      requestHarmonyGroupResult();
    }

    const defaultValue: any = {
      needCreateGitProject: true,
      isDynamic: platformType === PLATFORM_TYPE.IOS ? false : undefined,
      secretType: 'PUBLIC',
      scmType: 'GIT',
      owner: user.empId,
      admins: user.empId,
    };
    if (platformType === PLATFORM_TYPE.CROSS_PLATFORM) {
      defaultValue.gavType = CROSS_PLATFORM_TYPE.KLIB.key;
      defaultValue.moduleGroupTagSupport = true;
    }

    form.setFieldsValue(defaultValue);
  }, []);

  const productTypes = useMemo(() => {
    const items: SelectProps['options'] = [];
    switch (platformType) {
      case PLATFORM_TYPE.ANDROID:
        items.push(...values(MAVEN_TYPE));
        break;
      case PLATFORM_TYPE.HARMONY:
        items.push(...values(HARMONY_TYPE));
        break;
      case PLATFORM_TYPE.CROSS_PLATFORM:
        // items.push(...values(MAVEN_TYPE));
        items.push(...values(CROSS_PLATFORM_TYPE));
        break;
    }

    return items.map((item) => {
      return {
        label: item.label,
        value: item.key,
      };
    });
  }, [platformType]);

  return (
    <Flex justify="center">
      <Form
        name="basic"
        form={form}
        layout="vertical"
        className={styles.moduleRegister_formBox}
        initialValues={{ remember: true }}
        autoComplete="off"
        style={{ width: '80%' }}
      >
        {contextHolder}
        <Form.Item label="适用用途：" name="usage" rules={[{ required: true }]}>
          <Radio.Group>
            <Flex vertical>
              {Object.entries(USAGE_TYPE).map(([key, val]) => (
                <Radio value={val.value} key={key}>
                  {val.label}
                </Radio>
              ))}
            </Flex>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="模块名称"
          name="name"
          rules={[{ required: true }, { max: 64, message: '模块名称不能超过64个字符' }]}
        >
          <Input placeholder="如: AliDatabaseES4XXX" onBlur={autoFillDepKey} />
        </Form.Item>
        <Form.Item label="模块描述" name="description" rules={[{ required: true }]}>
          <Input.TextArea
            autoSize={{ minRows: 5 }}
            placeholder={
              platformType === PLATFORM_TYPE.HARMONY ? '填写模块信息' : '填写产品描述以及App何时上线、业务内容等'
            }
          />
        </Form.Item>
        {[PLATFORM_TYPE.ANDROID, PLATFORM_TYPE.HARMONY, PLATFORM_TYPE.CROSS_PLATFORM].includes(platformType) && (
          <>
            <Form.Item
              label="产物类型"
              name={platformType === PLATFORM_TYPE.HARMONY ? 'harmonyPackageType' : 'gavType'}
              rules={[{ required: true }]}
            >
              <Select
                placeholder="请选择产物类型"
                options={productTypes}
                disabled={platformType === PLATFORM_TYPE.CROSS_PLATFORM}
              />
            </Form.Item>
            <Form.Item label="GroupId" name="groupId" rules={[{ required: true }]}>
              {platformType === PLATFORM_TYPE.HARMONY ? (
                <Select
                  loading={getHarmonyGroupResultLoading}
                  placeholder={'如taobao-ohos 创建后无法修改，请慎重填写'}
                  onBlur={autoFillDepKey}
                >
                  {Object.entries(harmonyGroupResult).map(([key, value]) => {
                    return (
                      <Select.Option key={key} value={key}>
                        {`${key}    (${value})`}
                      </Select.Option>
                    );
                  })}
                </Select>
              ) : (
                <Input
                  placeholder={`如：${platformType !== PLATFORM_TYPE.ANDROID ? 'com.taobao.android' : 'taobao-ohos'}，创建后无法修改，请慎重填写`}
                  onBlur={autoFillDepKey}
                />
              )}
            </Form.Item>
            <Form.Item
              label="ArtifactId"
              name="artifactId"
              rules={[
                { required: true },
                ...(platformType === PLATFORM_TYPE.HARMONY
                  ? [
                    {
                      pattern: /^[a-z0-9_-]+$/,
                      message: 'Harmony ArtifactId只能由小写英文和数字以及中下划线组成',
                    },
                  ]
                  : []),
              ]}
            >
              <Input
                placeholder={`如：${platformType !== PLATFORM_TYPE.ANDROID ? 'tbuikit' : 'taobao-android'}，创建后无法修改，请慎重填写`}
                onBlur={autoFillDepKey}
              />
            </Form.Item>
          </>
        )}
        <Form.Item
          label="DepKey："
          name="depKey"
          rules={[{ required: true }]}
          help={`默认系统会根据${platformType === PLATFORM_TYPE.IOS ? '产品名称和产品标志' : 'GroupID和ArtifactID'}自动填写，可修改`}
        >
          <Input placeholder={DEPKEY_PLACEHOLDER[platformType]} />
        </Form.Item>
        <Form.Item label="代码仓库地址：" name="needCreateGitProject" rules={[{ required: true }]}>
          <Radio.Group>
            <Radio value>平台创建</Radio>
            <Radio value={false}>已有仓库</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name="codeLibraryAddress"
          help={
            needCreateGitProject
              ? '请将wirelessread添加为该代码组的管理员，便于平台进行代码仓库创建等操作。'
              : preCheckResult?.existed
                ? preCheckResult?.initialized && scaffoldId !== 'noCli'
                  ? '检测到该仓库已经被初始化，无需选择脚手架模板'
                  : '请将wirelessread添加为该代码组的管理员，便于平台进行代码仓库创建等操作。'
                : '代码仓库不存在 请确认输入是否正确'
          }
          validateStatus={
            needCreateGitProject
              ? ''
              : preCheckResult?.existed
                ? preCheckResult?.initialized && scaffoldId !== 'noCli'
                  ? 'warning'
                  : ''
                : 'error'
          }
        >
          <GitProjectInput
            placeholder="请输入 Git 仓库地址"
            fullAddress
            useExist={!needCreateGitProject}
            style={{ width: '100%' }}
            onGroupChange={(_, option: any) => {
              const group = option?.data || {};
              setGitNameSpaceId(group?.id);
            }}
            onChange={(val) => {
              if (!needCreateGitProject) {
                repoPreCheck(val);
              }
            }}
          />
        </Form.Item>
        <Divider orientation="left" plain>
          <Button type="link" onClick={() => setShowMoreForm(!showMoreForm)}>
            更多配置
            {showMoreForm ? <ChevronDownOutlined /> : <ChevronRightOutlined />}
          </Button>
        </Divider>
        {moreFormItem}
      </Form>
    </Flex>
  );
};

export default forwardRef(ModuleRegister);
