import React, { useCallback, useEffect, useState } from 'react';
import { Flex, Form, Input, Modal, Popconfirm, Table, TableProps, theme, Tooltip, Typography } from 'antd';
import { GitBranchSelect, PrefixSelect } from '@ali/mc-uikit';
import { ALTER_TYPE, ALTER_TYPE_MAP } from '@/constants/alterSheet';
import { MainModuleSubmoduleRelation, UserModuleRelation } from '@ali/mc-services';
import AlterModeField from './AlterModeField';
import ModuleSelector from './ModuleSelector';
import { useCreateCasualBuildContext } from './CreateCasualBuildProvider';
import styles from './index.module.less';
import SubmoduleList from './SubmoduleList';
import { omit } from 'lodash-es';
import { getAppConfigValue, GetAppConfigValueParams } from '@ali/mc-services/ApplicationConfiguration';
import { useRequest } from '@ali/mc-request';
import { GearOutlined, GitBranchOutlined, PencilOutlined } from '@ali/mc-icons';
import ModuleFlowModal from '../ModuleModal/ModuleFlowModal';
import { getMainModuleSubmodule } from '@ali/mc-services/Application';
import { findModuleDefaultAlterVersion, FindModuleDefaultAlterVersionParams, getAlterModes, GetAlterModesParams } from '@ali/mc-services/AlterSheet';

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: any;
  record: UserModuleRelation;
  index: number;
  disabled: boolean;
  required: boolean;
}
const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
  editing,
  dataIndex,
  title,
  record,
  disabled,
  required,
  children,
  ...restProps
}) => {
  const form = Form.useFormInstance();
  const alterType = Form.useWatch('alterType', form);
  const { token } = theme.useToken();
  const { setFieldValue } = form;

  // 查找模块默认版本
  const { runAsync: getModuleDefaultVersion } = useRequest<
    string,
    [FindModuleDefaultAlterVersionParams]
  >(findModuleDefaultAlterVersion);

  // 获取模块常用开发版本号
  const {
    runAsync: getCommonDevVersion,
    data: commonDevVersion,
  } = useRequest<string, [GetAppConfigValueParams]>(getAppConfigValue);

  useEffect(() => {
    if (dataIndex === 'version' && record?.moduleId && editing) {
      getCommonDevVersion({
        appId: record?.moduleId,
        configType: 'APPLICATION_DEV_CONFIG',
        attributeName: 'COMMON_DEVELOPMENT_VERSION',
      });
    }
  }, [dataIndex, record?.moduleId, getCommonDevVersion, editing]);

  useEffect(() => {
    if (dataIndex === 'version' && record?.moduleId && alterType && editing) {
      getModuleDefaultVersion({
        moduleId: record?.moduleId,
        alterType,
      }).then(res => {
        setFieldValue('version', res);
      });
    }
  }, [alterType, record?.moduleId, editing, getModuleDefaultVersion, dataIndex, record?.version, setFieldValue]);

  const handleCommonDevVersionFill = useCallback(() => {
    if (commonDevVersion) {
      form.setFieldValue('version', commonDevVersion);
    }
  }, [form, commonDevVersion]);

  let inputNode = null;
  switch (dataIndex) {
    case 'alterType':
      inputNode = (<PrefixSelect
        style={{
          width: '96px',
        }}
        options={[{
          label: '源码依赖',
          value: 'SOURCE',
        }, {
          label: '外部依赖',
          value: 'BINARY',
        }]}
        onChange={(value) => {
          if (value === 'BINARY') {
            form.setFieldValue('branch', undefined);
          }
        }}
      />);
      break;
    case 'version':
      inputNode = <Input placeholder="请输入版本号" style={{ width: '96px' }} />;
      break;
    case 'codeLibraryAddress':
      inputNode = <Input placeholder="请输入代码库地址" disabled={disabled} />;
      break;
    case 'branch':
      inputNode = alterType === 'BINARY' ? '-' : (<GitBranchSelect
        style={{ width: '170px' }}
        scmAddress={record?.module?.codeLibraryAddress ?? ''}
      />);
      break;
    default:
      break;
  }

  return (
    <td {...restProps}>
      {editing ? (
        <Flex align="center" gap={token.marginXS}>
          <Form.Item
            name={dataIndex}
            style={{ margin: 0 }}
            initialValue={dataIndex === 'alterType' ? 'SOURCE' : undefined}
            rules={[
              {
                required,
                message: `请输入${title}!`,
              },
            ]}
          >
            {inputNode}
          </Form.Item>
          {
            dataIndex === 'version' && <>
              {(commonDevVersion ? (
                <Tooltip title="填入常用开发版本号">
                  <PencilOutlined
                    style={{ color: token.colorTextSecondary, cursor: 'pointer' }}
                    onClick={handleCommonDevVersionFill}
                  />
                </Tooltip>
              ) : (
                <Tooltip title="如需设置常用开发版本号，可前往设置>>>">
                  <GearOutlined
                    style={{ color: token.colorTextSecondary, cursor: 'pointer' }}
                    onClick={() => {
                      window.open(`${window.location.origin}/#/app/${record?.moduleId}/detail/setting/?tab=other`, '_blank');
                    }}
                  />
                </Tooltip>
              ))}
            </>
          }
        </Flex>

      ) : (
        children
      )}
    </td>
  );
};

interface EditableModuleTableProps {
  applicationId: number;
  onDisableOkChange: (v: boolean) => void;
}

const EditableModuleTable = (props: EditableModuleTableProps) => {
  const { applicationId, onDisableOkChange } = props;
  const [form] = Form.useForm();
  const alterType = Form.useWatch('alterType', form);
  const { token } = theme.useToken();
  const [editingKey, setEditingKey] = useState<number>();
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([]);
  const {
    depModuleList,
    depModuleListLoading,
    setDepModuleList,
    setSelectedModuleIds,
    selectedModuleIds,
    setSelectedSubModulesRecord,
  } = useCreateCasualBuildContext();

  const [data, setData] = useState<UserModuleRelation[]>(depModuleList ?? []);
  const isEditing = (record: UserModuleRelation) => record.moduleId === editingKey;

  useEffect(() => {
    onDisableOkChange?.(editingKey !== undefined);
  }, [editingKey, onDisableOkChange]);

  const edit = (record: Partial<UserModuleRelation>) => {
    form.setFieldsValue({
      alterType: record?.alterType,
      version: record?.version,
      branch: record?.alterType === 'BINARY' ? undefined : record?.branch,
    });
    setEditingKey(record.moduleId);
  };

  const cancel = () => {
    setEditingKey(undefined);
  };

  const save = useCallback(async (key?: number) => {
    try {
      const row = (await form.validateFields()) as UserModuleRelation;
      const newData = [...(data ?? [])];
      const finalRow = {
        ...omit(row, [key ?? -1]),
        ...(key && { alterMode: row?.[key]?.alterMode }),
      };
      const index = newData.findIndex((item) => item.moduleId === key);

      // 更新表格数据
      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, {
          ...item,
          ...finalRow,
        });
        setData(newData);
        setDepModuleList(newData);
        setEditingKey(undefined);
      }
    } catch (errInfo) {
      console.error(errInfo);
    }
  }, [data, form, setDepModuleList]);

  const handleExpand = (rowKey: number) => {
    setExpandedRowKeys(prev => {
      if (prev?.includes(rowKey)) {
        return (prev ?? []).filter(i => i !== rowKey);
      } else {
        return [...prev, rowKey];
      }
    });
  };

  const handleOpenModuleFlow = (moduleInfo: UserModuleRelation) => {
    const isShowDynamicPublishBranch = moduleInfo?.module?.publishBranch?.length > 0;
    const isShowModuleFlow = moduleInfo?.module?.branchModel === 'INTEGRATION' || moduleInfo?.module?.publishBranch?.length > 0;
    Modal.info({
      footer: null,
      // width: 624,
      maskClosable: true,
      icon: null,
      closable: true,
      styles: {
        body: {
          maxHeight: 'calc(100vh - 250px)',
          overflow: 'auto',
          padding: 0,
        },
        content: {
          width: 672,
        },
      },
      className: styles.moduleFlowModal,
      title: '分支活动图',
      content: <ModuleFlowModal
        applicationId={applicationId}
        moduleId={moduleInfo?.moduleId}
        codeLibraryAddress={moduleInfo?.module?.codeLibraryAddress}
        isShowDynamicPublishBranch={isShowDynamicPublishBranch}
        isShowModuleFlow={isShowModuleFlow}
      />,
    });
  };

  const columns = [
    {
      title: '模块名称',
      dataIndex: 'name',
      editable: false,
      width: 160,
      render: (_: any, record: any) => {
        return (<Flex gap={token.marginXS} align="center">
          {record?.subModules?.length > 0
            ? <div
                className={`${styles.expandIconWrap} ${expandedRowKeys?.includes(record?.moduleId) ? '' : styles['expandIconCollapsed']}`}
                onClick={() => handleExpand(record?.moduleId)}
            /> : <div style={{ width: token.controlHeightXS }} />}
          <Flex gap={token.marginXS}>
            <div>
              {record?.module?.name ?? '-'}
            </div>
            <Flex>
              <Tooltip title="查看活动">
                <GitBranchOutlined
                  style={{
                    color: token.colorTextSecondary,
                    cursor: 'pointer',
                  }}
                  onClick={() => handleOpenModuleFlow(record)}
                />
              </Tooltip>
            </Flex>

          </Flex>
        </Flex>);
      },
    },
    {
      title: '变更类型',
      dataIndex: 'alterType',
      editable: true,
      width: 60,
      render: (_: any, record: any) => {
        return <div style={{ wordBreak: 'break-all' }}>{ALTER_TYPE_MAP[record?.alterType as ALTER_TYPE] ?? '-'}</div>;
      },
    },
    {
      title: '版本',
      dataIndex: 'version',
      width: 80,
      minWidth: 80,
      editable: true,
      render: (_: any, record: any) => {
        return <div style={{ wordBreak: 'break-all' }}>{record?.version ?? '-'}</div>;
      },
    },
    {
      title: '变更分支',
      dataIndex: 'branch',
      editable: true,
      width: 100,
      render: (_: any, record: any) => {
        return <div style={{ wordBreak: 'break-all' }}>{record?.branch ?? '-'}</div>;
      },
    },
    {
      title: '变更模式',
      dataIndex: 'alterMode',
      width: 90,
      render: (_: any, record: UserModuleRelation & { alterModeList: string[]}) => {
        const editable = isEditing(record);
        return (<AlterModeField
          isEditing={editable}
          moduleId={record?.moduleId}
          alterMode={record?.alterMode}
          dataSource={record?.alterModeList}
          form={form}
          setData={setData}
        />);
      },
    },
    {
      title: '代码库地址',
      dataIndex: 'codeLibraryAddress',
      editable: false,
      width: 100,
      render: (_: any, record: any) => {
        return (<Typography.Paragraph
          style={{ wordBreak: 'break-all' }}
          ellipsis={{
            tooltip: record?.module?.codeLibraryAddress ?? '-',
            rows: 2,
          }}
        >
          {record?.module?.codeLibraryAddress ?? '-'}
        </Typography.Paragraph>);
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 20,
      className: styles.operationColumnStyle,
      render: (_: any, record: UserModuleRelation) => {
        const editable = isEditing(record);
        return (<div style={{ wordBreak: 'break-all', width: '80px' }}>
          {
            editable ? (
              <>
                <Typography.Link onClick={() => save(record?.moduleId)} style={{ marginInlineEnd: 8 }}>
                  保存
                </Typography.Link>
                <Popconfirm title="确定取消编辑？" onConfirm={cancel}>
                  <a>取消</a>
                </Popconfirm>
              </>
            ) : (
              <Typography.Link disabled={editingKey !== undefined} onClick={() => edit(record)}>
                编辑
              </Typography.Link>
            )
          }
        </div>);
      },
    },
  ];


  const mergedColumns: TableProps<UserModuleRelation>['columns'] = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: UserModuleRelation) => ({
        record: {
          ...record,
          applicationId,
        },
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
        required: col.dataIndex === 'branch' && alterType === 'BINARY' ? false
          : record?.moduleId && selectedModuleIds ? selectedModuleIds?.includes(record?.moduleId)
            : false,
      }),
    };
  });
  const {
    runAsync: getSubmodules,
  } = useRequest<MainModuleSubmoduleRelation[], [number]>(getMainModuleSubmodule);

  const {
    runAsync: getAlterModesList,
  } = useRequest<string[], [GetAlterModesParams]>(getAlterModes);

  return (<Flex vertical gap={token.marginXS}>
    <ModuleSelector
      showSearch
      style={{ width: '100%' }}
      placeholder="请输入模块名称、depKey搜索"
      appId={applicationId}
      allowClear
      filterOption={(input, option) => {
        return (option?.data?.module?.name ?? '').toLowerCase().includes(input.toLowerCase()) ||
          (option?.data?.module?.depKey ?? '').toLowerCase().includes(input.toLowerCase());
      }}
      hiddenList={depModuleList ?? []}
      onSelect={(_, record) => {
        const { data: module } = record;
        if (module?.moduleId) {
          Promise.all([
            getSubmodules(module?.moduleId),
            getAlterModesList({
              moduleId: module?.moduleId,
              applicationId,
            }),
          ]).then(resArr => {
            const [subModules, alterModeList] = resArr;
            setData(prev => {
              if (prev?.find(item => item?.moduleId === module?.moduleId)) {
                return prev;
              } else {
                return [{
                  ...module,
                  empId: module?.module?.creator,
                  alterModeList,
                  ...(subModules?.length > 0 && { subModules: subModules.map(item => ({
                    ...item,
                    alterType: 'BINARY',
                  })) }),
                }, ...(prev ?? [])];
              }
            });
            setDepModuleList(prev => {
              if (prev?.find(item => item?.moduleId === module?.moduleId)) {
                return prev;
              } else {
                return [{
                  ...module,
                  empId: module?.module?.creator,
                  alterModeList,
                  ...(subModules?.length > 0 && { subModules: subModules.map(item => ({
                    ...item,
                    alterType: 'BINARY',
                  })) }),
                }, ...(prev ?? [])];
              }
            });
          });
        }
      }}
    />
    <div
      style={{
        fontSize: token.fontSize,
        fontWeight: token.fontWeightStrong,
      }}
    >
      我选过的模块
    </div>
    <Form form={form} component={false}>
      <Table
        loading={depModuleListLoading}
        components={{
          body: { cell: EditableCell },
        }}
        rowKey={r => r?.moduleId ?? 0}
        bordered
        dataSource={data}
        className={styles.editableModuleTable}
        expandable={{
          showExpandColumn: false,
          expandedRowKeys,
          rowExpandable: (record) => record?.subModules && record?.subModules?.length > 0,
          expandedRowRender: (record) => (<SubmoduleList
            key={`${record?.moduleId}_subModule_list`}
            dataSource={record?.subModules ?? []}
            mainModuleId={record?.moduleId}
          />),
        }}
        columns={mergedColumns}
        rowClassName="editable-row"
        pagination={{ onChange: cancel }}
        rowSelection={{
          preserveSelectedRowKeys: true,
          selectedRowKeys: selectedModuleIds,
          onSelect: (record, selected) => {
            if (record.moduleId) {
              if (selected && !selectedModuleIds?.includes(record?.moduleId)) {
                setSelectedModuleIds(prev => [...(prev ?? []), record?.moduleId ?? -1]);
              } else {
                setSelectedModuleIds(prev => (prev ?? []).filter(r => r !== record?.moduleId));
                setSelectedSubModulesRecord(prev => {
                  if (record?.moduleId) {
                    return omit(prev, [record?.moduleId]);
                  }
                  return prev;
                });
              }
            }
          },
          onSelectAll: (selected, _selectedRows, changeRows) => {
            if (selected) {
              (changeRows ?? []).forEach(newRow => {
                if (newRow?.moduleId && !selectedModuleIds?.includes(newRow?.moduleId)) {
                  setSelectedModuleIds(prev => [...(prev ?? []), newRow?.moduleId ?? -1]);
                }
              });
            } else {
              (changeRows ?? []).forEach(deleteRow => {
                setSelectedModuleIds(prev => (prev ?? []).filter(r => r !== deleteRow?.moduleId));
                setSelectedSubModulesRecord(prev => {
                  if (deleteRow?.moduleId) {
                    return omit(prev, [deleteRow?.moduleId]);
                  }
                  return prev;
                });
              });
            }
          },
        }}
      />
    </Form>
  </Flex>);
};

export default EditableModuleTable;
