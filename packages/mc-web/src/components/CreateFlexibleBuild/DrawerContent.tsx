import { Button, Drawer, Flex, Form, message, Popconfirm, theme, Typography } from 'antd';
import React, { useCallback, useState } from 'react';
import { DrawerContentProps } from './type';
import { PipelineExecuteParams, PipelineInitRequest } from '@ali/mc-services';
import { useRequest } from '@ali/mc-request';
import { omit } from 'lodash-es';
import { removeFalsy } from '@/util/utils';
import { useAppData, useNavigate } from 'ice';
import { executePipelineWithParams, ExecutePipelineWithParamsParams, getExecuteParams, GetExecuteParamsParams, initPipeline } from '@ali/mc-services/CasualBuild';
import { useCreateCasualBuildContext } from './CreateCasualBuildProvider';
import CreateForm from './CreateForm';
import PipelineConfig from './PipelineConfig';
type ParamsVO = Omit<PipelineInitRequest, 'dependencyType'> & {
  dependencyType?: 'RELEASE_PLAN' | 'RELEASE_PRODUCT' | 'INTEGRATE_AREA';
  spaceId?: number;
};

const DrawerContent = ({ open, onClose: propOnClose, refresh, spaceId, personalSpaceId, personalSpaceName }: DrawerContentProps) => {
  const [form] = Form.useForm();
  const { token } = theme.useToken();
  const { user } = useAppData() || {};
  const [step, setStep] = useState<'create' | 'pipelineDetail' | 'pipelineEdit'>('create');
  const [pipelineId, setPipelineId] = useState<number>();
  const {
    selectedModuleList,
    selectedAppBuildTemplates,
    selectedModuleTemplates,
    resetStates,
  } = useCreateCasualBuildContext();
  const navigate = useNavigate();

  const onClose = useCallback(() => {
    propOnClose?.();
    if (step !== 'create' && personalSpaceId !== spaceId) {
      navigate(`/teamspaces/${personalSpaceId}/builds`);
    }
  }, [propOnClose, step, navigate, spaceId, personalSpaceId]);

  const {
    runAsync: doInitPipeline,
    loading: confirmLoading,
  } = useRequest<number, [PipelineInitRequest]>(initPipeline);

  const {
    runAsync: requestCasualBuildExecuteParams,
    loading: requestCasualBuildExecuteParamsLoading,
  } = useRequest<PipelineExecuteParams[], [GetExecuteParamsParams]>(getExecuteParams);

  const {
    runAsync: executeCasualBuild,
    loading: executeCasualBuildLoading,
  } = useRequest<boolean, [ExecutePipelineWithParamsParams, PipelineExecuteParams[]]>(executePipelineWithParams);


  const handleSubmit = useCallback(() => {
    form.validateFields().then(async (values) => {
      const params: ParamsVO = omit(values, ['modules', 'appBuildTemplates']);
      let finalModules: any = [];
      (selectedModuleList ?? []).forEach((module: any) => {
        finalModules.push({
          ...(omit(module, ['module', 'clientId', 'creator', 'gmtCreate', 'gmtModified', 'modifier', 'id', 'submodules'])),
          needBuild: module?.alterType === 'SOURCE',
          selectedStageNames: selectedModuleTemplates[module?.moduleId] ?? [],
        });

        // 遍历子模块
        if (module.subModules) {
          module.subModules.forEach((subModule: any) => {
            // 为每个子模块添加mainModule的alterMode和version
            let subModuleWithMode = {
              ...(omit(subModule, ['module', 'gmtCreate', 'creator'])),
              alterMode: module?.alterMode,
              alterType: 'BINARY',
              needBuild: false,
              version: module?.version,
            };
            finalModules.push(subModuleWithMode);
          });
        }
      });

      params.modules = finalModules;
      params.selectedStageNames = selectedAppBuildTemplates ?? [];
      if (selectedAppBuildTemplates?.length > 0) {
        params.needBuild = true;
      } else {
        params.needBuild = false;
      }

      // if (params.modules?.length === 0 && params.selectedStageNames?.length === 0) {
      //   message.error('请至少选择一个整包构建或模块构建');
      //   throw new Error('请至少选择一个整包构建或模块构建');
      // }
      params.empId = user?.empId;
      params.dependencyType = params?.dependencyType === 'RELEASE_PLAN' ? undefined : params?.dependencyType;
      doInitPipeline(removeFalsy(params)).then(pipelineId_ => {
        if (pipelineId_) {
          setStep('pipelineDetail');
          setPipelineId(pipelineId_);
          refresh?.();
        }
      });
    }).catch(err => {
      console.error(err);
    });
  }, [
    selectedModuleTemplates,
    form,
    user,
    selectedAppBuildTemplates,
    doInitPipeline,
    refresh,
    // navigate,
    // spaceId,
    selectedModuleList,
  ]);

  const handleBuild = useCallback(() => {
    if (pipelineId && spaceId) {
      requestCasualBuildExecuteParams({
        pipelineId,
      }).then((executeParams) => {
        const uuidExecuteParams = (executeParams ?? []).map(params => ({
          ...params,
      }));
      const needBuildData = uuidExecuteParams.filter(i => i.needBuild);
      if (needBuildData.length) {
        executeCasualBuild({ pipelineId }, needBuildData).then(res => {
          if (res) {
            message.success('执行成功');
            onClose();
            refresh?.();
          }
        });
      }
      });
    }
  }, [pipelineId, spaceId, executeCasualBuild, onClose, refresh, requestCasualBuildExecuteParams]);

  return (<Drawer
    open={open}
    onClose={onClose}
    destroyOnClose
    afterOpenChange={(v) => {
      if (!v) {
        setStep('create');
        setPipelineId(undefined);
        form.resetFields();
        resetStates?.();
      }
    }}
    zIndex={998}
    title={<div style={{ fontSize: token.fontSize }}>
      自由构建
      {/* {<Button
        onClick={() => {
          setStep(s => {
            if (s === 'create') {
              return 'pipelineDetail';
            } else {
              return 'create';
            }
          });
        }}
      >
        {step}
      </Button>} */}
    </div>}
    push={false}
    styles={{
      wrapper: {
        width: '100%',
      },
      body: {
        padding: '0px',
        height: 'calc(100vh - 64px * 2)',
      },
    }}
    footer={<Flex style={{ width: '100%', justifyContent: 'center', alignItems: 'center' }} gap={token.margin}>
      {
        step === 'create' && <>
          <Button onClick={onClose}>
            取消
          </Button>
          <Popconfirm
            title={null}
            description={
              <Typography.Text>
                生成流水线后，当前页面不可返回，是否确认当前信息并生成？
              </Typography.Text>
            }
            onConfirm={handleSubmit}
          >
            <Button type="primary" loading={confirmLoading}>
              生成流水线
            </Button>
          </Popconfirm>
        </>
      }
      {
        ['pipelineDetail', 'pipelineEdit'].includes(step) && <>
          <Button onClick={onClose}>
            放弃构建
          </Button>
          <Button
            type="primary"
            disabled={step === 'pipelineEdit'}
            onClick={handleBuild}
            loading={executeCasualBuildLoading || requestCasualBuildExecuteParamsLoading}
          >
            运行构建
          </Button>
        </>
      }
    </Flex>}
  >
    {
      open && step === 'create' && <CreateForm
        confirmLoading={confirmLoading}
        spaceId={spaceId}
        form={form}
        personalSpaceId={personalSpaceId}
        personalSpaceName={personalSpaceName}
      />
    }
    {
      open && ['pipelineDetail', 'pipelineEdit'].includes(step) && pipelineId && (
      <PipelineConfig
        pipelineId={pipelineId}
        step={step as 'pipelineDetail' | 'pipelineEdit'}
        setStep={setStep}
        executeCasualBuildLoading={executeCasualBuildLoading || requestCasualBuildExecuteParamsLoading}
      />)
    }
  </Drawer>);
};

export default DrawerContent;
