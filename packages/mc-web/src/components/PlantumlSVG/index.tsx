import React, { useEffect, useState, useMemo, forwardRef } from 'react';
import { Spin, Empty, theme } from 'antd';
import { getBaseUrl } from '@ali/mc-services';
import defaultStyles from './index.module.less';
import './index.less';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com');

/**
 * 对text映射列表做处理（去除结尾的;符号）
 */
export function processNodeText(text: string) {
  return text.replace(/;$/g, '');
}

/**
 * plantuml svg
 * @param {code} plantuml code
 * @param {keyList} 需要支持点击事件的文本列表
 * @param {onClick} 点击事件，传参text
 */
type PlantumlSVGProps = {
  code?: string;
  keyList?: string[];
  activeKey?: string;
  className?: string;
  styles?: React.CSSProperties;
  onClick?: (text: string) => void;
  onMount?: () => void;
};

function PlantumlSVG(props: PlantumlSVGProps, ref: React.Ref<HTMLDivElement>) {
  const { keyList = [], code = '', activeKey, className, styles, onClick, onMount = () => {} } = props;
  const [svgXML, setSvgXML] = useState('');
  const [loading, setLoading] = useState(false);
  const { token } = theme.useToken();

  const list = useMemo(() => {
    return keyList.map((x) => processNodeText(x));
  }, [keyList]);

  // 通过 code 获取SVG
  useEffect(() => {
    if (code) {
      setLoading(true);
      fetch(`${baseUrl}/ai/code/chart/plantuml/svg`, {
        method: 'POST',
        body: code,
      })
        .then((res) => res?.text())
        .then((text) => setSvgXML(text))
        .finally(() => setLoading(false));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code]);

  // 被点击的节点高亮展示
  useEffect(() => {
    Array.from(document?.getElementsByTagName('text')).forEach((item) => {
      const text = (item.textContent ?? '').trimEnd();
      if (text === activeKey) {
        item.setAttribute('class', 'plantumlNodeActive');
      } else {
        item.setAttribute('class', 'plantumlNode');
      }
    });
  }, [activeKey]);

  // 绑定点击事件和点击样式
  useEffect(() => {
    onMount();
    if (typeof onClick === 'function') {
      Array.from(document?.getElementsByTagName('text'))?.forEach((item) => {
        const text = (item.textContent ?? '').trimEnd();
        const isTextNode = list.includes(text);
        if (isTextNode) {
          item.setAttribute('class', 'plantumlNode');
          item?.addEventListener('click', () => {
            Array.from(document?.getElementsByClassName('plantumlNodeActive'))?.forEach((activeItem) => {
              activeItem.setAttribute('class', 'plantumlNode');
            });
            item.setAttribute('class', 'plantumlNodeActive');
            onClick(text);
          });
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [svgXML]);

  return (
    <Spin wrapperClassName={defaultStyles.plantumlSVGSpinWrapper} tip="画布加载中" spinning={loading}>
      {svgXML ? (
        <div
          ref={ref}
          className={className}
          style={{
            width: '100%',
            height: '100%',
            minWidth: '200px',
            minHeight: '100px',
            ...styles,
          }}
          dangerouslySetInnerHTML={{ __html: svgXML }}
        />
      ) : (
        <Empty style={{ marginTop: token.margin, marginBottom: token.margin }} />
      )}
    </Spin>
  );
}

export default forwardRef(PlantumlSVG);
