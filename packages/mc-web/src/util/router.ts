// import { getBasename } from '@ice/stark-app';
import { Params, matchPath as reactRouterMatchPath } from 'react-router-dom';

/**
 * 目前 ICE 的 dataLoader 拿到的 ctx 是没有根据router 解析的，这里需要手动解析
 * @param routePath 路由规则
 * @param pathname 当前路径
 * @returns
 */
export function matchRouteParams(routePath: string, pathname: string): Params<string> | undefined {
  // const basename = getBasename();
  const basename = '/';
  const realRoutePath = basename === '/' ? routePath : `${basename}${routePath}`;
  const match = reactRouterMatchPath({
    path: realRoutePath,
  }, pathname);

  return match?.params;
}

export function isTopRouterChange(routePath: string, pathPrefix: string) {
  // const basename = getBasename();
  const basename = '/';
  const realRoutePath = basename === '/' ? routePath : `${basename}${routePath}`;
  return !pathPrefix.startsWith(realRoutePath);
}

