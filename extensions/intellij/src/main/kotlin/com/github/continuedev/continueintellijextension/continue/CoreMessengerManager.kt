package com.github.continuedev.continueintellijextension.`continue`

import com.github.continuedev.continueintellijextension.services.TelemetryService
import com.github.continuedev.continueintellijextension.utils.castNestedOrNull
import com.github.continuedev.continueintellijextension.utils.getContinueBinaryPath
import com.github.continuedev.continueintellijextension.utils.getMachineUniqueID
import com.intellij.openapi.components.service
import com.intellij.openapi.observable.properties.AtomicProperty
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.*

class CoreMessengerManager(
    private val project: Project,
    private val ideProtocolClient: IdeProtocolClient,
    private val coroutineScope: CoroutineScope
) {
    private val logger = LoggerManager.getLogger(CoreMessengerManager::class.java)

    var coreMessenger = AtomicProperty<CoreMessenger?>(null)
    private var lastBackoffInterval = 0.5
    private var retryJob: Job? = null

    @Volatile
    private var isDisposed = false

    init {
        coroutineScope.launch {
            val continueBinaryPath = getContinueBinaryPath()
            setupCoreMessenger(continueBinaryPath)
        }
    }

    private fun setupCoreMessenger(continueCorePath: String) {
        try {
            coreMessenger.set(CoreMessenger(project, continueCorePath, ideProtocolClient, coroutineScope))

            coreMessenger.get()?.request("config/getSerializedProfileInfo", null, null) { response ->
                val allowAnonymousTelemetry = response.castNestedOrNull<Boolean>("content", "result", "config", "allowAnonymousTelemetry")

                val telemetryService = service<TelemetryService>()
                if (allowAnonymousTelemetry == true || allowAnonymousTelemetry == null) {
                    telemetryService.setup(getMachineUniqueID())
                }
            }

            // On exit, use exponential backoff to create another CoreMessenger
            coreMessenger.get()?.onDidExit {
                if (!isDisposed) {
                    lastBackoffInterval *= 2
                    logger.info("CoreMessenger exited, retrying in $lastBackoffInterval seconds")

                    // 使用协程进行重试，而不是阻塞线程
                    retryJob?.cancel()
                    retryJob = coroutineScope.launch {
                        try {
                            delay((lastBackoffInterval * 1000).toLong())
                            if (!isDisposed) {
                                setupCoreMessenger(continueCorePath)
                            }
                        } catch (ignore: CancellationException) {
                            logger.info("Retry cancelled")
                        }
                    }
                }
            }
        } catch (err: Throwable) {
            val telemetryService = service<TelemetryService>()
            telemetryService.capture("jetbrains_core_start_error", mapOf("error" to err))

            logger.warn("Exception occurred", err)
        }
    }

    /**
     * 清理资源，停止重试机制
     */
    fun dispose() {
        logger.info("start dispose")
        isDisposed = true
        retryJob?.cancel()
        coreMessenger.get()?.killSubProcess()
        coreMessenger.set(null)
    }
}
