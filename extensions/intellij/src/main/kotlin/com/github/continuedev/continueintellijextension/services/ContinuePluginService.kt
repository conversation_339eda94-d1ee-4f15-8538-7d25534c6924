package com.github.continuedev.continueintellijextension.services

import com.github.continuedev.continueintellijextension.`continue`.CoreMessenger
import com.github.continuedev.continueintellijextension.`continue`.CoreMessengerManager
import com.github.continuedev.continueintellijextension.`continue`.DiffManager
import com.github.continuedev.continueintellijextension.`continue`.IdeProtocolClient
import com.github.continuedev.continueintellijextension.toolWindow.ContinuePluginToolWindowFactory
import com.github.continuedev.continueintellijextension.utils.uuid
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.EDT
import com.intellij.openapi.components.Service
import com.intellij.openapi.observable.properties.AtomicProperty
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.wm.ToolWindow
import com.intellij.ui.jcef.JBCefBrowser
import com.intellij.ui.jcef.executeJavaScript
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import javax.swing.JComponent
import kotlin.properties.Delegates

@Service(Service.Level.PROJECT)
class ContinuePluginService : Disposable, DumbAware {
    private val logger = LoggerManager.getLogger(javaClass)
    // 常驻窗口缓存（仅Chat窗口）
    private val residentWindows = mutableMapOf<String, ContinuePluginToolWindowFactory.ContinuePluginWindow>()
    // 非常驻窗口缓存（动态创建和销毁）
    private val nonResidentWindows = mutableMapOf<String, ContinuePluginToolWindowFactory.ContinuePluginWindow>()
    private var activePluginWindow: ContinuePluginToolWindowFactory.ContinuePluginWindow? = null
    val coroutineScope = CoroutineScope(Dispatchers.Default)
    var listeners = mutableListOf<(() -> Unit)?>()
    var ideProtocolClient: IdeProtocolClient? by Delegates.observable(null) { _, _, _ ->
        synchronized(this) {
            listeners.forEach {
                it?.invoke()
            }
            listeners.clear()
        }
    }
    lateinit var toolWindow: ToolWindow
    var coreMessengerManager = AtomicProperty<CoreMessengerManager?>(null)
    val coreMessenger: CoreMessenger?
        get() = coreMessengerManager.get()?.coreMessenger?.get()
    var workspacePaths: Array<String>? = null
    var windowId: String = uuid()
    var diffManager: DiffManager? = null
    val activeContent: JComponent?
        get() = activePluginWindow?.content

    val activeBrowser: JBCefBrowser?
        get() = activePluginWindow?.browser?.browser

    /**
     * 更新所有窗口的URL（当环境设置变更时）
     */
    fun updateAllWindowsUrl() {
        coroutineScope.launch(Dispatchers.EDT) {
            // 更新常驻窗口
            residentWindows.values.forEach { window ->
                window.checkAndUpdateUrl()
            }

            // 更新非常驻窗口
            nonResidentWindows.values.forEach { window ->
                window.checkAndUpdateUrl()
            }
        }
    }

    override fun dispose() {
        coroutineScope.cancel()

        // 清理 CoreMessengerManager
        coreMessengerManager.get()?.dispose()
        coreMessengerManager.set(null)

        // 清理其他资源
        diffManager = null
        ideProtocolClient = null
        listeners.clear()

        // 清理窗口缓存
        residentWindows.clear()
        nonResidentWindows.clear()
        activePluginWindow = null
    }

    fun sendToWebview(
        messageType: String,
        data: Any?,
        messageId: String = uuid()
    ) {
        activePluginWindow?.browser?.sendToWebview(messageType, data, messageId)
    }

    /**
     * Add a listener for protocolClient initialization.
     * Currently, only one needs to be processed. If there are more than one,
     * we can use an array to add listeners to ensure that the message is processed.
     */
    fun onProtocolClientInitialized(listener: () -> Unit) {
        if (ideProtocolClient == null) {
            synchronized(this) {
                if (ideProtocolClient == null) {
                    this.listeners.add(listener)
                } else {
                    listener()
                }
            }
        } else {
            listener()
        }
    }

    /**
     * 添加常驻窗口（仅Chat窗口）
     */
    fun addResidentWindow(window: ContinuePluginToolWindowFactory.ContinuePluginWindow, name: String) {
        residentWindows[name] = window
        if (activePluginWindow == null) {
            activePluginWindow = window
        }
    }

    /**
     * 获取窗口（优先从常驻窗口查找，再从非常驻窗口查找）
     */
    private fun getWindow(name: String): ContinuePluginToolWindowFactory.ContinuePluginWindow? {
        return residentWindows[name] ?: nonResidentWindows[name]
    }

    fun onPluginWindowChanged(name: String) {
        activePluginWindow = getWindow(name)
        activePluginWindow?.content?.requestFocus()
        logger.info("Panel changed: $name --- ${activePluginWindow?.contentWrapper?.displayName}")
    }

    fun changeTitle(title: String) {
        coroutineScope.launch(Dispatchers.EDT) {
            activePluginWindow?.changeTitle(title)
        }
    }

    fun changeWindow(index: Int, url: String?, reload: Boolean = false) {
        coroutineScope.launch(Dispatchers.EDT) {
            // 只从常驻窗口中获取（主要是Chat窗口）
            val window = residentWindows.values.toList().getOrNull(index) ?: return@launch
            val browser = window.browser.browser

            val contentWrapper = window.contentWrapper
            if (contentWrapper != null && contentWrapper != toolWindow.contentManager.selectedContent) {
                toolWindow.contentManager.setSelectedContent(contentWrapper)
            }

            val newUrl = if (!url.isNullOrEmpty()) url else if (reload) window.browser.url else null
            newUrl?.let {
                browser.executeJavaScript("window.location.href= '$newUrl';")
            }
        }
    }

    /**
     * 打开非常驻窗口
     */
    fun openNonResidentWindow(windowName: String) {
        coroutineScope.launch(Dispatchers.EDT) {
            val contentManager = toolWindow.contentManager

            // 检查窗口是否已经添加到toolWindow
            val existingContent = contentManager.contents.find { it.displayName == windowName }
            if (existingContent != null) {
                // 如果已存在，直接选中
                contentManager.setSelectedContent(existingContent)
                return@launch
            }

            // 动态创建非常驻窗口
            val window = createNonResidentWindow(windowName) ?: return@launch

            // 添加到非常驻窗口缓存
            nonResidentWindows[windowName] = window

            // 创建content并添加到toolWindow
            val content = window.preparePanel(toolWindow, windowName)
            contentManager.setSelectedContent(content)
        }
    }

    /**
     * 关闭非常驻窗口
     */
    fun closeNonResidentWindow(windowName: String) {
        coroutineScope.launch(Dispatchers.EDT) {
            val contentManager = toolWindow.contentManager
            val content = contentManager.contents.find { it.displayName == windowName }
            content?.let {
                contentManager.removeContent(it, true)
            }

            // 从非常驻窗口缓存中移除
            val window = nonResidentWindows.remove(windowName)
            window?.contentWrapper = null

            logger.info("Non-resident window '$windowName' closed and removed from cache")
        }
    }

    /**
     * 创建非常驻窗口
     */
    private fun createNonResidentWindow(windowName: String): ContinuePluginToolWindowFactory.ContinuePluginWindow? {
        val project = toolWindow.project
        val isDebugMode = System.getenv("USE_TCP")?.toBoolean() ?: false

        return when (windowName) {
            "History" -> ContinuePluginToolWindowFactory.ContinuePluginWindow(project, "HISTORY_URL", true)
            "API" -> if (isDebugMode) {
                ContinuePluginToolWindowFactory.ContinuePluginWindow(project, "API_URL", true)
            } else {
                logger.warn("API window is only available in debug mode")
                null
            }
            "Continue" -> if (isDebugMode) {
                ContinuePluginToolWindowFactory.ContinuePluginWindow(project, "GUI_URL", true)
            } else {
                logger.warn("Continue window is only available in debug mode")
                null
            }
            else -> {
                logger.warn("Unknown non-resident window name: $windowName")
                null
            }
        }
    }
}