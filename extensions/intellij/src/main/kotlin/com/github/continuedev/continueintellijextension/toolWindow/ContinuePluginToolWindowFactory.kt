package com.github.continuedev.continueintellijextension.toolWindow

import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.DefaultActionGroup
import com.intellij.openapi.components.service
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindow
import com.intellij.openapi.wm.ToolWindowFactory
import com.intellij.openapi.wm.impl.content.ToolWindowContentUi
import com.intellij.ui.content.Content
import com.intellij.ui.content.ContentManagerEvent
import com.intellij.ui.content.ContentManagerListener
import com.intellij.ui.jcef.executeJavaScript
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.util.Reflect
import com.taobao.mc.aimi.util.URLManager
import javax.swing.JComponent

const val JS_QUERY_POOL_SIZE = "200"

class ContinuePluginToolWindowFactory : ToolWindowFactory, DumbAware {
    private val logger = LoggerManager.getLogger(ContinuePluginToolWindowFactory::class.java)
    private val isDebugMode = System.getenv("USE_TCP")?.toBoolean() ?: false

    override fun createToolWindowContent(project: Project, toolWindow: ToolWindow) {
        val continuePluginService = project.service<ContinuePluginService>()
        continuePluginService.toolWindow = toolWindow

        // 只创建常驻的Chat窗口
        val chatWindow = ContinuePluginWindow(project, "AIMI_URL", false)
        chatWindow.preparePanel(toolWindow, "Chat")
        continuePluginService.addResidentWindow(chatWindow, "Chat")

        // 非常驻窗口不再预创建，将在需要时动态创建
        logger.info("Initialized tool window with resident Chat window only. Non-resident windows will be created on demand.")

        toolWindow.addContentManagerListener(object : ContentManagerListener {
            override fun selectionChanged(event: ContentManagerEvent) {
                val displayName = event.content.displayName ?: return
                continuePluginService.onPluginWindowChanged(displayName)
            }

            override fun contentRemoved(event: ContentManagerEvent) {
                val displayName = event.content.displayName ?: return
                continuePluginService.closeNonResidentWindow(displayName)
            }
        })

        val titleActions = mutableListOf<AnAction>()
        createTitleActions(titleActions)

        // Add MaximizeToolWindow action
        val action = ActionManager.getInstance().getAction("MaximizeToolWindow")
        if (action != null) {
            titleActions.add(action)
        }

        toolWindow.setTitleActions(titleActions)

        // 设置additionalGearActions，包含开发者工具
        val devToolsAction = ActionManager.getInstance().getAction("aimi.openDevTools")
        if (devToolsAction != null) {
            val gearActionGroup = DefaultActionGroup()
            gearActionGroup.add(devToolsAction)
            toolWindow.setAdditionalGearActions(gearActionGroup)
        }

        Reflect.on(toolWindow).set("canCloseContent", true)
        toolWindow.component.putClientProperty(ToolWindowContentUi.HIDE_ID_LABEL, "true")
        toolWindow.component.putClientProperty(ToolWindowContentUi.DONT_HIDE_TOOLBAR_IN_HEADER, true)
    }

    private fun createTitleActions(titleActions: MutableList<in AnAction>) {
        val action = if (!isDebugMode) {
            ActionManager.getInstance().getAction("AIMISidebarActionsGroup")
        } else {
            ActionManager.getInstance().getAction("AIMISidebarDebugActionsGroup")
        }
        if (action != null) {
            titleActions.add(action)
        }
    }

    override fun shouldBeAvailable(project: Project) = true

    class ContinuePluginWindow(private val project: Project, private val urlKey: String, val closeable: Boolean = true) {
        private val logger = LoggerManager.getLogger(ContinuePluginWindow::class.java)
        private var lastEnvironment: String = URLManager.getCurrentEnvironment()

        init {
            System.setProperty("ide.browser.jcef.jsQueryPoolSize", JS_QUERY_POOL_SIZE)
            System.setProperty("ide.browser.jcef.contextMenu.devTools.enabled", "true")
        }

        val browser: ContinueBrowser by lazy {
            val url = URLManager.getUrl(urlKey)
            logger.info("$urlKey URL: $url")
            ContinueBrowser(project, url)
        }

        val content: JComponent
            get() = browser.browser.component

        var contentWrapper: Content? = null

        fun changeTitle(title: String) {
            contentWrapper?.displayName = title
        }

        /**
         * 检查并更新URL（当环境设置变更时）
         */
        suspend fun checkAndUpdateUrl() {
            if (URLManager.hasEnvironmentChanged(lastEnvironment)) {
                val newUrl = URLManager.getUrl(urlKey)
                logger.info("Environment changed:${lastEnvironment} to ${URLManager.getCurrentEnvironment()}, updating $urlKey URL to: $newUrl")

                // 如果浏览器已经初始化，则刷新到新URL
                browser.url = newUrl
                reload(newUrl)

                // 更新最后的环境状态
                lastEnvironment = URLManager.getCurrentEnvironment()
            }
        }

        suspend fun reload(url: String? = null) {
            runCatching {
                val newUrl = if (!url.isNullOrEmpty()) url else browser.url
                browser.browser.executeJavaScript("window.location.href= '$newUrl';")
            }.onFailure {
                logger.warn("reload browser: $urlKey failed", it)
            }
        }

        fun preparePanel(toolWindow: ToolWindow, displayName: String): Content {
            val content = addContent(toolWindow, content, displayName)
            contentWrapper = content
            return content
        }

        private fun addContent(toolWindow: ToolWindow, panel: JComponent?, displayName: String?): Content {
            val contentManager = toolWindow.contentManager
            val content = contentManager.factory.createContent(panel, displayName, false)
            content.isCloseable = closeable
            contentManager.addContent(content)
            return content
        }
    }
}