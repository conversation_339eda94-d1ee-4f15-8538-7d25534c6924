package com.taobao.mc.aimi.find.providers

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.GlobalSearchScope
import com.taobao.mc.aimi.find.*
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.min

/**
 * 文件名搜索提供者
 * 使用 IntelliJ 的 FilenameIndex 进行快速文件名搜索
 */
class FileNameSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(FileNameSearchProvider::class.java)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.FILE_NAME
    }

    override fun getSupportedFeatures(): Set<SearchFeature> {
        return setOf(
            SearchFeature.CASE_SENSITIVE,
            SearchFeature.FUZZY_MATCH,
            SearchFeature.ASYNC,
            SearchFeature.CANCELLABLE,
            SearchFeature.PROGRESS_TRACKING,
            SearchFeature.RESULT_RANKING
        )
    }

    override suspend fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        return withContext(Dispatchers.IO) {
            try {
                callback?.onProgress(SearchProgress(0, 100, "开始文件名搜索..."))

                val scope = createSearchScope(config)
                
                // 1. 精确匹配
                callback?.onProgress(SearchProgress(10, 100, "执行精确匹配..."))
                val exactMatches = findExactMatches(config.query, scope, config)
                results.addAll(exactMatches)

                // 2. 前缀匹配
                callback?.onProgress(SearchProgress(30, 100, "执行前缀匹配..."))
                val prefixMatches = findPrefixMatches(config.query, scope, config)
                results.addAll(prefixMatches)

                // 3. 包含匹配
                callback?.onProgress(SearchProgress(50, 100, "执行包含匹配..."))
                val containsMatches = findContainsMatches(config.query, scope, config)
                results.addAll(containsMatches)

                // 4. 模糊匹配
                callback?.onProgress(SearchProgress(70, 100, "执行模糊匹配..."))
                val fuzzyMatches = findFuzzyMatches(config.query, scope, config)
                results.addAll(fuzzyMatches)

                // 5. 正则匹配（如果启用）
                if (config.useRegex) {
                    callback?.onProgress(SearchProgress(85, 100, "执行正则匹配..."))
                    val regexMatches = findRegexMatches(config.query, scope, config)
                    results.addAll(regexMatches)
                }

                val searchTime = System.currentTimeMillis() - startTime
                val uniqueResults = results
                    .distinctBy { it.file?.path }
                    .sortedByDescending { it.relevanceScore }
                    .take(config.maxResults)

                val searchResults = SearchResults(
                    query = config.query,
                    searchType = config.searchType,
                    results = uniqueResults,
                    totalCount = uniqueResults.size,
                    searchTime = searchTime,
                    hasMore = results.size > config.maxResults
                )

                callback?.onProgress(SearchProgress(100, 100, "文件名搜索完成，找到 ${uniqueResults.size} 个结果"))
                callback?.onCompleted(searchResults)

                searchResults

            } catch (e: Exception) {
                logger.error("文件名搜索失败: ${e.message}", e)
                callback?.onError(e)
                throw e
            }
        }
    }

    /**
     * 精确匹配
     */
    private fun findExactMatches(query: String, scope: GlobalSearchScope, config: SearchConfig): List<FileNameSearchResult> {
        return ReadAction.compute<List<FileNameSearchResult>, RuntimeException> {
            val results = mutableListOf<FileNameSearchResult>()
            
            try {
                val files = FilenameIndex.getFilesByName(project, query, scope)
                for (file in files) {
                    if (shouldIncludeFile(file.virtualFile, config)) {
                        results.add(createFileResult(file.virtualFile, query, FileMatchType.EXACT, 2.0))
                    }
                }
            } catch (e: Exception) {
                logger.warn("精确匹配时发生错误: ${e.message}")
            }
            
            results
        }
    }

    /**
     * 前缀匹配
     */
    private fun findPrefixMatches(query: String, scope: GlobalSearchScope, config: SearchConfig): List<FileNameSearchResult> {
        return ReadAction.compute<List<FileNameSearchResult>, RuntimeException> {
            val results = mutableListOf<FileNameSearchResult>()
            
            try {
                val allFileNames = FilenameIndex.getAllFilenames(project)
                for (fileName in allFileNames) {
                    if (fileName.startsWith(query, ignoreCase = !config.caseSensitive) && fileName != query) {
                        val files = FilenameIndex.getFilesByName(project, fileName, scope)
                        for (file in files.take(10)) { // 限制每个文件名的结果数量
                            if (shouldIncludeFile(file.virtualFile, config)) {
                                val score = calculatePrefixScore(fileName, query)
                                results.add(createFileResult(file.virtualFile, query, FileMatchType.PREFIX, score))
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("前缀匹配时发生错误: ${e.message}")
            }
            
            results.take(50) // 限制前缀匹配结果数量
        }
    }

    /**
     * 包含匹配
     */
    private fun findContainsMatches(query: String, scope: GlobalSearchScope, config: SearchConfig): List<FileNameSearchResult> {
        return ReadAction.compute<List<FileNameSearchResult>, RuntimeException> {
            val results = mutableListOf<FileNameSearchResult>()
            
            try {
                val allFileNames = FilenameIndex.getAllFilenames(project)
                for (fileName in allFileNames) {
                    if (fileName.contains(query, ignoreCase = !config.caseSensitive) && 
                        !fileName.startsWith(query, ignoreCase = !config.caseSensitive) &&
                        fileName != query) {
                        
                        val files = FilenameIndex.getFilesByName(project, fileName, scope)
                        for (file in files.take(5)) { // 限制每个文件名的结果数量
                            if (shouldIncludeFile(file.virtualFile, config)) {
                                val score = calculateContainsScore(fileName, query)
                                results.add(createFileResult(file.virtualFile, query, FileMatchType.CONTAINS, score))
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("包含匹配时发生错误: ${e.message}")
            }
            
            results.take(100) // 限制包含匹配结果数量
        }
    }

    /**
     * 模糊匹配
     */
    private fun findFuzzyMatches(query: String, scope: GlobalSearchScope, config: SearchConfig): List<FileNameSearchResult> {
        return ReadAction.compute<List<FileNameSearchResult>, RuntimeException> {
            val results = mutableListOf<FileNameSearchResult>()
            
            try {
                val allFileNames = FilenameIndex.getAllFilenames(project)
                for (fileName in allFileNames.take(1000)) { // 限制处理的文件名数量
                    val fuzzyScore = calculateFuzzyScore(fileName, query, config.caseSensitive)
                    if (fuzzyScore > 0.3) { // 模糊匹配阈值
                        val files = FilenameIndex.getFilesByName(project, fileName, scope)
                        for (file in files.take(3)) { // 限制每个文件名的结果数量
                            if (shouldIncludeFile(file.virtualFile, config)) {
                                results.add(createFileResult(file.virtualFile, query, FileMatchType.FUZZY, fuzzyScore))
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("模糊匹配时发生错误: ${e.message}")
            }
            
            results.sortedByDescending { it.relevanceScore }.take(50) // 限制模糊匹配结果数量
        }
    }

    /**
     * 正则匹配
     */
    private fun findRegexMatches(query: String, scope: GlobalSearchScope, config: SearchConfig): List<FileNameSearchResult> {
        return ReadAction.compute<List<FileNameSearchResult>, RuntimeException> {
            val results = mutableListOf<FileNameSearchResult>()
            
            try {
                val regex = if (config.caseSensitive) {
                    Regex(query)
                } else {
                    Regex(query, RegexOption.IGNORE_CASE)
                }
                
                val allFileNames = FilenameIndex.getAllFilenames(project)
                for (fileName in allFileNames.take(1000)) { // 限制处理的文件名数量
                    if (regex.containsMatchIn(fileName)) {
                        val files = FilenameIndex.getFilesByName(project, fileName, scope)
                        for (file in files.take(5)) { // 限制每个文件名的结果数量
                            if (shouldIncludeFile(file.virtualFile, config)) {
                                results.add(createFileResult(file.virtualFile, query, FileMatchType.REGEX, 1.5))
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("正则匹配时发生错误: ${e.message}")
            }
            
            results.take(30) // 限制正则匹配结果数量
        }
    }

    /**
     * 创建文件搜索结果
     */
    private fun createFileResult(file: VirtualFile, query: String, matchType: FileMatchType, score: Double): FileNameSearchResult {
        return FileNameSearchResult(
            file = file,
            text = file.name,
            context = "文件路径: ${file.path}",
            relevanceScore = score,
            fileName = file.name,
            filePath = file.path,
            matchType = matchType
        )
    }

    /**
     * 检查是否应该包含文件
     */
    private fun shouldIncludeFile(file: VirtualFile, config: SearchConfig): Boolean {
        // 文件类型过滤
        if (config.fileTypes.isNotEmpty() && !config.fileTypes.contains(file.extension ?: "")) {
            return false
        }
        
        // 排除模式过滤
        return config.excludePatterns.none { pattern ->
            file.path.contains(pattern, ignoreCase = true)
        }
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.scope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
            SearchScope.FILE -> GlobalSearchScope.projectScope(project)
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
            SearchScope.OPEN_FILES -> GlobalSearchScope.projectScope(project)
            SearchScope.CUSTOM -> GlobalSearchScope.projectScope(project)
        }
    }

    /**
     * 计算前缀匹配分数
     */
    private fun calculatePrefixScore(fileName: String, query: String): Double {
        val ratio = query.length.toDouble() / fileName.length
        return 1.5 + ratio * 0.5
    }

    /**
     * 计算包含匹配分数
     */
    private fun calculateContainsScore(fileName: String, query: String): Double {
        val position = fileName.indexOf(query, ignoreCase = true)
        val ratio = query.length.toDouble() / fileName.length
        val positionScore = 1.0 - (position.toDouble() / fileName.length)
        return 1.0 + ratio * 0.3 + positionScore * 0.2
    }

    /**
     * 计算模糊匹配分数
     */
    private fun calculateFuzzyScore(fileName: String, query: String, caseSensitive: Boolean): Double {
        val name = if (caseSensitive) fileName else fileName.lowercase()
        val q = if (caseSensitive) query else query.lowercase()
        
        if (name == q) return 2.0
        if (name.startsWith(q)) return 1.8
        if (name.contains(q)) return 1.5
        
        // 简单的字符匹配算法
        var score = 0.0
        var queryIndex = 0
        
        for (char in name) {
            if (queryIndex < q.length && char == q[queryIndex]) {
                score += 1.0 / q.length
                queryIndex++
            }
        }
        
        return min(score, 1.0)
    }
}
