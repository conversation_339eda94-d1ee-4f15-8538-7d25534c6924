package com.taobao.mc.aimi.find.providers

import com.intellij.find.FindManager
import com.intellij.find.FindModel
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.editor.Document
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.GlobalSearchScope
import com.taobao.mc.aimi.find.*
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.min

/**
 * 文本搜索提供者
 * 使用 IntelliJ 的 FindManager 进行高效的文本搜索
 */
class TextSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(TextSearchProvider::class.java)
    private val findManager = FindManager.getInstance(project)
    private val fileDocumentManager = FileDocumentManager.getInstance()
    private val psiDocumentManager = PsiDocumentManager.getInstance(project)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.TEXT || searchType == SearchType.FULL_TEXT
    }

    override fun getSupportedFeatures(): Set<SearchFeature> {
        return setOf(
            SearchFeature.CASE_SENSITIVE,
            SearchFeature.WHOLE_WORDS,
            SearchFeature.REGEX,
            SearchFeature.CONTEXT_AWARE,
            SearchFeature.ASYNC,
            SearchFeature.CANCELLABLE,
            SearchFeature.PROGRESS_TRACKING,
            SearchFeature.RESULT_RANKING
        )
    }

    override suspend fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        return withContext(Dispatchers.IO) {
            try {
                callback?.onProgress(SearchProgress(0, 100, "开始文本搜索..."))

                val findModel = createFindModel(config)
                val searchScope = createSearchScope(config)
                val files = getFilesToSearch(searchScope, config)

                callback?.onProgress(SearchProgress(10, 100, "找到 ${files.size} 个文件需要搜索"))

                var processedFiles = 0
                val totalFiles = files.size

                for (file in files) {
                    if (results.size >= config.maxResults) {
                        break
                    }

                    try {
                        val fileResults = searchInFile(file, findModel, config)
                        results.addAll(fileResults)
                        
                        processedFiles++
                        val progress = 10 + (processedFiles * 80 / totalFiles)
                        callback?.onProgress(SearchProgress(
                            processedFiles, 
                            totalFiles, 
                            "搜索文件: ${file.name} (${processedFiles}/${totalFiles})",
                            progress
                        ))
                        
                    } catch (e: Exception) {
                        logger.warn("搜索文件 ${file.path} 时发生错误: ${e.message}")
                    }
                }

                val searchTime = System.currentTimeMillis() - startTime
                val searchResults = SearchResults(
                    query = config.query,
                    searchType = config.searchType,
                    results = results.take(config.maxResults).sortedByDescending { it.relevanceScore },
                    totalCount = results.size,
                    searchTime = searchTime,
                    hasMore = results.size > config.maxResults
                )

                callback?.onProgress(SearchProgress(100, 100, "文本搜索完成，找到 ${results.size} 个结果"))
                callback?.onCompleted(searchResults)

                searchResults

            } catch (e: Exception) {
                logger.error("文本搜索失败: ${e.message}", e)
                callback?.onError(e)
                throw e
            }
        }
    }

    /**
     * 创建查找模型
     */
    private fun createFindModel(config: SearchConfig): FindModel {
        val findModel = FindModel()
        findModel.stringToFind = config.query
        findModel.isCaseSensitive = config.caseSensitive
        findModel.isWholeWordsOnly = config.wholeWords
        findModel.isRegularExpressions = config.useRegex
        findModel.isPreserveCase = false
        return findModel
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.scope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project) // 简化实现
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project) // 简化实现
            SearchScope.FILE -> GlobalSearchScope.projectScope(project) // 简化实现
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project) // 简化实现
            SearchScope.OPEN_FILES -> GlobalSearchScope.projectScope(project) // 简化实现
            SearchScope.CUSTOM -> GlobalSearchScope.projectScope(project) // 简化实现
        }
    }

    /**
     * 获取需要搜索的文件列表
     */
    private fun getFilesToSearch(scope: GlobalSearchScope, config: SearchConfig): List<VirtualFile> {
        return ReadAction.compute<List<VirtualFile>, RuntimeException> {
            val allFiles = mutableListOf<VirtualFile>()
            
            try {
                val allFileNames = FilenameIndex.getAllFilenames(project)
                
                for (fileName in allFileNames.take(1000)) { // 限制文件名数量
                    val files = FilenameIndex.getFilesByName(project, fileName, scope)
                    for (file in files) {
                        if (file.isValid && !file.virtualFile.isDirectory) {
                            val virtualFile = file.virtualFile
                            
                            // 文件类型过滤
                            if (config.fileTypes.isEmpty() || config.fileTypes.contains(virtualFile.extension ?: "")) {
                                // 排除模式过滤
                                val shouldExclude = config.excludePatterns.any { pattern ->
                                    virtualFile.path.contains(pattern, ignoreCase = true)
                                }
                                
                                if (!shouldExclude) {
                                    allFiles.add(virtualFile)
                                }
                            }
                        }
                    }
                }
                
            } catch (e: Exception) {
                logger.warn("获取文件列表时发生错误: ${e.message}")
            }
            
            allFiles.take(500) // 限制总文件数量
        }
    }

    /**
     * 在单个文件中搜索
     */
    private fun searchInFile(file: VirtualFile, findModel: FindModel, config: SearchConfig): List<TextSearchResult> {
        return ReadAction.compute<List<TextSearchResult>, RuntimeException> {
            val results = mutableListOf<TextSearchResult>()
            
            try {
                val document = fileDocumentManager.getDocument(file) ?: return@compute results
                val text = document.text
                
                if (text.isEmpty()) {
                    return@compute results
                }

                var offset = 0
                var matchCount = 0
                
                while (offset < text.length && matchCount < 50) { // 限制单文件结果数量
                    val findResult = findManager.findString(text, offset, findModel)
                    
                    if (!findResult.isStringFound) {
                        break
                    }

                    val startOffset = findResult.startOffset
                    val endOffset = findResult.endOffset
                    val matchedText = text.substring(startOffset, endOffset)
                    
                    // 获取行号和列号
                    val lineNumber = document.getLineNumber(startOffset)
                    val columnNumber = startOffset - document.getLineStartOffset(lineNumber)
                    
                    // 获取上下文和完整行文本
                    val (context, lineText) = getContext(document, lineNumber, startOffset, endOffset)
                    
                    // 计算相关性分数
                    val relevanceScore = calculateRelevanceScore(matchedText, config.query, lineText)
                    
                    val result = TextSearchResult(
                        file = file,
                        line = lineNumber + 1, // 1-based line number
                        column = columnNumber + 1, // 1-based column number
                        text = matchedText,
                        context = context,
                        relevanceScore = relevanceScore,
                        startOffset = startOffset,
                        endOffset = endOffset,
                        matchedText = matchedText,
                        lineText = lineText
                    )
                    
                    results.add(result)
                    offset = endOffset
                    matchCount++
                }
                
            } catch (e: Exception) {
                logger.warn("在文件 ${file.path} 中搜索时发生错误: ${e.message}")
            }
            
            results
        }
    }

    /**
     * 获取搜索结果的上下文
     */
    private fun getContext(document: Document, lineNumber: Int, startOffset: Int, endOffset: Int): Pair<String, String> {
        return try {
            val lineStartOffset = document.getLineStartOffset(lineNumber)
            val lineEndOffset = document.getLineEndOffset(lineNumber)
            val lineText = document.getText(TextRange(lineStartOffset, lineEndOffset))
            
            // 获取前后几行作为上下文
            val contextLines = mutableListOf<String>()
            val contextRange = 2 // 前后2行
            
            for (i in (lineNumber - contextRange)..(lineNumber + contextRange)) {
                if (i >= 0 && i < document.lineCount) {
                    val contextLineStart = document.getLineStartOffset(i)
                    val contextLineEnd = document.getLineEndOffset(i)
                    val contextLine = document.getText(TextRange(contextLineStart, contextLineEnd))
                    
                    if (i == lineNumber) {
                        // 高亮当前行的匹配部分
                        val relativeStart = startOffset - lineStartOffset
                        val relativeEnd = endOffset - lineStartOffset
                        
                        if (relativeStart >= 0 && relativeEnd <= contextLine.length) {
                            val before = contextLine.substring(0, relativeStart)
                            val match = contextLine.substring(relativeStart, relativeEnd)
                            val after = contextLine.substring(relativeEnd)
                            contextLines.add("$before**$match**$after")
                        } else {
                            contextLines.add(contextLine)
                        }
                    } else {
                        contextLines.add(contextLine)
                    }
                }
            }
            
            Pair(contextLines.joinToString("\n"), lineText)
        } catch (e: Exception) {
            Pair("无法获取上下文", "")
        }
    }

    /**
     * 计算相关性分数
     */
    private fun calculateRelevanceScore(matchedText: String, query: String, lineText: String): Double {
        var score = 1.0
        
        // 精确匹配加分
        if (matchedText.equals(query, ignoreCase = true)) {
            score += 0.5
        }
        
        // 全词匹配加分
        if (lineText.contains("\\b${Regex.escape(matchedText)}\\b".toRegex())) {
            score += 0.3
        }
        
        // 行长度影响（短行更相关）
        score += (1.0 / (lineText.length / 100.0 + 1.0)) * 0.2
        
        return min(score, 2.0)
    }
}
