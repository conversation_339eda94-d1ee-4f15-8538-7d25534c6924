package com.taobao.mc.aimi.settings

// 添加导入
import com.github.continuedev.continueintellijextension.activities.ContinuePluginDisposable
import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.github.continuedev.continueintellijextension.utils.uuid
import com.intellij.openapi.components.service
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.ComboBox
import com.intellij.ui.TitledSeparator
import com.intellij.ui.table.JBTable
import com.intellij.util.application
import com.intellij.util.messages.MessageBusConnection
import com.intellij.util.ui.JBUI
import com.taobao.mc.aimi.logger.LoggerManager
import java.awt.*
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import java.net.URI
import javax.swing.*
import javax.swing.table.DefaultTableModel

// 环境选择选项枚举
enum class EnvironmentOption(val displayName: String, val value: String) {
    PRODUCTION("生产", "release"),
    PRE_RELEASE("预发", "debug");

    companion object {
        val options = entries.toTypedArray()

        fun fromValue(value: String): EnvironmentOption {
            return entries.find { it.value == value } ?: PRODUCTION
        }
    }

    override fun toString(): String = displayName
}

class AIMISettingsComponent(private val project: Project? = null) : DumbAware, AIMISettingsListener {
    private val logger = LoggerManager.getLogger(AIMISettingsComponent::class.java)
    private val continueService: ContinuePluginService? = project?.service()

    // 使用项目级别的消息总线连接，如果没有 project 则使用应用级别
    private var messageBusConnection: MessageBusConnection? = null

    // UI 容器
    val panel: JPanel = JPanel(GridBagLayout())
    private val constraints = GridBagConstraints().apply {
        fill = GridBagConstraints.HORIZONTAL
        weightx = 1.0
        weighty = 0.0
        gridx = 0
        gridy = 0
        insets = JBUI.insets(5)
    }

    // 补全设置
    val enableTabAutocomplete: JCheckBox = JCheckBox("开启自动补全")
    val enableCrossFileCompletion: JCheckBox = JCheckBox("开启跨文件代码补全")

    // 补全形式选择
    val completionTypeComboBox: JComboBox<MultilineOptions> = ComboBox(MultilineOptions.options)

    // 补全防抖时延设置
    val debounceDelaySpinner: JSpinner = JSpinner(SpinnerNumberModel(500, 100, 5000, 50))

    // 索引状态管理
    val indexProgressBar: JProgressBar = JProgressBar(0, 100)
    private val indexLabel = JLabel("")
    private val updateIndexButton: JButton = JButton("更新索引")
    private val rebuildIndexButton: JButton = JButton("重建索引")

    // 环境选择
    val environmentComboBox: JComboBox<EnvironmentOption> = ComboBox(EnvironmentOption.options)

    init {
        // 添加通用设置栏目
        createGeneralComponent()

        createCompletionComponent()

        createIndexComponent()

        createOtherComponent()

        // Add a "filler" component that takes up all remaining vertical space
        constraints.weighty = 1.0
        val filler = JPanel()
        panel.add(filler, constraints)

        addAIMISettingsListener()
    }

    private fun createGeneralComponent() {
        addToPanel(TitledSeparator("通用"))

        // 创建快捷键表格
        val columnNames = arrayOf("命令", "快捷键", "说明")
        val data = arrayOf(
            arrayOf("打开/关闭AIMI", "⌥A", "在IDE激活时开启/关闭AIMI窗口"),
            // 可以在此添加更多快捷键行
            arrayOf("触发代码补全", "⌘\\", "在光标所在位置触发代码补全"),
            arrayOf("接受代码补全", "⇥", "在光标所在位置接受代码补全")
        )

        // val model = ListWrappingTableModel(data.map { it.toList() }.toList(), *columnNames)
        val tableModel = object : DefaultTableModel(data, columnNames) {
            override fun isCellEditable(row: Int, column: Int): Boolean {
                return false
            }
        }
        val table = JBTable(tableModel)
        table.rowHeight = 25
        table.setShowGrid(true)
        // 设置表头背景色和字体
        val header = table.tableHeader
        header.resizingAllowed = false
        header.font = Font(table.font.name, Font.BOLD, table.font.size)
        header.background = UIManager.getColor("Table.background").darker()
        header.foreground = UIManager.getColor("Table.foreground")

        // 设置表格自动调整大小
        table.autoResizeMode = JTable.AUTO_RESIZE_ALL_COLUMNS

        // 设置表格不可编辑
        table.setDefaultEditor(Any::class.java, null)

        // 设置表格不可点击/选择
        table.selectionModel.selectionMode = ListSelectionModel.SINGLE_SELECTION
        table.columnSelectionAllowed = false
        table.rowSelectionAllowed = false
        table.cellSelectionEnabled = false
        table.cellEditor = null
        table.isFocusable = false

        // 直接添加表格，不使用滚动面板
        table.preferredScrollableViewportSize = table.preferredSize

        // 创建面板容纳表格，使用BorderLayout让表格填充整个宽度
        val tablePanel = JPanel(BorderLayout())
        tablePanel.add(table.tableHeader, BorderLayout.NORTH)
        tablePanel.add(table, BorderLayout.CENTER)

        // 为整个表格面板添加边框
        tablePanel.border = BorderFactory.createEmptyBorder(5, 0, 5, 0)
        // 使用线框边框替代空边框
        tablePanel.border = BorderFactory.createLineBorder(UIManager.getColor("Table.gridColor"))

        addToPanel(tablePanel)
    }

    private fun createCompletionComponent() {
        addToPanel(TitledSeparator("代码补全"))
        addToPanel(enableTabAutocomplete)
        val settings = AIMISettingService.instance.state
        enableTabAutocomplete.isSelected = settings.enableTabAutocomplete

        // 跨行补全设置with说明 - 同一行显示
        val flowLayout = FlowLayout(FlowLayout.LEFT, 0, 0)
        flowLayout.alignOnBaseline = true
        val crossFilePanel = JPanel(flowLayout)
        crossFilePanel.add(enableCrossFileCompletion)
        enableCrossFileCompletion.isSelected = settings.enableCrossFileComplete

        // 添加说明文字
        val hintLabel = JLabel("该选项依赖下方的 \"实时仓库索引\" 设置")
        hintLabel.font = Font(hintLabel.font.name, Font.PLAIN, hintLabel.font.size - 1)
        hintLabel.foreground = UIManager.getColor("Label.disabledForeground")
        crossFilePanel.add(hintLabel)

        addToPanel(crossFilePanel)

        // 补全形式选择
        val multilineCompletionPanel = JPanel(FlowLayout(FlowLayout.LEFT, 5, 0))
        val completionTypeLabel = JLabel("补全形式:")
        multilineCompletionPanel.add(completionTypeLabel)
        multilineCompletionPanel.add(completionTypeComboBox)
        completionTypeComboBox.selectedItem = MultilineOptions.fromValue(settings.multilineCompletions)
        addToPanel(multilineCompletionPanel)

        // 补全防抖时延设置
        val debouncePanel = JPanel(FlowLayout(FlowLayout.LEFT, 5, 0))
        val debounceLabel = JLabel("补全防抖延时(ms)：")
        debouncePanel.add(debounceLabel)
        debouncePanel.add(Box.createHorizontalStrut(20))
        debounceDelaySpinner.preferredSize = Dimension(80, debounceDelaySpinner.preferredSize.height)
        debouncePanel.add(debounceDelaySpinner)
        addToPanel(debouncePanel)

        // 快捷键展示
        // addToPanel(createCompleteHint())
    }

    private fun createIndexComponent() {
        project?.let {
            // 4. 索引状态管理
            addToPanel(TitledSeparator("实时仓库索引"))
            val settings = AIMISettingService.instance
            val indexStatus = settings.state.indexStatus
            indexLabel.text = indexStatus.desc
            addToPanel(indexLabel)

            // 进度条
            // indexProgressBar.isStringPainted = true
            // indexProgressBar.string = "索引进度: 0%"
            // indexProgressBar.foreground = JBColor.WHITE
            indexProgressBar.value = (indexStatus.progress * 100).toInt()
            addToPanel(indexProgressBar)
            updateIndexProgress(indexStatus)
            // 索引操作按钮面板
            val indexButtons = JPanel(FlowLayout(FlowLayout.LEFT, 5, 0))
            indexButtons.add(updateIndexButton)
            indexButtons.add(rebuildIndexButton)
            addToPanel(indexButtons)

            updateIndexButton.addActionListener { updateIndex() }

            rebuildIndexButton.addActionListener { rebuildIndex() }
        }
    }

    private fun createOtherComponent() {
        addToPanel(TitledSeparator("其他"))

        // 环境选择 - 标题和多选框在同一行
        val environmentPanel = JPanel(FlowLayout(FlowLayout.LEFT, 5, 0))
        val environmentLabel = JLabel("环境选择:")
        environmentPanel.add(environmentLabel)
        environmentPanel.add(environmentComboBox)

        // 设置当前环境值
        val settings = AIMISettingService.instance.state
        environmentComboBox.selectedItem = EnvironmentOption.fromValue(settings.environment)

        addToPanel(environmentPanel)

        // 创建超链接
        val linkLabel = JLabel("<html><u>AIMI插件安装指南</u></html>")
        linkLabel.foreground = JBUI.CurrentTheme.Link.Foreground.ENABLED
        linkLabel.cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)

        // 添加点击事件
        linkLabel.addMouseListener(object : MouseAdapter() {
            override fun mouseClicked(e: MouseEvent?) {
                try {
                    Desktop.getDesktop()
                        .browse(URI("https://alidocs.dingtalk.com/i/nodes/XPwkYGxZV347LdvpH4yZBr92JAgozOKL?cid=369053%3A691691904&utm_source=im&utm_scene=team_space&iframeQuery=utm_medium%3Dim_card%26utm_source%3dim&utm_medium=im_card&corpId=dingd8e1123006514592"))
                } catch (ex: Exception) {
                    logger.warn("Failed to open browser", ex)
                }
            }
        })

        addToPanel(linkLabel)
    }

    private fun addAIMISettingsListener() {
        project?.let {
            val pluginDisposable = ContinuePluginDisposable.getInstance(project)
            messageBusConnection = application.messageBus.connect(pluginDisposable)
            messageBusConnection?.subscribe(AIMISettingsListener.TOPIC, this)
        }
    }

    // 实现 AIMISettingsListener 接口
    override fun settingsUpdated(settings: AIMIState) {
        SwingUtilities.invokeLater {
            logger.info("收到设置更新通知，正在更新UI")

            // 更新复选框状态
            enableTabAutocomplete.isSelected = settings.enableTabAutocomplete
            enableCrossFileCompletion.isSelected = settings.enableCrossFileComplete
            // enableOSR.isSelected = settings.enableOSR

            // 更新补全形式选择
            val matchingOption = MultilineOptions.fromValue(settings.multilineCompletions)
            completionTypeComboBox.selectedItem = matchingOption

            // 更新环境选择
            environmentComboBox.selectedItem = EnvironmentOption.fromValue(settings.environment)

            // 更新索引状态
            updateIndexProgress(settings.indexStatus)
        }
    }

    fun dispatch(messageType: String, data: Map<String, Any>, callback: (Any?) -> Unit = {}) {
        logger.info("dispatch messageType:$messageType, data:$data")
        continueService?.coreMessenger
            ?.request(
                messageType,
                data,
                uuid()
            ) {
                callback(it)
            } ?: run {
            logger.warn("Failed to dispatch message: $messageType, data:$data")
        }
    }

    // 添加释放资源的方法
    fun dispose() {
        logger.info("释放资源")
        messageBusConnection?.disconnect()
        messageBusConnection = null
    }

    private fun updateIndex() {
        // 更新索引操作
        SwingUtilities.invokeLater {
            dispatch("index/forceReIndex", mapOf("shouldClearIndexes" to false))
        }
    }

    private fun rebuildIndex() {
        // 重建索引操作
        SwingUtilities.invokeLater {
            dispatch("index/forceReIndex", mapOf("shouldClearIndexes" to true))
        }
    }

    fun updateIndexProgress(indexStatus: IndexUpdateStatus) {
        SwingUtilities.invokeLater {
            val (progressIndex, status, desc) = indexStatus
            val progress = (progressIndex * 100).toInt()
            logger.info("索引进度: $progress%")
            val info = if (status == IndexState.INDEXING) "$progress% $desc" else desc
            indexLabel.text = info
            indexProgressBar.value = progress
            // indexProgressBar.string = "$status: $progress%"
        }
    }

    private fun addToPanel(component: JComponent) {
        panel.add(component, constraints)
        constraints.gridy++
    }
}