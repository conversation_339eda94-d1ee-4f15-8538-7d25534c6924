package com.taobao.mc.aimi.util

import com.taobao.mc.aimi.settings.AIMISettingService

/**
 * URL管理器，根据环境设置生成对应的URL
 */
object URLManager {

    // 生产环境URL配置
    private val RELEASE_URLS = mapOf(
        "GUI_URL" to "http://localhost:5173/jetbrains_index.html",
        "AIMI_URL" to "https://aimi.alibaba-inc.com/#/ide/dashboard",
        "HISTORY_URL" to "https://aimi.alibaba-inc.com/#/ide/history",
        "API_URL" to "https://aimi.alibaba-inc.com/#/ide/api"
    )

    // 预发环境URL配置
    private val DEBUG_URLS = mapOf(
        "GUI_URL" to "http://localhost:5173/jetbrains_index.html",
        "AIMI_URL" to "https://pre-aimi.alibaba-inc.com/#/ide/dashboard",
        "HISTORY_URL" to "https://pre-aimi.alibaba-inc.com/#/ide/history",
        "API_URL" to "https://pre-aimi.alibaba-inc.com/#/ide/api"
    )

    /**
     * 根据当前环境设置获取URL
     */
    fun getUrl(urlKey: String): String {
        val environment = AIMISettingService.instance.state.environment
        val urls = if (environment == "release") RELEASE_URLS else DEBUG_URLS
        return urls[urlKey] ?: System.getenv(urlKey) ?: getDefaultUrl()
    }

    /**
     * 获取默认URL
     */
    private fun getDefaultUrl(): String {
        val environment = AIMISettingService.instance.state.environment
        return if (environment == "release") {
            "https://aimi.alibaba-inc.com/#/ide/dashboard"
        } else {
            "https://pre-aimi.alibaba-inc.com/#/ide/dashboard"
        }
    }

    /**
     * 获取当前环境设置
     */
    fun getCurrentEnvironment(): String {
        return AIMISettingService.instance.state.environment
    }

    /**
     * 检查环境是否发生变化
     */
    fun hasEnvironmentChanged(lastEnvironment: String?): Boolean {
        val currentEnvironment = AIMISettingService.instance.state.environment
        return lastEnvironment != currentEnvironment
    }
}