package com.taobao.mc.aimi.find

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.find.providers.*
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 搜索引擎 - 统一的搜索入口
 * 管理所有搜索提供者，提供统一的搜索接口
 */
@Service(Service.Level.PROJECT)
class SearchEngine(private val project: Project) {
    private val logger = LoggerManager.getLogger(SearchEngine::class.java)
    
    // 创建服务级别的协程作用域
    private val engineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 搜索提供者注册表
    private val searchProviders = mutableMapOf<SearchType, SearchProvider>()
    
    // 搜索历史
    private val searchHistory = mutableListOf<SearchConfig>()
    private val maxHistorySize = 200
    
    // 活跃搜索任务
    private val activeSearches = ConcurrentHashMap<String, Job>()
    
    // 搜索统计
    private val searchStats = ConcurrentHashMap<SearchType, SearchStatistics>()
    
    // 服务状态
    private val isInitialized = AtomicBoolean(false)

    companion object {
        fun getInstance(project: Project): SearchEngine = project.service()
    }

    init {
        initializeSearchProviders()
    }

    /**
     * 初始化搜索提供者
     */
    private fun initializeSearchProviders() {
        if (isInitialized.compareAndSet(false, true)) {
            logger.info("初始化搜索引擎")
            
            // 注册所有搜索提供者
            registerProvider(TextSearchProvider(project))
            registerProvider(FileNameSearchProvider(project))
            registerProvider(UniversalSymbolSearchProvider(project))  // 替换原来的 SymbolSearchProvider
            registerProvider(ReferenceSearchProvider(project))
            registerProvider(IndexSearchProvider(project))
            registerProvider(PsiHelperSearchProvider(project))        // 新增 PsiHelper 搜索
            registerProvider(UsageSearchProvider(project))            // 新增 Usage 搜索
            
            // 初始化搜索统计
            SearchType.values().forEach { type ->
                searchStats[type] = SearchStatistics()
            }
            
            logger.info("搜索引擎初始化完成，注册了 ${searchProviders.size} 个搜索提供者")
        }
    }

    /**
     * 注册搜索提供者
     */
    private fun registerProvider(provider: SearchProvider) {
        SearchType.values().forEach { type ->
            if (provider.canHandle(type)) {
                searchProviders[type] = provider
                logger.debug("注册搜索提供者: $type -> ${provider::class.simpleName}")
            }
        }
    }

    /**
     * 执行单一类型搜索
     */
    suspend fun search(config: SearchConfig, callback: SearchProgressCallback? = null): SearchResults {
        logger.info("开始搜索: query=${config.query}, type=${config.searchType}")
        
        val startTime = System.currentTimeMillis()
        
        try {
            // 添加到搜索历史
            addToHistory(config)
            
            // 获取对应的搜索提供者
            val provider = searchProviders[config.searchType]
                ?: throw IllegalArgumentException("不支持的搜索类型: ${config.searchType}")
            
            // 执行搜索
            val results = provider.search(config, callback)
            
            // 更新统计信息
            updateSearchStats(config.searchType, results, System.currentTimeMillis() - startTime)
            
            return results
            
        } catch (e: Exception) {
            logger.error("搜索失败: ${e.message}", e)
            updateSearchStats(config.searchType, null, System.currentTimeMillis() - startTime, e)
            throw e
        }
    }

    /**
     * 异步执行搜索
     */
    fun searchAsync(config: SearchConfig, callback: SearchProgressCallback? = null): Job {
        val searchId = generateSearchId(config)
        
        // 取消之前的同类搜索
        cancelSearch(searchId)
        
        val job = engineScope.launch {
            try {
                val results = search(config, callback)
                callback?.onCompleted(results)
            } catch (e: CancellationException) {
                logger.warn("搜索被取消: $searchId")
                callback?.onCancelled()
            } catch (e: Exception) {
                logger.warn("异步搜索失败: ${e.message}", e)
                callback?.onError(e)
            }
        }
        
        activeSearches[searchId] = job
        
        // 清理完成的任务
        job.invokeOnCompletion {
            activeSearches.remove(searchId)
        }
        
        return job
    }

    /**
     * 组合搜索 - 同时使用多种搜索方式
     */
    suspend fun combinedSearch(
        query: String,
        searchTypes: Set<SearchType> = setOf(SearchType.TEXT, SearchType.SYMBOL, SearchType.FILE_NAME),
        scope: SearchScope = SearchScope.PROJECT,
        caseSensitive: Boolean = false,
        maxResults: Int = 200,
        callback: SearchProgressCallback? = null
    ): SearchResults {
        logger.info("开始组合搜索: query=$query, types=$searchTypes")
        
        val startTime = System.currentTimeMillis()
        val allResults = mutableListOf<SearchResult>()
        val errors = mutableListOf<String>()
        
        try {
            val searchConfigs = searchTypes.map { type ->
                SearchConfig(
                    query = query,
                    searchType = type,
                    scope = scope,
                    caseSensitive = caseSensitive,
                    maxResults = maxResults / searchTypes.size
                )
            }
            
            var completedSearches = 0
            val totalSearches = searchConfigs.size
            
            // 并行执行所有搜索
            val jobs = searchConfigs.map { config ->
                engineScope.async {
                    try {
                        callback?.onProgress(SearchProgress(
                            completedSearches,
                            totalSearches,
                            "执行 ${config.searchType} 搜索..."
                        ))
                        
                        val results = search(config)
                        completedSearches++
                        results.results
                    } catch (e: Exception) {
                        logger.warn("${config.searchType} 搜索失败: ${e.message}")
                        errors.add("${config.searchType}: ${e.message}")
                        completedSearches++
                        emptyList<SearchResult>()
                    }
                }
            }
            
            // 等待所有搜索完成
            val resultLists = jobs.awaitAll()
            resultLists.forEach { allResults.addAll(it) }
            
            // 去重、排序和限制结果
            val uniqueResults = allResults
                .distinctBy { "${it.file?.path}:${it.line}:${it.column}:${it.text}" }
                .sortedByDescending { it.relevanceScore }
                .take(maxResults)
            
            val searchTime = System.currentTimeMillis() - startTime
            val combinedResults = SearchResults(
                query = query,
                searchType = SearchType.TEXT, // 默认类型
                results = uniqueResults,
                totalCount = uniqueResults.size,
                searchTime = searchTime,
                hasMore = allResults.size > maxResults,
                suggestions = generateSearchSuggestions(query, uniqueResults),
                errors = errors
            )
            
            callback?.onProgress(SearchProgress(100, 100, "组合搜索完成，找到 ${uniqueResults.size} 个结果"))
            callback?.onCompleted(combinedResults)
            
            return combinedResults
            
        } catch (e: Exception) {
            logger.error("组合搜索失败: ${e.message}", e)
            callback?.onError(e)
            throw e
        }
    }

    /**
     * 智能搜索 - 根据查询内容自动选择最佳搜索策略
     */
    suspend fun smartSearch(
        query: String,
        callback: SearchProgressCallback? = null
    ): SearchResults {
        logger.info("开始智能搜索: query=$query")
        
        // 分析查询内容，选择最佳搜索策略
        val searchTypes = analyzeQueryAndSelectSearchTypes(query)
        
        return combinedSearch(
            query = query,
            searchTypes = searchTypes,
            callback = callback
        )
    }

    /**
     * 增量搜索 - 随着用户输入实时更新结果
     */
    fun incrementalSearch(
        query: String,
        callback: SearchProgressCallback,
        debounceMs: Long = 300
    ): Job {
        val searchId = "incremental_${query.hashCode()}"
        
        // 取消之前的增量搜索
        cancelSearch(searchId)
        
        val job = engineScope.launch {
            // 防抖延迟
            delay(debounceMs)
            
            if (query.length >= 2) { // 最少2个字符才开始搜索
                val config = SearchConfig(
                    query = query,
                    searchType = SearchType.TEXT,
                    maxResults = 50
                )
                
                try {
                    val results = search(config, callback)
                    callback.onCompleted(results)
                } catch (e: Exception) {
                    callback.onError(e)
                }
            }
        }
        
        activeSearches[searchId] = job
        return job
    }

    /**
     * 取消搜索
     */
    fun cancelSearch(searchId: String) {
        activeSearches[searchId]?.cancel()
        activeSearches.remove(searchId)
    }

    /**
     * 取消所有搜索
     */
    fun cancelAllSearches() {
        logger.info("取消所有活跃搜索")
        activeSearches.values.forEach { it.cancel() }
        activeSearches.clear()
    }

    /**
     * 获取搜索历史
     */
    fun getSearchHistory(): List<SearchConfig> {
        return searchHistory.toList()
    }

    /**
     * 清空搜索历史
     */
    fun clearSearchHistory() {
        searchHistory.clear()
        logger.info("搜索历史已清空")
    }

    /**
     * 获取搜索统计信息
     */
    fun getSearchStatistics(): Map<SearchType, SearchStatistics> {
        return searchStats.toMap()
    }

    /**
     * 获取活跃搜索数量
     */
    fun getActiveSearchCount(): Int {
        return activeSearches.size
    }

    /**
     * 检查服务是否可用
     */
    fun isAvailable(): Boolean {
        return isInitialized.get() && searchProviders.isNotEmpty()
    }

    /**
     * 获取支持的搜索类型
     */
    fun getSupportedSearchTypes(): Set<SearchType> {
        return searchProviders.keys.toSet()
    }

    /**
     * 获取搜索提供者支持的功能
     */
    fun getProviderFeatures(searchType: SearchType): Set<SearchFeature> {
        return searchProviders[searchType]?.getSupportedFeatures() ?: emptySet()
    }

    // ==================== 私有方法 ====================

    /**
     * 分析查询内容并选择搜索类型
     */
    private fun analyzeQueryAndSelectSearchTypes(query: String): Set<SearchType> {
        val searchTypes = mutableSetOf<SearchType>()
        
        // 基于查询内容的启发式规则
        when {
            // 文件扩展名模式
            query.contains(".") && query.split(".").size == 2 -> {
                searchTypes.add(SearchType.FILE_NAME)
            }
            
            // 驼峰命名模式（可能是类名或方法名）
            query.matches(Regex("[A-Z][a-zA-Z0-9]*")) -> {
                searchTypes.addAll(setOf(SearchType.SYMBOL, SearchType.REFERENCE))
            }
            
            // 全大写（可能是常量）
            query.matches(Regex("[A-Z_]+")) -> {
                searchTypes.addAll(setOf(SearchType.SYMBOL, SearchType.TEXT))
            }
            
            // 包含特殊字符（可能是正则表达式或路径）
            query.contains(Regex("[.*+?^\${}()|\\[\\]\\\\]")) -> {
                searchTypes.add(SearchType.TEXT)
            }
            
            // 短查询（可能是文件名或符号）
            query.length <= 3 -> {
                searchTypes.addAll(setOf(SearchType.FILE_NAME, SearchType.SYMBOL))
            }
            
            // 默认情况
            else -> {
                searchTypes.addAll(setOf(SearchType.TEXT, SearchType.SYMBOL, SearchType.FILE_NAME))
            }
        }
        
        return searchTypes
    }

    /**
     * 生成搜索建议
     */
    private fun generateSearchSuggestions(query: String, results: List<SearchResult>): List<String> {
        val suggestions = mutableListOf<String>()
        
        // 基于结果生成建议
        if (results.isEmpty()) {
            suggestions.add("尝试使用部分关键词")
            suggestions.add("检查拼写是否正确")
            suggestions.add("尝试使用不同的搜索类型")
        } else if (results.size < 5) {
            suggestions.add("尝试使用更宽泛的搜索词")
            suggestions.add("使用通配符 * 进行模糊匹配")
        }
        
        // 基于查询内容生成建议
        if (query.length < 3) {
            suggestions.add("使用更长的搜索词以获得更精确的结果")
        }
        
        return suggestions.distinct()
    }

    /**
     * 添加到搜索历史
     */
    private fun addToHistory(config: SearchConfig) {
        synchronized(searchHistory) {
            // 移除重复的搜索
            searchHistory.removeIf { it.query == config.query && it.searchType == config.searchType }
            
            // 添加到开头
            searchHistory.add(0, config)
            
            // 限制历史大小
            if (searchHistory.size > maxHistorySize) {
                searchHistory.removeAt(searchHistory.size - 1)
            }
        }
    }

    /**
     * 更新搜索统计
     */
    private fun updateSearchStats(searchType: SearchType, results: SearchResults?, duration: Long, error: Throwable? = null) {
        val stats = searchStats[searchType] ?: return
        
        stats.totalSearches++
        stats.totalDuration += duration
        
        if (error != null) {
            stats.errorCount++
        } else if (results != null) {
            stats.successCount++
            stats.totalResults += results.totalCount
        }
    }

    /**
     * 生成搜索ID
     */
    private fun generateSearchId(config: SearchConfig): String {
        return "${config.searchType}_${config.query.hashCode()}_${System.currentTimeMillis()}"
    }

    /**
     * 释放资源
     */
    fun dispose() {
        logger.info("释放搜索引擎资源")
        cancelAllSearches()
        engineScope.cancel()
        searchProviders.clear()
        searchHistory.clear()
        searchStats.clear()
        isInitialized.set(false)
    }
}

/**
 * 搜索统计信息
 */
data class SearchStatistics(
    var totalSearches: Long = 0,
    var successCount: Long = 0,
    var errorCount: Long = 0,
    var totalResults: Long = 0,
    var totalDuration: Long = 0
) {
    val averageDuration: Double
        get() = if (totalSearches > 0) totalDuration.toDouble() / totalSearches else 0.0
    
    val successRate: Double
        get() = if (totalSearches > 0) successCount.toDouble() / totalSearches else 0.0
    
    val averageResults: Double
        get() = if (successCount > 0) totalResults.toDouble() / successCount else 0.0
}
