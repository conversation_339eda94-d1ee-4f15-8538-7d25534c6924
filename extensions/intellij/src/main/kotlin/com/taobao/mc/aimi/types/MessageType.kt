package com.taobao.mc.aimi.types

sealed interface IMessageType {
    val type: String
}

sealed class MessageType {
    sealed class ToIDE(override val type: String) : IMessageType {
        // IDE Message Types from ideMessageTypes
        data object ReadRangeInFile : ToIDE("readRangeInFile")
        data object IsTelemetryEnabled : ToIDE("isTelemetryEnabled")
        data object GetUniqueId : ToIDE("getUniqueId")
        data object GetWorkspaceConfigs : ToIDE("getWorkspaceConfigs")
        data object GetDiff : ToIDE("getDiff")
        data object GetTerminalContents : ToIDE("getTerminalContents")
        data object GetWorkspaceDirs : ToIDE("getWorkspaceDirs")
        data object ShowLines : ToIDE("showLines")
        data object WriteFile : ToIDE("writeFile")
        data object FileExists : ToIDE("fileExists")
        data object ShowVirtualFile : ToIDE("showVirtualFile")
        data object OpenFile : ToIDE("openFile")
        data object RunCommand : ToIDE("runCommand")
        data object SaveFile : ToIDE("saveFile")
        data object ReadFile : ToIDE("readFile")
        data object ShowDiff : ToIDE("showDiff")
        data object GetOpenFiles : ToIDE("getOpenFiles")
        data object GetCurrentFile : ToIDE("getCurrentFile")
        data object GetPinnedFiles : ToIDE("getPinnedFiles")
        data object GetSearchResults : ToIDE("getSearchResults")
        data object GetFileResults : ToIDE("getFileResults")
        data object GetProblems : ToIDE("getProblems")
        data object Subprocess : ToIDE("subprocess")
        data object GetBranch : ToIDE("getBranch")
        data object GetTags : ToIDE("getTags")
        data object GetIdeInfo : ToIDE("getIdeInfo")
        data object GetIdeSettings : ToIDE("getIdeSettings")
        data object GetRepoName : ToIDE("getRepoName")
        data object ListDir : ToIDE("listDir")
        data object GetGitRootPath : ToIDE("getGitRootPath")
        data object GetFileStats : ToIDE("getFileStats")
        data object InsertAtCursor : ToIDE("insertAtCursor")
        data object ApplyToFile : ToIDE("applyToFile")
        data object SetControlPlaneSessionInfo : ToIDE("setControlPlaneSessionInfo")
        data object GetControlPlaneSessionInfo : ToIDE("getControlPlaneSessionInfo")
        data object LogoutOfControlPlane : ToIDE("logoutOfControlPlane")
        data object ShowToast : ToIDE("showToast")
        data object OpenUrl : ToIDE("openUrl")
        data object GetClipboardContent : ToIDE("getClipboardContent")

        // GUI only messages
        data object ToggleDevTools : ToIDE("toggleDevTools")
        data object ShowTutorial : ToIDE("showTutorial")

        // JetBrains specific messages
        data object CopyText : ToIDE("copyText")
        data object JetbrainsIsOSREnabled : ToIDE("jetbrains/isOSREnabled")
        data object JetbrainsGetColors : ToIDE("jetbrains/getColors")
        data object JetbrainsOnLoad : ToIDE("jetbrains/onLoad")

        data object JetbrainsChangeTitle : ToIDE("changeTitle")
        data object JetbrainsChangeWindow : ToIDE("changeWindow")

        // Refactor from webview
        data object IndexProgress : ToIDE("indexProgress")
    }

    sealed class ToWebview(override val type: String) : IMessageType {
        // Pass Through to Webview Messages from PASS_THROUGH_TO_WEBVIEW
        data object ConfigUpdate : ToWebview("configUpdate")
        data object IndexingStatusUpdate : ToWebview("indexing/statusUpdate")
        data object AddContextItem : ToWebview("addContextItem")
        data object RefreshSubmenuItems : ToWebview("refreshSubmenuItems")
        data object IsContinueInputFocused : ToWebview("isContinueInputFocused")
        data object SetTTSActive : ToWebview("setTTSActive")
        data object GetWebviewHistoryLength : ToWebview("getWebviewHistoryLength")
        data object GetCurrentSessionId : ToWebview("getCurrentSessionId")
        data object SessionUpdate : ToWebview("sessionUpdate")
        data object DidCloseFiles : ToWebview("didCloseFiles")
        data object ToolCallPartialOutput : ToWebview("toolCallPartialOutput")
        data object UpdateApplyState : ToWebview("updateApplyState")
        data object ActiveEditorChanged : ToWebview("activeEditorChanged")
    }

    sealed class ToCore(override val type: String) : IMessageType {
        // Pass Through to Core Messages from PASS_THROUGH_TO_CORE
        data object Abort : ToCore("abort")
        data object HistoryList : ToCore("history/list")
        data object HistoryDelete : ToCore("history/delete")
        data object HistoryLoad : ToCore("history/load")
        data object HistorySave : ToCore("history/save")
        data object HistoryClear : ToCore("history/clear")
        data object DevdataLog : ToCore("devdata/log")
        data object ConfigAddModel : ToCore("config/addModel")
        data object ConfigNewPromptFile : ToCore("config/newPromptFile")
        data object ConfigIdeSettingsUpdate : ToCore("config/ideSettingsUpdate")
        data object ConfigAddLocalWorkspaceBlock : ToCore("config/addLocalWorkspaceBlock")
        data object ConfigGetSerializedProfileInfo : ToCore("config/getSerializedProfileInfo")
        data object ConfigDeleteModel : ToCore("config/deleteModel")
        data object ConfigRefreshProfiles : ToCore("config/refreshProfiles")
        data object ConfigOpenProfile : ToCore("config/openProfile")
        data object ConfigUpdateSharedConfig : ToCore("config/updateSharedConfig")
        data object ConfigUpdateSelectedModel : ToCore("config/updateSelectedModel")
        data object McpReloadServer : ToCore("mcp/reloadServer")
        data object ContextGetContextItems : ToCore("context/getContextItems")
        data object ContextGetSymbolsForFiles : ToCore("context/getSymbolsForFiles")
        data object ContextLoadSubmenuItems : ToCore("context/loadSubmenuItems")
        data object ContextAddDocs : ToCore("context/addDocs")
        data object ContextRemoveDocs : ToCore("context/removeDocs")
        data object ContextIndexDocs : ToCore("context/indexDocs")
        data object AutocompleteComplete : ToCore("autocomplete/complete")
        data object AutocompleteCancel : ToCore("autocomplete/cancel")
        data object AutocompleteAccept : ToCore("autocomplete/accept")
        data object TtsKill : ToCore("tts/kill")
        data object LlmComplete : ToCore("llm/complete")
        data object LlmStreamChat : ToCore("llm/streamChat")
        data object LlmListModels : ToCore("llm/listModels")
        data object StreamDiffLines : ToCore("streamDiffLines")
        data object ChatDescriberDescribe : ToCore("chatDescriber/describe")
        data object StatsGetTokensPerDay : ToCore("stats/getTokensPerDay")
        data object StatsGetTokensPerModel : ToCore("stats/getTokensPerModel")
        data object IndexSetPaused : ToCore("index/setPaused")
        data object IndexForceReIndex : ToCore("index/forceReIndex")
        data object IndexForceReIndexFiles : ToCore("index/forceReIndexFiles")
        data object IndexIndexingProgressBarInitialized : ToCore("index/indexingProgressBarInitialized")
        data object IndexingReindex : ToCore("indexing/reindex")
        data object IndexingAbort : ToCore("indexing/abort")
        data object IndexingSetPaused : ToCore("indexing/setPaused")
        data object DocsInitStatuses : ToCore("docs/initStatuses")
        data object DocsGetDetails : ToCore("docs/getDetails")
        data object CompleteOnboarding : ToCore("completeOnboarding")
        data object AddAutocompleteModel : ToCore("addAutocompleteModel")
        data object DidChangeSelectedProfile : ToCore("didChangeSelectedProfile")
        data object DidChangeSelectedOrg : ToCore("didChangeSelectedOrg")
        data object ToolsCall : ToCore("tools/call")
        data object ControlPlaneOpenUrl : ToCore("controlPlane/openUrl")
        data object IsItemTooBig : ToCore("isItemTooBig")
        data object ProcessMarkAsBackgrounded : ToCore("process/markAsBackgrounded")
        data object ProcessIsBackgrounded : ToCore("process/isBackgrounded")
    }

    companion object {
        fun from(type: String): IMessageType? {
            return IMessageType::class.sealedSubclasses.mapNotNull { it.objectInstance }.firstOrNull() {
                it.type == type
            }
        }

        val ideMessageTypes by lazy {
            ToIDE::class.sealedSubclasses.mapNotNull { it.objectInstance }
        }

        val coreMessageTypes by lazy {
            ToCore::class.sealedSubclasses.mapNotNull { it.objectInstance }
        }

        val webviewMessageTypes by lazy {
            ToWebview::class.sealedSubclasses.mapNotNull { it.objectInstance }
        }
    }
}