package com.taobao.mc.aimi.find.providers

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiManager
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.util.indexing.FileBasedIndex
import com.intellij.util.indexing.ID
import com.taobao.mc.aimi.find.*
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 索引搜索提供者
 * 使用 IntelliJ 的 FileBasedIndex 进行快速索引搜索
 */
class IndexSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(IndexSearchProvider::class.java)
    private val fileBasedIndex = FileBasedIndex.getInstance()
    private val psiManager = PsiManager.getInstance(project)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.INDEX
    }

    override fun getSupportedFeatures(): Set<SearchFeature> {
        return setOf(
            SearchFeature.CASE_SENSITIVE,
            SearchFeature.FUZZY_MATCH,
            SearchFeature.ASYNC,
            SearchFeature.CANCELLABLE,
            SearchFeature.PROGRESS_TRACKING,
            SearchFeature.RESULT_RANKING
        )
    }

    override suspend fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        return withContext(Dispatchers.IO) {
            try {
                callback?.onProgress(SearchProgress(0, 100, "开始索引搜索..."))

                val scope = createSearchScope(config)
                
                // 1. 文件名索引搜索
                callback?.onProgress(SearchProgress(10, 100, "搜索文件名索引..."))
                val filenameResults = searchFilenameIndex(config.query, scope, config)
                results.addAll(filenameResults)

                // 2. 单词索引搜索
                callback?.onProgress(SearchProgress(25, 100, "搜索单词索引..."))
                val wordResults = searchWordIndex(config.query, scope, config)
                results.addAll(wordResults)

                // 3. 标识符索引搜索
                callback?.onProgress(SearchProgress(40, 100, "搜索标识符索引..."))
                val identifierResults = searchIdentifierIndex(config.query, scope, config)
                results.addAll(identifierResults)

                // 4. 注释索引搜索
                callback?.onProgress(SearchProgress(55, 100, "搜索注释索引..."))
                val commentResults = searchCommentIndex(config.query, scope, config)
                results.addAll(commentResults)

                // 5. 字符串字面量索引搜索
                callback?.onProgress(SearchProgress(70, 100, "搜索字符串字面量索引..."))
                val stringResults = searchStringLiteralIndex(config.query, scope, config)
                results.addAll(stringResults)

                // 6. 自定义索引搜索
                callback?.onProgress(SearchProgress(85, 100, "搜索自定义索引..."))
                val customResults = searchCustomIndexes(config.query, scope, config)
                results.addAll(customResults)

                val searchTime = System.currentTimeMillis() - startTime
                val uniqueResults = results
                    .distinctBy { "${it.file?.path}:${it.line}:${it.column}:${it.text}" }
                    .sortedByDescending { it.relevanceScore }
                    .take(config.maxResults)

                val searchResults = SearchResults(
                    query = config.query,
                    searchType = config.searchType,
                    results = uniqueResults,
                    totalCount = uniqueResults.size,
                    searchTime = searchTime,
                    hasMore = results.size > config.maxResults
                )

                callback?.onProgress(SearchProgress(100, 100, "索引搜索完成，找到 ${uniqueResults.size} 个结果"))
                callback?.onCompleted(searchResults)

                searchResults

            } catch (e: Exception) {
                logger.error("索引搜索失败: ${e.message}", e)
                callback?.onError(e)
                throw e
            }
        }
    }

    /**
     * 搜索文件名索引
     */
    private fun searchFilenameIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 精确匹配
                val exactFiles = FilenameIndex.getFilesByName(project, query, scope)
                for (file in exactFiles) {
                    if (shouldIncludeFile(file.virtualFile, config)) {
                        results.add(createIndexResult(file.virtualFile, query, "文件名精确匹配", 2.0))
                    }
                }

                // 模糊匹配
                if (!config.caseSensitive) {
                    val allFilenames = FilenameIndex.getAllFilenames(project)
                    for (filename in allFilenames.take(500)) { // 限制处理数量
                        if (filename.contains(query, ignoreCase = true) && filename != query) {
                            val files = FilenameIndex.getFilesByName(project, filename, scope)
                            for (file in files.take(3)) { // 限制每个文件名的结果数量
                                if (shouldIncludeFile(file.virtualFile, config)) {
                                    val score = calculateFilenameScore(filename, query)
                                    results.add(createIndexResult(file.virtualFile, query, "文件名模糊匹配", score))
                                }
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("搜索文件名索引时发生错误: ${e.message}")
            }
            
            results.take(50) // 限制文件名结果数量
        }
    }

    /**
     * 搜索单词索引
     */
    private fun searchWordIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 尝试查找单词索引
                val wordIndexIds = listOf("word", "words", "word.index")
                
                for (indexName in wordIndexIds) {
                    val wordIndexId = ID.findByName<String, Void>(indexName)
                    if (wordIndexId != null) {
                        try {
                            val files = fileBasedIndex.getContainingFiles(wordIndexId, query, scope)
                            for (file in files.take(30)) { // 限制文件数量
                                if (shouldIncludeFile(file, config)) {
                                    results.add(createIndexResult(file, query, "单词索引匹配", 1.5))
                                }
                            }
                        } catch (e: Exception) {
                            logger.debug("搜索单词索引 $indexName 时发生错误: ${e.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("搜索单词索引时发生错误: ${e.message}")
            }
            
            results.take(40)
        }
    }

    /**
     * 搜索标识符索引
     */
    private fun searchIdentifierIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 尝试查找标识符索引
                val identifierIndexIds = listOf(
                    "identifier", "identifiers", "java.class.name", "java.method.name", 
                    "java.field.name", "kotlin.class.name", "kotlin.function.name"
                )
                
                for (indexName in identifierIndexIds) {
                    val indexId = ID.findByName<String, Void>(indexName)
                    if (indexId != null) {
                        try {
                            val files = fileBasedIndex.getContainingFiles(indexId, query, scope)
                            for (file in files.take(25)) {
                                if (shouldIncludeFile(file, config)) {
                                    results.add(createIndexResult(file, query, "标识符索引匹配", 1.7))
                                }
                            }
                        } catch (e: Exception) {
                            logger.debug("搜索标识符索引 $indexName 时发生错误: ${e.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("搜索标识符索引时发生错误: ${e.message}")
            }
            
            results.take(35)
        }
    }

    /**
     * 搜索注释索引
     */
    private fun searchCommentIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 尝试查找注释索引
                val commentIndexIds = listOf("comment", "comments", "comment.index")
                
                for (indexName in commentIndexIds) {
                    val indexId = ID.findByName<String, Void>(indexName)
                    if (indexId != null) {
                        try {
                            val files = fileBasedIndex.getContainingFiles(indexId, query, scope)
                            for (file in files.take(20)) {
                                if (shouldIncludeFile(file, config)) {
                                    results.add(createIndexResult(file, query, "注释索引匹配", 1.3))
                                }
                            }
                        } catch (e: Exception) {
                            logger.debug("搜索注释索引 $indexName 时发生错误: ${e.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("搜索注释索引时发生错误: ${e.message}")
            }
            
            results.take(25)
        }
    }

    /**
     * 搜索字符串字面量索引
     */
    private fun searchStringLiteralIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 尝试查找字符串字面量索引
                val stringIndexIds = listOf("string.literal", "string", "literals")
                
                for (indexName in stringIndexIds) {
                    val indexId = ID.findByName<String, Void>(indexName)
                    if (indexId != null) {
                        try {
                            val files = fileBasedIndex.getContainingFiles(indexId, query, scope)
                            for (file in files.take(20)) {
                                if (shouldIncludeFile(file, config)) {
                                    results.add(createIndexResult(file, query, "字符串字面量索引匹配", 1.4))
                                }
                            }
                        } catch (e: Exception) {
                            logger.debug("搜索字符串索引 $indexName 时发生错误: ${e.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("搜索字符串字面量索引时发生错误: ${e.message}")
            }
            
            results.take(25)
        }
    }

    /**
     * 搜索自定义索引
     */
    private fun searchCustomIndexes(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 获取所有可用的索引并尝试搜索
                val customIndexIds = listOf(
                    "todo", "fixme", "hack", "note", "bug",
                    "xml.namespace", "xml.attribute", "xml.tag",
                    "properties.key", "properties.value",
                    "json.key", "json.value"
                )
                
                for (indexName in customIndexIds) {
                    val indexId = ID.findByName<String, Void>(indexName)
                    if (indexId != null) {
                        try {
                            val files = fileBasedIndex.getContainingFiles(indexId, query, scope)
                            for (file in files.take(10)) {
                                if (shouldIncludeFile(file, config)) {
                                    results.add(createIndexResult(file, query, "自定义索引匹配($indexName)", 1.2))
                                }
                            }
                        } catch (e: Exception) {
                            logger.debug("搜索自定义索引 $indexName 时发生错误: ${e.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("搜索自定义索引时发生错误: ${e.message}")
            }
            
            results.take(30)
        }
    }

    /**
     * 创建索引搜索结果
     */
    private fun createIndexResult(file: VirtualFile, query: String, indexType: String, score: Double): IndexSearchResult {
        return IndexSearchResult(
            file = file,
            line = 1,
            column = 1,
            text = file.name,
            context = "文件: ${file.path}",
            relevanceScore = score,
            indexKey = query,
            indexValue = file.name,
            indexType = indexType
        )
    }

    /**
     * 检查是否应该包含文件
     */
    private fun shouldIncludeFile(file: VirtualFile, config: SearchConfig): Boolean {
        // 文件类型过滤
        if (config.fileTypes.isNotEmpty() && !config.fileTypes.contains(file.extension ?: "")) {
            return false
        }
        
        // 排除模式过滤
        return config.excludePatterns.none { pattern ->
            file.path.contains(pattern, ignoreCase = true)
        }
    }

    /**
     * 计算文件名匹配分数
     */
    private fun calculateFilenameScore(filename: String, query: String): Double {
        var score = 1.0
        
        if (filename.startsWith(query, ignoreCase = true)) {
            score = 1.8
        } else if (filename.contains(query, ignoreCase = true)) {
            score = 1.5
        }
        
        // 长度影响
        val lengthRatio = query.length.toDouble() / filename.length
        score += lengthRatio * 0.3
        
        return kotlin.math.min(score, 2.0)
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.scope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
            SearchScope.FILE -> GlobalSearchScope.projectScope(project)
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
            SearchScope.OPEN_FILES -> GlobalSearchScope.projectScope(project)
            SearchScope.CUSTOM -> GlobalSearchScope.projectScope(project)
        }
    }

    /**
     * 获取所有可用的索引ID列表
     */
    private fun getAllAvailableIndexes(): List<String> {
        return try {
            // 常见的索引名称
            listOf(
                "word", "words", "word.index",
                "identifier", "identifiers",
                "comment", "comments", "comment.index",
                "string.literal", "string", "literals",
                "java.class.name", "java.method.name", "java.field.name",
                "kotlin.class.name", "kotlin.function.name", "kotlin.property.name",
                "xml.namespace", "xml.attribute", "xml.tag",
                "properties.key", "properties.value",
                "json.key", "json.value",
                "todo", "fixme", "hack", "note", "bug"
            )
        } catch (e: Exception) {
            logger.debug("获取索引列表时发生错误: ${e.message}")
            emptyList()
        }
    }
}
