package com.taobao.mc.aimi.find.language

import com.intellij.psi.PsiElement

/**
 * 语言特定的元素类型定义
 * 支持多种编程语言的 PSI 元素识别
 */
object LanguageElementTypes {

    /**
     * 元素类型枚举
     */
    enum class ElementType {
        CLASS,
        INTERFACE,
        ENUM,
        ANNOTATION,
        METHOD,
        FUNCTION,
        CONSTRUCTOR,
        PROPER<PERSON>,
        FIELD,
        VA<PERSON>ABLE,
        PARAMETER,
        CONSTANT,
        NAMESPACE,
        MODULE,
        PROTOCOL,
        STRUCT,
        EXTENSION,
        DELEGATE,
        LAMBDA,
        CLOSURE,
        COMMENT,
        STRING_LITERAL,
        UNKNOWN
    }

    /**
     * 语言特定的元素类型映射
     */
    private val languageElementMappings = mapOf(
        // Java
        "java" to JavaElementMapping,
        
        // Kotlin
        "kt" to KotlinElementMapping,
        
        // Objective-C
        "m" to ObjectiveCElementMapping,
        "mm" to ObjectiveCElementMapping,
        "h" to ObjectiveCElementMapping,
        
        // <PERSON>
        "swift" to SwiftElementMapping,
        
        // TypeScript/JavaScript
        "ts" to TypeScriptElementMapping,
        "tsx" to TypeScriptElementMapping,
        "js" to JavaScriptElementMapping,
        "jsx" to JavaScriptElementMapping,
        
        // C/C++
        "c" to CElementMapping,
        "cpp" to CppElementMapping,
        "cc" to CppElementMapping,
        "cxx" to CppElementMapping,
        
        // Python
        "py" to PythonElementMapping,
        
        // Go
        "go" to GoElementMapping,
        
        // Rust
        "rs" to RustElementMapping
    )

    /**
     * 根据文件扩展名获取语言元素映射
     */
    fun getElementMapping(fileExtension: String?): LanguageElementMapping {
        return languageElementMappings[fileExtension?.lowercase()] ?: GenericElementMapping
    }

    /**
     * 确定 PSI 元素的类型
     */
    fun determineElementType(element: PsiElement, fileExtension: String?): ElementType {
        val mapping = getElementMapping(fileExtension)
        return mapping.getElementType(element)
    }

    /**
     * 获取元素的显示名称
     */
    fun getElementDisplayName(elementType: ElementType, language: String?): String {
        return when (language?.lowercase()) {
            "java" -> getJavaDisplayName(elementType)
            "kotlin", "kt" -> getKotlinDisplayName(elementType)
            "swift" -> getSwiftDisplayName(elementType)
            "objc", "objective-c" -> getObjectiveCDisplayName(elementType)
            "typescript", "ts" -> getTypeScriptDisplayName(elementType)
            "javascript", "js" -> getJavaScriptDisplayName(elementType)
            else -> getGenericDisplayName(elementType)
        }
    }

    private fun getJavaDisplayName(elementType: ElementType): String = when (elementType) {
        ElementType.CLASS -> "类"
        ElementType.INTERFACE -> "接口"
        ElementType.ENUM -> "枚举"
        ElementType.ANNOTATION -> "注解"
        ElementType.METHOD -> "方法"
        ElementType.CONSTRUCTOR -> "构造器"
        ElementType.FIELD -> "字段"
        ElementType.VARIABLE -> "变量"
        ElementType.PARAMETER -> "参数"
        ElementType.CONSTANT -> "常量"
        else -> getGenericDisplayName(elementType)
    }

    private fun getKotlinDisplayName(elementType: ElementType): String = when (elementType) {
        ElementType.CLASS -> "类"
        ElementType.INTERFACE -> "接口"
        ElementType.ENUM -> "枚举"
        ElementType.ANNOTATION -> "注解"
        ElementType.FUNCTION -> "函数"
        ElementType.PROPERTY -> "属性"
        ElementType.VARIABLE -> "变量"
        ElementType.PARAMETER -> "参数"
        ElementType.CONSTANT -> "常量"
        ElementType.EXTENSION -> "扩展"
        ElementType.LAMBDA -> "Lambda"
        else -> getGenericDisplayName(elementType)
    }

    private fun getSwiftDisplayName(elementType: ElementType): String = when (elementType) {
        ElementType.CLASS -> "类"
        ElementType.PROTOCOL -> "协议"
        ElementType.STRUCT -> "结构体"
        ElementType.ENUM -> "枚举"
        ElementType.FUNCTION -> "函数"
        ElementType.METHOD -> "方法"
        ElementType.PROPERTY -> "属性"
        ElementType.VARIABLE -> "变量"
        ElementType.CONSTANT -> "常量"
        ElementType.EXTENSION -> "扩展"
        ElementType.CLOSURE -> "闭包"
        else -> getGenericDisplayName(elementType)
    }

    private fun getObjectiveCDisplayName(elementType: ElementType): String = when (elementType) {
        ElementType.CLASS -> "类"
        ElementType.PROTOCOL -> "协议"
        ElementType.METHOD -> "方法"
        ElementType.PROPERTY -> "属性"
        ElementType.VARIABLE -> "变量"
        ElementType.CONSTANT -> "常量"
        else -> getGenericDisplayName(elementType)
    }

    private fun getTypeScriptDisplayName(elementType: ElementType): String = when (elementType) {
        ElementType.CLASS -> "类"
        ElementType.INTERFACE -> "接口"
        ElementType.ENUM -> "枚举"
        ElementType.FUNCTION -> "函数"
        ElementType.METHOD -> "方法"
        ElementType.PROPERTY -> "属性"
        ElementType.VARIABLE -> "变量"
        ElementType.CONSTANT -> "常量"
        ElementType.NAMESPACE -> "命名空间"
        ElementType.MODULE -> "模块"
        else -> getGenericDisplayName(elementType)
    }

    private fun getJavaScriptDisplayName(elementType: ElementType): String = when (elementType) {
        ElementType.CLASS -> "类"
        ElementType.FUNCTION -> "函数"
        ElementType.METHOD -> "方法"
        ElementType.PROPERTY -> "属性"
        ElementType.VARIABLE -> "变量"
        ElementType.CONSTANT -> "常量"
        else -> getGenericDisplayName(elementType)
    }

    private fun getGenericDisplayName(elementType: ElementType): String = when (elementType) {
        ElementType.CLASS -> "类"
        ElementType.INTERFACE -> "接口"
        ElementType.ENUM -> "枚举"
        ElementType.ANNOTATION -> "注解"
        ElementType.METHOD -> "方法"
        ElementType.FUNCTION -> "函数"
        ElementType.CONSTRUCTOR -> "构造器"
        ElementType.PROPERTY -> "属性"
        ElementType.FIELD -> "字段"
        ElementType.VARIABLE -> "变量"
        ElementType.PARAMETER -> "参数"
        ElementType.CONSTANT -> "常量"
        ElementType.NAMESPACE -> "命名空间"
        ElementType.MODULE -> "模块"
        ElementType.PROTOCOL -> "协议"
        ElementType.STRUCT -> "结构体"
        ElementType.EXTENSION -> "扩展"
        ElementType.DELEGATE -> "委托"
        ElementType.LAMBDA -> "Lambda"
        ElementType.CLOSURE -> "闭包"
        ElementType.COMMENT -> "注释"
        ElementType.STRING_LITERAL -> "字符串字面量"
        ElementType.UNKNOWN -> "未知"
    }
}

/**
 * 语言元素映射接口
 */
interface LanguageElementMapping {
    fun getElementType(element: PsiElement): LanguageElementTypes.ElementType
    fun getElementPatterns(): Map<LanguageElementTypes.ElementType, List<String>>
}

/**
 * 通用元素映射
 */
object GenericElementMapping : LanguageElementMapping {
    override fun getElementType(element: PsiElement): LanguageElementTypes.ElementType {
        val elementString = element.toString().lowercase()
        val className = element.javaClass.simpleName.lowercase()
        
        return when {
            elementString.contains("class") || className.contains("class") -> LanguageElementTypes.ElementType.CLASS
            elementString.contains("interface") || className.contains("interface") -> LanguageElementTypes.ElementType.INTERFACE
            elementString.contains("enum") || className.contains("enum") -> LanguageElementTypes.ElementType.ENUM
            elementString.contains("method") || className.contains("method") -> LanguageElementTypes.ElementType.METHOD
            elementString.contains("function") || className.contains("function") -> LanguageElementTypes.ElementType.FUNCTION
            elementString.contains("field") || className.contains("field") -> LanguageElementTypes.ElementType.FIELD
            elementString.contains("property") || className.contains("property") -> LanguageElementTypes.ElementType.PROPERTY
            elementString.contains("variable") || className.contains("variable") -> LanguageElementTypes.ElementType.VARIABLE
            elementString.contains("parameter") || className.contains("parameter") -> LanguageElementTypes.ElementType.PARAMETER
            elementString.contains("comment") || className.contains("comment") -> LanguageElementTypes.ElementType.COMMENT
            else -> LanguageElementTypes.ElementType.UNKNOWN
        }
    }

    override fun getElementPatterns(): Map<LanguageElementTypes.ElementType, List<String>> {
        return mapOf(
            LanguageElementTypes.ElementType.CLASS to listOf("class", "psiclass"),
            LanguageElementTypes.ElementType.INTERFACE to listOf("interface", "psiinterface"),
            LanguageElementTypes.ElementType.ENUM to listOf("enum", "psienum"),
            LanguageElementTypes.ElementType.METHOD to listOf("method", "psimethod"),
            LanguageElementTypes.ElementType.FUNCTION to listOf("function", "psifunction"),
            LanguageElementTypes.ElementType.FIELD to listOf("field", "psifield"),
            LanguageElementTypes.ElementType.PROPERTY to listOf("property", "psiproperty"),
            LanguageElementTypes.ElementType.VARIABLE to listOf("variable", "psivariable"),
            LanguageElementTypes.ElementType.PARAMETER to listOf("parameter", "psiparameter"),
            LanguageElementTypes.ElementType.COMMENT to listOf("comment", "psicomment")
        )
    }
}
