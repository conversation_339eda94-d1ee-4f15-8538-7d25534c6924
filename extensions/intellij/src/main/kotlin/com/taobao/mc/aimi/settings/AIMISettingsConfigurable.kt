package com.taobao.mc.aimi.settings

import com.intellij.openapi.options.Configurable
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManager
import com.intellij.openapi.wm.IdeFrame
import com.taobao.mc.aimi.logger.LoggerManager
import java.awt.Container
import java.awt.KeyboardFocusManager
import javax.swing.JComponent

class AIMISettingsConfigurable : Configurable {
    private val logger = LoggerManager.getLogger(javaClass)
    private var settingsComponent: AIMISettingsComponent? = null

    override fun createComponent(): JComponent {
        // 获取当前激活的项目
        val project = getActiveProject()
        logger.info("当前激活的项目: $project")
        return AIMISettingsComponent(project).apply { settingsComponent = this }.panel
    }

    /**
     * 获取当前激活的项目
     * 优先获取当前焦点项目，如果没有则获取打开的项目列表中的第一个
     */
    private fun getActiveProject(): Project? {
        return try {
            // 尝试获取当前激活的项目
            queryActiveProject(KeyboardFocusManager.getCurrentKeyboardFocusManager().activeWindow) ?: run {
                val projectManager = ProjectManager.getInstance()
                projectManager.openProjects.firstOrNull()
            }
        } catch (_: Exception) {
            null
        }
    }

    override fun isModified(): Boolean {
        val settings = AIMISettingService.instance
        val selectedOption = settingsComponent?.completionTypeComboBox?.selectedItem as? MultilineOptions
        val selectedEnvironment = settingsComponent?.environmentComboBox?.selectedItem as? EnvironmentOption
        val modified =
            settingsComponent?.enableTabAutocomplete?.isSelected != settings.state.enableTabAutocomplete ||
            settingsComponent?.enableCrossFileCompletion?.isSelected != settings.state.enableCrossFileComplete ||
                    // settingsComponent?.enableOSR?.isSelected != settings.state.enableOSR ||
                    settingsComponent?.debounceDelaySpinner?.value != settings.state.debounceDelay ||
                    selectedOption?.value != settings.state.multilineCompletions ||
                    selectedEnvironment?.value != settings.state.environment
        return modified
    }

    override fun apply() {
        val settings = AIMISettingService.instance
        var notifyCore = false

        val enableTabAutoComplete = settingsComponent?.enableTabAutocomplete?.isSelected ?: false
        if (settings.state.enableTabAutocomplete != enableTabAutoComplete) {
            settings.state.enableTabAutocomplete = enableTabAutoComplete
        }

        val enableCrossFileComplete = settingsComponent?.enableCrossFileCompletion?.isSelected ?: false
        if (settings.state.enableCrossFileComplete != enableCrossFileComplete) {
            settings.state.enableCrossFileComplete = enableCrossFileComplete
            notifyCore = true
        }

        // val enableOSR = settingsComponent?.enableOSR?.isSelected ?: true
        // if (settings.state.enableOSR != enableOSR) {
        //     settings.state.enableOSR = enableOSR
        // }

        val debounceDelay = settingsComponent?.debounceDelaySpinner?.value as? Int ?: 500
        if (settings.state.debounceDelay != debounceDelay) {
            settings.state.debounceDelay = debounceDelay
            notifyCore = true
        }

        val selectedOption = settingsComponent?.completionTypeComboBox?.selectedItem as? MultilineOptions ?: MultilineOptions.AUTO
        if (selectedOption.value != settings.state.multilineCompletions) {
            logger.info("补全形式已切换为: ${selectedOption.displayName} (${selectedOption.value})")
            settings.state.multilineCompletions = selectedOption.value
            notifyCore = true
        }

        // 处理环境选择变更
        val selectedEnvironment = settingsComponent?.environmentComboBox?.selectedItem as? EnvironmentOption ?: EnvironmentOption.PRODUCTION
        if (selectedEnvironment.value != settings.state.environment) {
            logger.info("环境已切换为: ${selectedEnvironment.displayName} (${selectedEnvironment.value})")
            settings.state.environment = selectedEnvironment.value
            notifyCore = true
        }

        if (notifyCore) {
            settings.state.modify.incrementAndGet()
            settings.notifySettingsUpdated()
        }
    }

    override fun reset() {
        val settings = AIMISettingService.instance
        settingsComponent?.enableTabAutocomplete?.isSelected = settings.state.enableTabAutocomplete
        settingsComponent?.enableCrossFileCompletion?.isSelected = settings.state.enableCrossFileComplete
        // settingsComponent?.enableOSR?.isSelected = settings.state.enableOSR
        settingsComponent?.debounceDelaySpinner?.value = settings.state.debounceDelay

        // 根据保存的值找到对应的枚举选项
        val matchingOption = MultilineOptions.fromValue(settings.state.multilineCompletions)
        settingsComponent?.completionTypeComboBox?.selectedItem = matchingOption

        // 重置环境选择
        val matchingEnvironment = EnvironmentOption.fromValue(settings.state.environment)
        settingsComponent?.environmentComboBox?.selectedItem = matchingEnvironment
    }

    override fun disposeUIResources() {
        // 释放消息总线连接
        settingsComponent?.dispose()
        settingsComponent = null
    }

    override fun getDisplayName(): String {
        return "AIMI Settings"
    }

    private fun queryActiveProject(window: Container?): Project? {
        window ?: return null
        if (window is IdeFrame) return window.project
        return queryActiveProject(window.parent)
    }
}