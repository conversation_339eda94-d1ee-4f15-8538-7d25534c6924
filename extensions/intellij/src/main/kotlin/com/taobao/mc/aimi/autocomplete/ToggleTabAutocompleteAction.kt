package com.taobao.mc.aimi.autocomplete

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.service
import com.taobao.mc.aimi.settings.AIMISettingService

class ToggleTabAutocompleteAction(title: String, private val onClick: () -> Unit = {}) : AnAction(title) {
    override fun actionPerformed(e: AnActionEvent) {
        val settings = service<AIMISettingService>()
        // 切换自动补全状态
        settings.state.enableTabAutocomplete = !settings.state.enableTabAutocomplete
        // 通知设置变化
        onClick()
    }
}
