package com.taobao.mc.aimi.util

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor


val FileEditor.editor: Editor?
    get() = (this as? TextEditor)?.editor

fun FileEditor.readText(): String {
    return editor?.document?.text
        ?: run {
            file?.let {
                FileDocumentManager.getInstance().getDocument(it)?.text
            } ?: ""
        }
}

val FileEditorManager.mSelectedTextEditor: Editor?
    get() = selectedTextEditor ?: selectedEditor?.editor