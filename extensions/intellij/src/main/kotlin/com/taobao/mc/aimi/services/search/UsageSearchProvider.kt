package com.taobao.mc.aimi.services.search

// import com.intellij.find.usages.api.PsiUsage
// import com.intellij.find.usages.api.Usage
// import com.intellij.find.usages.api.UsageSearchParameters
// import com.intellij.find.usages.api.UsageSearcher
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.*
import com.intellij.psi.search.GlobalSearchScope
// import com.intellij.psi.search.PsiShortNamesCache
import com.intellij.psi.search.searches.ReferencesSearch
import com.intellij.psi.util.PsiTreeUtil
// import com.intellij.usages.UsageInfo
// import com.intellij.usages.UsageInfoToUsageConverter
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.*

/**
 * Usage 搜索提供者
 * 使用 ReferencesSearch 和 UsageSearcher 查找符号使用
 */
class UsageSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(UsageSearchProvider::class.java)
    private val psiManager = PsiManager.getInstance(project)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.USAGE
    }

    override fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        try {
            callback?.onProgress(0, 100, "开始 Usage 搜索...")

            // 查找符号定义
            val elements = findElementsByName(config.query, config)
            callback?.onProgress(20, 100, "找到 ${elements.size} 个符号定义")

            if (elements.isEmpty()) {
                val searchResults = SearchResults(
                    query = config.query,
                    searchType = config.searchType,
                    results = emptyList(),
                    totalCount = 0,
                    searchTime = System.currentTimeMillis() - startTime
                )
                callback?.onCompleted(searchResults)
                return searchResults
            }

            var processedElements = 0
            val totalElements = elements.size

            for (element in elements) {
                if (results.size >= config.maxResults) {
                    break
                }

                try {
                    val usageResults = findUsagesForElement(element, config)
                    results.addAll(usageResults)
                    
                    processedElements++
                    val progress = 20 + (processedElements * 70 / totalElements)
                    callback?.onProgress(progress, 100, "搜索符号使用: ${getElementName(element)} (${processedElements}/${totalElements})")
                    
                } catch (e: Exception) {
                    logger.warn("搜索元素 ${getElementName(element)} 的使用时发生错误: ${e.message}")
                }
            }

            val searchTime = System.currentTimeMillis() - startTime
            val searchResults = SearchResults(
                query = config.query,
                searchType = config.searchType,
                results = results.take(config.maxResults),
                totalCount = results.size,
                searchTime = searchTime,
                hasMore = results.size > config.maxResults
            )

            callback?.onProgress(100, 100, "Usage 搜索完成，找到 ${results.size} 个使用")
            callback?.onCompleted(searchResults)

            return searchResults

        } catch (e: Exception) {
            logger.error("Usage 搜索失败: ${e.message}", e)
            callback?.onError(e)
            throw e
        }
    }

    /**
     * 根据名称查找 PSI 元素
     */
    private fun findElementsByName(name: String, config: SearchConfig): List<PsiElement> {
        return ReadAction.compute<List<PsiElement>, RuntimeException> {
            val elements = mutableListOf<PsiElement>()

            try {
                // 简化实现：通过文件遍历查找命名元素
                val scope = createSearchScope(config)
                val files = getFilesToSearch(scope, config.fileTypes)

                for (file in files.take(50)) { // 限制文件数量
                    val psiFile = psiManager.findFile(file) ?: continue

                    // 查找命名元素
                    val namedElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiNamedElement::class.java)
                    for (element in namedElements) {
                        val elementName = element.name
                        if (elementName != null && matchesQuery(elementName, name, config.caseSensitive)) {
                            elements.add(element)
                        }
                    }
                }

            } catch (e: Exception) {
                logger.warn("查找元素时发生错误: ${e.message}")
            }

            elements.distinctBy { it.textOffset }.take(20) // 限制结果数量
        }
    }

    /**
     * 获取需要搜索的文件列表
     */
    private fun getFilesToSearch(scope: GlobalSearchScope, fileTypes: Set<String>): List<VirtualFile> {
        return ReadAction.compute<List<VirtualFile>, RuntimeException> {
            val allFiles = mutableListOf<VirtualFile>()

            val allFileNames = com.intellij.psi.search.FilenameIndex.getAllFilenames(project)

            for (fileName in allFileNames.toList().take(100)) { // 限制文件名数量
                val files = com.intellij.psi.search.FilenameIndex.getFilesByName(project, fileName, scope)
                for (file in files) {
                    if (file.isValid && !file.virtualFile.isDirectory) {
                        if (fileTypes.isEmpty() || fileTypes.contains(file.virtualFile.extension ?: "")) {
                            allFiles.add(file.virtualFile)
                        }
                    }
                }
            }

            allFiles.take(50) // 限制总文件数量
        }
    }

    /**
     * 检查是否匹配查询
     */
    private fun matchesQuery(text: String, query: String, caseSensitive: Boolean): Boolean {
        return if (caseSensitive) {
            text.contains(query)
        } else {
            text.contains(query, ignoreCase = true)
        }
    }

    /**
     * 查找元素的所有使用
     */
    private fun findUsagesForElement(element: PsiElement, config: SearchConfig): List<UsageSearchResult> {
        return ReadAction.compute<List<UsageSearchResult>, RuntimeException> {
            val results = mutableListOf<UsageSearchResult>()
            val scope = createSearchScope(config)

            try {
                // 使用 ReferencesSearch 查找引用
                val references = ReferencesSearch.search(element, scope).findAll()
                
                for (reference in references) {
                    if (results.size >= 50) { // 限制单个元素的结果数量
                        break
                    }

                    try {
                        val refElement = reference.element
                        val containingFile = refElement.containingFile?.virtualFile
                        
                        if (containingFile != null) {
                            val document = PsiDocumentManager.getInstance(project).getDocument(refElement.containingFile!!)
                            
                            if (document != null) {
                                val textRange = reference.rangeInElement.shiftRight(refElement.textRange.startOffset)
                                val lineNumber = document.getLineNumber(textRange.startOffset)
                                val columnNumber = textRange.startOffset - document.getLineStartOffset(lineNumber)
                                
                                val context = getUsageContext(document, lineNumber, textRange)
                                val usageType = determineUsageType(refElement, element)
                                
                                val result = UsageSearchResult(
                                    file = containingFile,
                                    line = lineNumber + 1,
                                    column = columnNumber + 1,
                                    text = reference.canonicalText,
                                    context = context,
                                    usage = createUsageFromReference(reference),
                                    element = refElement,
                                    usageType = usageType
                                )
                                
                                results.add(result)
                            }
                        }
                    } catch (e: Exception) {
                        logger.debug("处理引用时发生错误: ${e.message}")
                    }
                }

            } catch (e: Exception) {
                logger.warn("查找元素使用时发生错误: ${e.message}")
            }

            results
        }
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.searchScope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
            SearchScope.FILE -> GlobalSearchScope.projectScope(project)
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
        }
    }

    /**
     * 获取元素名称
     */
    private fun getElementName(element: PsiElement): String {
        return when (element) {
            is PsiNamedElement -> element.name ?: "未知"
            else -> element.text.take(50)
        }
    }

    /**
     * 获取使用上下文
     */
    private fun getUsageContext(document: com.intellij.openapi.editor.Document, lineNumber: Int, textRange: TextRange): String {
        return try {
            val lineStartOffset = document.getLineStartOffset(lineNumber)
            val lineEndOffset = document.getLineEndOffset(lineNumber)
            val lineText = document.getText(TextRange(lineStartOffset, lineEndOffset))
            
            val relativeStart = textRange.startOffset - lineStartOffset
            val relativeEnd = textRange.endOffset - lineStartOffset
            
            if (relativeStart >= 0 && relativeEnd <= lineText.length) {
                val before = lineText.substring(0, relativeStart)
                val match = lineText.substring(relativeStart, relativeEnd)
                val after = lineText.substring(relativeEnd)
                "$before**$match**$after"
            } else {
                lineText
            }
        } catch (e: Exception) {
            "无法获取上下文"
        }
    }

    /**
     * 确定使用类型
     */
    private fun determineUsageType(refElement: PsiElement, targetElement: PsiElement): String {
        return when {
            refElement.parent?.toString()?.contains("call", ignoreCase = true) == true -> "方法调用"
            refElement.parent?.toString()?.contains("new", ignoreCase = true) == true -> "实例化"
            refElement.parent?.toString()?.contains("import", ignoreCase = true) == true -> "导入"
            else -> "引用"
        }
    }

    /**
     * 从引用创建 Usage 对象
     */
    private fun createUsageFromReference(reference: PsiReference): com.intellij.usages.Usage {
        // 简化实现，返回一个基本的 Usage 对象
        return object : com.intellij.usages.Usage {
            override fun getPresentation(): com.intellij.usages.UsagePresentation {
                return object : com.intellij.usages.UsagePresentation {
                    override fun getIcon(): javax.swing.Icon? = null
                    override fun getText(): Array<com.intellij.usages.TextChunk> {
                        return arrayOf(com.intellij.usages.TextChunk(
                            com.intellij.usages.TextChunk.getRegularAttributes(),
                            reference.canonicalText
                        ))
                    }
                    override fun getPlainText(): String = reference.canonicalText
                    override fun getTooltipText(): String = reference.canonicalText
                }
            }

            override fun getLocation(): com.intellij.fileEditor.FileEditorLocation? = null
            override fun isValid(): Boolean = reference.element.isValid
            override fun isReadOnly(): Boolean = false
            override fun selectInEditor() {}
            override fun highlightInEditor() {}
            override fun navigate(requestFocus: Boolean) {}
            override fun canNavigate(): Boolean = true
            override fun canNavigateToSource(): Boolean = true
        }
    }
}
