package com.taobao.mc.aimi.find

import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.runBlocking

/**
 * 搜索工具类
 * 提供便捷的搜索方法和工具函数
 */
object SearchUtils {
    private val logger = LoggerManager.getLogger(SearchUtils::class.java)

    /**
     * 快速文本搜索
     */
    fun quickTextSearch(
        project: Project,
        query: String,
        caseSensitive: Boolean = false,
        maxResults: Int = 50
    ): SearchResults {
        val searchEngine = SearchEngine.getInstance(project)
        val config = SearchConfig(
            query = query,
            searchType = SearchType.TEXT,
            caseSensitive = caseSensitive,
            maxResults = maxResults
        )
        
        return runBlocking {
            searchEngine.search(config)
        }
    }

    /**
     * 快速文件名搜索
     */
    fun quickFileSearch(
        project: Project,
        fileName: String,
        maxResults: Int = 30
    ): SearchResults {
        val searchEngine = SearchEngine.getInstance(project)
        val config = SearchConfig(
            query = fileName,
            searchType = SearchType.FILE_NAME,
            maxResults = maxResults
        )
        
        return runBlocking {
            searchEngine.search(config)
        }
    }

    /**
     * 快速符号搜索
     */
    fun quickSymbolSearch(
        project: Project,
        symbolName: String,
        caseSensitive: Boolean = false,
        maxResults: Int = 40
    ): SearchResults {
        val searchEngine = SearchEngine.getInstance(project)
        val config = SearchConfig(
            query = symbolName,
            searchType = SearchType.SYMBOL,
            caseSensitive = caseSensitive,
            maxResults = maxResults
        )
        
        return runBlocking {
            searchEngine.search(config)
        }
    }

    /**
     * 快速引用搜索
     */
    fun quickReferenceSearch(
        project: Project,
        symbolName: String,
        maxResults: Int = 60
    ): SearchResults {
        val searchEngine = SearchEngine.getInstance(project)
        val config = SearchConfig(
            query = symbolName,
            searchType = SearchType.REFERENCE,
            maxResults = maxResults
        )
        
        return runBlocking {
            searchEngine.search(config)
        }
    }

    /**
     * 智能搜索 - 自动选择最佳搜索策略
     */
    fun smartSearch(
        project: Project,
        query: String,
        maxResults: Int = 100
    ): SearchResults {
        val searchEngine = SearchEngine.getInstance(project)
        
        return runBlocking {
            searchEngine.smartSearch(query)
        }
    }

    /**
     * 组合搜索 - 使用多种搜索方式
     */
    fun combinedSearch(
        project: Project,
        query: String,
        searchTypes: Set<SearchType> = setOf(SearchType.TEXT, SearchType.SYMBOL, SearchType.FILE_NAME, SearchType.INDEX, SearchType.REFERENCE),
        maxResults: Int = 150
    ): SearchResults {
        val searchEngine = SearchEngine.getInstance(project)
        
        return runBlocking {
            searchEngine.combinedSearch(
                query = query,
                searchTypes = searchTypes,
                maxResults = maxResults
            )
        }
    }

    /**
     * 在指定文件类型中搜索
     */
    fun searchInFileTypes(
        project: Project,
        query: String,
        fileTypes: Set<String>,
        searchType: SearchType = SearchType.TEXT,
        maxResults: Int = 80
    ): SearchResults {
        val searchEngine = SearchEngine.getInstance(project)
        val config = SearchConfig(
            query = query,
            searchType = searchType,
            fileTypes = fileTypes,
            maxResults = maxResults
        )
        
        return runBlocking {
            searchEngine.search(config)
        }
    }

    /**
     * 搜索 Java 文件
     */
    fun searchInJavaFiles(
        project: Project,
        query: String,
        searchType: SearchType = SearchType.TEXT,
        maxResults: Int = 80
    ): SearchResults {
        return searchInFileTypes(project, query, setOf("java"), searchType, maxResults)
    }

    /**
     * 搜索 Kotlin 文件
     */
    fun searchInKotlinFiles(
        project: Project,
        query: String,
        searchType: SearchType = SearchType.TEXT,
        maxResults: Int = 80
    ): SearchResults {
        return searchInFileTypes(project, query, setOf("kt"), searchType, maxResults)
    }

    /**
     * 搜索配置文件
     */
    fun searchInConfigFiles(
        project: Project,
        query: String,
        maxResults: Int = 50
    ): SearchResults {
        val configFileTypes = setOf("xml", "json", "yaml", "yml", "properties", "conf", "config")
        return searchInFileTypes(project, query, configFileTypes, SearchType.TEXT, maxResults)
    }

    /**
     * 搜索文档文件
     */
    fun searchInDocFiles(
        project: Project,
        query: String,
        maxResults: Int = 40
    ): SearchResults {
        val docFileTypes = setOf("md", "txt", "rst", "adoc", "html", "htm")
        return searchInFileTypes(project, query, docFileTypes, SearchType.TEXT, maxResults)
    }

    /**
     * 格式化搜索结果为可读字符串
     */
    fun formatSearchResults(results: SearchResults): String {
        val sb = StringBuilder()
        
        sb.appendLine("搜索结果:")
        sb.appendLine("查询: ${results.query}")
        sb.appendLine("类型: ${results.searchType}")
        sb.appendLine("总数: ${results.totalCount}")
        sb.appendLine("耗时: ${results.searchTime}ms")
        sb.appendLine("是否有更多: ${results.hasMore}")
        sb.appendLine()
        
        if (results.results.isNotEmpty()) {
            sb.appendLine("结果列表:")
            results.results.forEachIndexed { index, result ->
                sb.appendLine("${index + 1}. ${formatSearchResult(result)}")
            }
        } else {
            sb.appendLine("未找到匹配结果")
        }
        
        if (results.suggestions.isNotEmpty()) {
            sb.appendLine()
            sb.appendLine("建议:")
            results.suggestions.forEach { suggestion ->
                sb.appendLine("- $suggestion")
            }
        }
        
        if (results.errors.isNotEmpty()) {
            sb.appendLine()
            sb.appendLine("错误:")
            results.errors.forEach { error ->
                sb.appendLine("- $error")
            }
        }
        
        return sb.toString()
    }

    /**
     * 格式化单个搜索结果
     */
    fun formatSearchResult(result: SearchResult): String {
        val fileName = result.file?.name ?: "未知文件"
        val location = "${result.line}:${result.column}"
        val score = String.format("%.2f", result.relevanceScore)
        
        return when (result) {
            is TextSearchResult -> "文本: $fileName:$location - ${result.matchedText} (分数: $score)"
            is FileNameSearchResult -> "文件: ${result.fileName} - ${result.matchType} (分数: $score)"
            is PsiSearchResult -> "符号: $fileName:$location - ${result.elementType}:${result.elementName} (分数: $score)"
            is ReferenceSearchResult -> "引用: $fileName:$location - ${result.referenceType} (分数: $score)"
            is IndexSearchResult -> "索引: $fileName - ${result.indexType} (分数: $score)"
            is UsageSearchResult -> "使用: $fileName:$location - ${result.usageType} (分数: $score)"
            is PsiStructureSearchResult -> "PSI: $fileName:$location - ${result.elementType}:${result.elementName} (分数: $score)"
            is SymbolSearchResult ->  "符号: $fileName:$location - ${result.symbolType}:${result.symbolName} (分数: $score)"
        }
    }

    /**
     * 获取搜索统计信息的可读格式
     */
    fun formatSearchStatistics(stats: Map<SearchType, SearchStatistics>): String {
        val sb = StringBuilder()
        
        sb.appendLine("搜索统计信息:")
        sb.appendLine("=" * 50)
        
        stats.forEach { (type, stat) ->
            sb.appendLine("${type}:")
            sb.appendLine("  总搜索次数: ${stat.totalSearches}")
            sb.appendLine("  成功次数: ${stat.successCount}")
            sb.appendLine("  错误次数: ${stat.errorCount}")
            sb.appendLine("  成功率: ${String.format("%.2f%%", stat.successRate * 100)}")
            sb.appendLine("  平均耗时: ${String.format("%.2f ms", stat.averageDuration)}")
            sb.appendLine("  平均结果数: ${String.format("%.1f", stat.averageResults)}")
            sb.appendLine()
        }
        
        return sb.toString()
    }

    /**
     * 验证搜索配置
     */
    fun validateSearchConfig(config: SearchConfig): List<String> {
        val errors = mutableListOf<String>()
        
        if (config.query.isBlank()) {
            errors.add("搜索查询不能为空")
        }
        
        if (config.query.length > 1000) {
            errors.add("搜索查询过长（最大1000字符）")
        }
        
        if (config.maxResults <= 0) {
            errors.add("最大结果数必须大于0")
        }
        
        if (config.maxResults > 10000) {
            errors.add("最大结果数不能超过10000")
        }
        
        if (config.timeout <= 0) {
            errors.add("超时时间必须大于0")
        }
        
        if (config.useRegex) {
            try {
                Regex(config.query)
            } catch (e: Exception) {
                errors.add("无效的正则表达式: ${e.message}")
            }
        }
        
        return errors
    }

    /**
     * 创建默认搜索配置
     */
    fun createDefaultConfig(query: String, searchType: SearchType): SearchConfig {
        return SearchConfig(
            query = query,
            searchType = searchType,
            scope = SearchScope.PROJECT,
            caseSensitive = false,
            wholeWords = false,
            useRegex = false,
            includeComments = true,
            includeStrings = true,
            maxResults = 100,
            timeout = 30000L
        )
    }

    /**
     * 创建快速搜索配置
     */
    fun createQuickConfig(query: String, searchType: SearchType): SearchConfig {
        return SearchConfig(
            query = query,
            searchType = searchType,
            scope = SearchScope.PROJECT,
            caseSensitive = false,
            wholeWords = false,
            useRegex = false,
            includeComments = false,
            includeStrings = true,
            maxResults = 50,
            timeout = 10000L
        )
    }

    /**
     * 创建精确搜索配置
     */
    fun createPreciseConfig(query: String, searchType: SearchType): SearchConfig {
        return SearchConfig(
            query = query,
            searchType = searchType,
            scope = SearchScope.PROJECT,
            caseSensitive = true,
            wholeWords = true,
            useRegex = false,
            includeComments = true,
            includeStrings = true,
            maxResults = 200,
            timeout = 60000L
        )
    }

    /**
     * 字符串重复操作符
     */
    private operator fun String.times(n: Int): String {
        return this.repeat(n)
    }
}
