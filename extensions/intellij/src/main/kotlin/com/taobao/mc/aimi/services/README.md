# IntelliJ 文本搜索工具

这是一个基于 IntelliJ IDEA 平台 API 的综合文本搜索工具，提供了四种不同的搜索方式来满足各种搜索需求。

## 功能特性

### 1. 文本检索 (TextSearchProvider)
- 使用 IntelliJ 的 `FindManager` 和 `FindModel` 进行文本搜索
- 支持大小写敏感、全词匹配、正则表达式搜索
- 提供搜索结果的上下文信息
- 高效的文本匹配算法

### 2. Usage 检索 (UsageSearchProvider)
- 使用 `ReferencesSearch` 和 `UsageSearcher` 查找符号使用
- 支持类、方法、字段的使用查找
- 提供使用类型分类（方法调用、实例化、导入等）
- 智能的符号解析和引用追踪

### 3. 索引检索 (IndexSearchProvider)
- 使用 `FileBasedIndex` 进行快速搜索
- 支持文件名、单词、标识符、注释、字符串字面量索引
- 利用 IntelliJ 的预建索引提高搜索速度
- 支持模糊匹配和精确匹配

### 4. PSI 搜索 (PsiSearchProvider)
- 使用 PSI 树遍历进行结构化搜索
- 支持类、方法、字段、变量、注释、字符串字面量的搜索
- 提供语法感知的搜索结果
- 支持按文件类型过滤

## 使用方法

### 基本用法

```kotlin
// 获取搜索服务实例
val searchService = TextSearchService.getInstance(project)

// 创建搜索配置
val config = SearchConfig(
    query = "searchText",
    searchType = SearchType.TEXT,
    caseSensitive = false,
    wholeWords = true,
    maxResults = 100
)

// 执行搜索
val results = searchService.search(config)
```

### 异步搜索

```kotlin
val callback = object : SearchProgressCallback {
    override fun onProgress(current: Int, total: Int, message: String) {
        println("搜索进度: $current/$total - $message")
    }
    
    override fun onCompleted(results: SearchResults) {
        println("搜索完成: 找到 ${results.totalCount} 个结果")
    }
    
    override fun onError(error: Throwable) {
        println("搜索失败: ${error.message}")
    }
}

// 启动异步搜索
val job = searchService.searchAsync(config, callback)
```

### 组合搜索

```kotlin
// 同时使用多种搜索方式
val results = searchService.combinedSearch(
    query = "searchText",
    searchTypes = setOf(SearchType.TEXT, SearchType.USAGE, SearchType.PSI),
    caseSensitive = false,
    maxResults = 200
)
```

## 搜索配置选项

### SearchConfig 参数

- `query`: 搜索查询字符串
- `searchType`: 搜索类型 (TEXT, USAGE, INDEX, PSI)
- `caseSensitive`: 是否大小写敏感
- `wholeWords`: 是否全词匹配
- `useRegex`: 是否使用正则表达式
- `searchScope`: 搜索范围 (PROJECT, MODULE, DIRECTORY, FILE, SELECTION)
- `fileTypes`: 文件类型过滤 (如 setOf("java", "kt"))
- `maxResults`: 最大结果数量

### 搜索范围

- `PROJECT`: 整个项目
- `MODULE`: 当前模块
- `DIRECTORY`: 指定目录
- `FILE`: 当前文件
- `SELECTION`: 当前选择

## 搜索结果类型

### TextSearchResult
- 文本搜索的结果
- 包含匹配的文本、位置、上下文信息

### UsageSearchResult
- 符号使用的结果
- 包含使用类型、引用元素信息

### IndexSearchResult
- 索引搜索的结果
- 包含索引类型、键值信息

### PsiSearchResult
- PSI 搜索的结果
- 包含 PSI 元素类型、名称信息

## 示例代码

查看 `TextSearchExample.kt` 文件获取完整的使用示例，包括：

- 基本文本搜索
- Usage 搜索
- 索引搜索
- PSI 搜索
- 组合搜索
- 异步搜索
- 搜索历史管理

## 性能优化

1. **索引搜索**: 对于大型项目，优先使用索引搜索以获得更好的性能
2. **文件类型过滤**: 通过 `fileTypes` 参数限制搜索范围
3. **结果数量限制**: 使用 `maxResults` 参数避免过多结果影响性能
4. **异步搜索**: 对于耗时搜索，使用异步方式避免阻塞 UI

## 错误处理

所有搜索方法都包含适当的错误处理：

- 捕获并记录搜索过程中的异常
- 提供详细的错误信息
- 支持搜索取消和超时处理

## 扩展性

该搜索工具设计为可扩展的：

- 可以轻松添加新的搜索提供者
- 支持自定义搜索结果类型
- 提供插件化的搜索策略

## 测试

运行测试：

```bash
./gradlew test --tests "TextSearchServiceTest"
```

测试覆盖了所有主要功能和边界情况。

## 注意事项

1. 确保在 EDT (Event Dispatch Thread) 外执行搜索操作
2. 大型项目中的搜索可能需要较长时间，建议使用异步搜索
3. PSI 搜索需要文件已经被解析，对于新文件可能需要等待索引完成
4. 某些索引可能在 IntelliJ 的不同版本中有所差异

## 依赖

该工具依赖以下 IntelliJ Platform API：

- `com.intellij.find.*` - 文本搜索
- `com.intellij.psi.*` - PSI 操作
- `com.intellij.util.indexing.*` - 索引操作
- `com.intellij.find.usages.*` - Usage 搜索

确保在 `build.gradle.kts` 中包含了必要的平台插件依赖。
