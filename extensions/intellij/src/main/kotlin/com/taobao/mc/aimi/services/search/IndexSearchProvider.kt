package com.taobao.mc.aimi.services.search

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiManager
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.util.indexing.FileBasedIndex
import com.intellij.util.indexing.ID
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.*

/**
 * 索引搜索提供者
 * 使用 FileBasedIndex 和各种索引进行快速搜索
 */
class IndexSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(IndexSearchProvider::class.java)
    private val fileBasedIndex = FileBasedIndex.getInstance()
    private val psiManager = PsiManager.getInstance(project)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.INDEX
    }

    override fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        try {
            callback?.onProgress(0, 100, "开始索引搜索...")

            val scope = createSearchScope(config)
            
            // 1. 文件名索引搜索
            callback?.onProgress(10, 100, "搜索文件名索引...")
            val filenameResults = searchFilenameIndex(config.query, scope, config)
            results.addAll(filenameResults)

            // 2. 单词索引搜索
            callback?.onProgress(30, 100, "搜索单词索引...")
            val wordResults = searchWordIndex(config.query, scope, config)
            results.addAll(wordResults)

            // 3. 标识符索引搜索
            callback?.onProgress(50, 100, "搜索标识符索引...")
            val identifierResults = searchIdentifierIndex(config.query, scope, config)
            results.addAll(identifierResults)

            // 4. 注释索引搜索
            callback?.onProgress(70, 100, "搜索注释索引...")
            val commentResults = searchCommentIndex(config.query, scope, config)
            results.addAll(commentResults)

            // 5. 字符串字面量索引搜索
            callback?.onProgress(90, 100, "搜索字符串字面量索引...")
            val stringResults = searchStringLiteralIndex(config.query, scope, config)
            results.addAll(stringResults)

            val searchTime = System.currentTimeMillis() - startTime
            val searchResults = SearchResults(
                query = config.query,
                searchType = config.searchType,
                results = results.distinctBy { "${it.file?.path}:${it.line}:${it.column}" }.take(config.maxResults),
                totalCount = results.size,
                searchTime = searchTime,
                hasMore = results.size > config.maxResults
            )

            callback?.onProgress(100, 100, "索引搜索完成，找到 ${results.size} 个结果")
            callback?.onCompleted(searchResults)

            return searchResults

        } catch (e: Exception) {
            logger.error("索引搜索失败: ${e.message}", e)
            callback?.onError(e)
            throw e
        }
    }

    /**
     * 搜索文件名索引
     */
    private fun searchFilenameIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 精确匹配
                val exactFiles = FilenameIndex.getFilesByName(project, query, scope)
                for (file in exactFiles) {
                    results.add(createFileIndexResult(file, query, "文件名精确匹配"))
                }

                // 模糊匹配
                if (!config.caseSensitive) {
                    val allFilenames = FilenameIndex.getAllFilenames(project)
                    for (filename in allFilenames) {
                        if (filename.contains(query, ignoreCase = true) && filename != query) {
                            val files = FilenameIndex.getFilesByName(project, filename, scope)
                            for (file in files) {
                                results.add(createFileIndexResult(file, query, "文件名模糊匹配"))
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                logger.warn("搜索文件名索引时发生错误: ${e.message}")
            }
            
            results.take(20) // 限制文件名结果数量
        }
    }

    /**
     * 搜索单词索引
     */
    private fun searchWordIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 使用 FileBasedIndex 搜索单词
                val wordIndexId = ID.findByName<String, Void>("word")
                if (wordIndexId != null) {
                    val files = fileBasedIndex.getContainingFiles(wordIndexId, query, scope)
                    for (file in files.take(30)) { // 限制文件数量
                        results.add(createFileIndexResult(file, query, "单词索引匹配"))
                    }
                }
            } catch (e: Exception) {
                logger.debug("搜索单词索引时发生错误: ${e.message}")
            }
            
            results
        }
    }

    /**
     * 搜索标识符索引
     */
    private fun searchIdentifierIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 搜索标识符索引
                val identifierIndexId = ID.findByName<String, Void>("identifier")
                if (identifierIndexId != null) {
                    val files = fileBasedIndex.getContainingFiles(identifierIndexId, query, scope)
                    for (file in files.take(30)) {
                        results.add(createFileIndexResult(file, query, "标识符索引匹配"))
                    }
                }
            } catch (e: Exception) {
                logger.debug("搜索标识符索引时发生错误: ${e.message}")
            }
            
            results
        }
    }

    /**
     * 搜索注释索引
     */
    private fun searchCommentIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 搜索注释索引
                val commentIndexId = ID.findByName<String, Void>("comment")
                if (commentIndexId != null) {
                    val files = fileBasedIndex.getContainingFiles(commentIndexId, query, scope)
                    for (file in files.take(20)) {
                        results.add(createFileIndexResult(file, query, "注释索引匹配"))
                    }
                }
            } catch (e: Exception) {
                logger.debug("搜索注释索引时发生错误: ${e.message}")
            }
            
            results
        }
    }

    /**
     * 搜索字符串字面量索引
     */
    private fun searchStringLiteralIndex(query: String, scope: GlobalSearchScope, config: SearchConfig): List<IndexSearchResult> {
        return ReadAction.compute<List<IndexSearchResult>, RuntimeException> {
            val results = mutableListOf<IndexSearchResult>()
            
            try {
                // 搜索字符串字面量索引
                val stringIndexId = ID.findByName<String, Void>("string.literal")
                if (stringIndexId != null) {
                    val files = fileBasedIndex.getContainingFiles(stringIndexId, query, scope)
                    for (file in files.take(20)) {
                        results.add(createFileIndexResult(file, query, "字符串字面量索引匹配"))
                    }
                }
            } catch (e: Exception) {
                logger.debug("搜索字符串字面量索引时发生错误: ${e.message}")
            }
            
            results
        }
    }

    /**
     * 创建文件索引结果
     */
    private fun createFileIndexResult(file: VirtualFile, query: String, indexType: String): IndexSearchResult {
        return IndexSearchResult(
            file = file,
            line = 1,
            column = 1,
            text = file.name,
            context = "文件: ${file.path}",
            indexKey = query,
            indexValue = file.name,
            indexType = indexType
        )
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.searchScope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
            SearchScope.FILE -> GlobalSearchScope.projectScope(project)
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
        }
    }

    /**
     * 获取所有可用的索引ID
     */
    private fun getAllAvailableIndexes(): List<ID<*, *>> {
        return try {
            // 获取所有注册的索引
            val allIndexes = mutableListOf<ID<*, *>>()
            
            // 常见的索引ID
            val commonIndexNames = listOf(
                "word",
                "identifier", 
                "comment",
                "string.literal",
                "java.class.name",
                "java.method.name",
                "java.field.name",
                "kotlin.class.name",
                "kotlin.function.name",
                "kotlin.property.name"
            )
            
            for (indexName in commonIndexNames) {
                val indexId = ID.findByName<String, Void>(indexName)
                if (indexId != null) {
                    allIndexes.add(indexId)
                }
            }
            
            allIndexes
        } catch (e: Exception) {
            logger.debug("获取索引列表时发生错误: ${e.message}")
            emptyList()
        }
    }
}
