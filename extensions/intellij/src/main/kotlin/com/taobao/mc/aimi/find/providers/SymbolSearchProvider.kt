package com.taobao.mc.aimi.find.providers
//
// import com.intellij.openapi.application.ReadAction
// import com.intellij.openapi.project.Project
// import com.intellij.psi.*
// import com.intellij.psi.search.GlobalSearchScope
// import com.intellij.psi.search.PsiShortNamesCache
// import com.intellij.psi.util.PsiTreeUtil
// import com.taobao.mc.aimi.find.*
// import com.taobao.mc.aimi.logger.LoggerManager
// import kotlinx.coroutines.Dispatchers
// import kotlinx.coroutines.withContext
// import kotlin.jvm.java
// import kotlin.math.min
//
// /**
//  * 符号搜索提供者
//  * 使用 IntelliJ 的 PsiShortNamesCache 进行符号搜索
//  */
// class SymbolSearchProvider(private val project: Project) : SearchProvider {
//     private val logger = LoggerManager.getLogger(SymbolSearchProvider::class.java)
//
//     override fun canHandle(searchType: SearchType): Boolean {
//         return searchType == SearchType.SYMBOL
//     }
//
//     override fun getSupportedFeatures(): Set<SearchFeature> {
//         return setOf(
//             SearchFeature.CASE_SENSITIVE,
//             SearchFeature.FUZZY_MATCH,
//             SearchFeature.CONTEXT_AWARE,
//             SearchFeature.ASYNC,
//             SearchFeature.CANCELLABLE,
//             SearchFeature.PROGRESS_TRACKING,
//             SearchFeature.RESULT_RANKING
//         )
//     }
//
//     override suspend fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
//         val startTime = System.currentTimeMillis()
//         val results = mutableListOf<SearchResult>()
//
//         return withContext(Dispatchers.IO) {
//             try {
//                 callback?.onProgress(SearchProgress(0, 100, "开始符号搜索..."))
//
//                 val scope = createSearchScope(config)
//
//                 // 1. 搜索类
//                 callback?.onProgress(SearchProgress(10, 100, "搜索类定义..."))
//                 val classResults = searchClasses(config.query, scope, config)
//                 results.addAll(classResults)
//
//                 // 2. 搜索方法
//                 callback?.onProgress(SearchProgress(30, 100, "搜索方法定义..."))
//                 val methodResults = searchMethods(config.query, scope, config)
//                 results.addAll(methodResults)
//
//                 // 3. 搜索字段
//                 callback?.onProgress(SearchProgress(50, 100, "搜索字段定义..."))
//                 val fieldResults = searchFields(config.query, scope, config)
//                 results.addAll(fieldResults)
//
//                 // 4. 搜索变量
//                 callback?.onProgress(SearchProgress(70, 100, "搜索变量定义..."))
//                 val variableResults = searchVariables(config.query, scope, config)
//                 results.addAll(variableResults)
//
//                 // 5. 搜索其他符号
//                 callback?.onProgress(SearchProgress(85, 100, "搜索其他符号..."))
//                 val otherResults = searchOtherSymbols(config.query, scope, config)
//                 results.addAll(otherResults)
//
//                 val searchTime = System.currentTimeMillis() - startTime
//                 val uniqueResults = results
//                     .distinctBy { "${it.file?.path}:${it.line}:${it.column}" }
//                     .sortedByDescending { it.relevanceScore }
//                     .take(config.maxResults)
//
//                 val searchResults = SearchResults(
//                     query = config.query,
//                     searchType = config.searchType,
//                     results = uniqueResults,
//                     totalCount = uniqueResults.size,
//                     searchTime = searchTime,
//                     hasMore = results.size > config.maxResults
//                 )
//
//                 callback?.onProgress(SearchProgress(100, 100, "符号搜索完成，找到 ${uniqueResults.size} 个结果"))
//                 callback?.onCompleted(searchResults)
//
//                 searchResults
//
//             } catch (e: Exception) {
//                 logger.error("符号搜索失败: ${e.message}", e)
//                 callback?.onError(e)
//                 throw e
//             }
//         }
//     }
//
//     /**
//      * 搜索类定义
//      */
//     private fun searchClasses(query: String, scope: GlobalSearchScope, config: SearchConfig): List<SymbolSearchResult> {
//         return ReadAction.compute<List<SymbolSearchResult>, RuntimeException> {
//             val results = mutableListOf<SymbolSearchResult>()
//
//             try {
//                 val shortNamesCache = PsiShortNamesCache.getInstance(project)
//
//                 // 精确匹配
//                 val exactClasses = shortNamesCache.getClassesByName(query, scope)
//                 for (clazz in exactClasses) {
//                     results.add(createSymbolResult(clazz, query, "类", 2.0))
//                 }
//
//                 // 模糊匹配
//                 if (!config.caseSensitive) {
//                     val allClassNames = shortNamesCache.getAllClassNames()
//                     for (className in allClassNames.take(500)) { // 限制处理数量
//                         if (className.contains(query, ignoreCase = true) && className != query) {
//                             val classes = shortNamesCache.getClassesByName(className, scope)
//                             for (clazz in classes.take(3)) { // 限制每个类名的结果数量
//                                 val score = calculateSymbolScore(className, query)
//                                 results.add(createSymbolResult(clazz, query, "类", score))
//                             }
//                         }
//                     }
//                 }
//
//             } catch (e: Exception) {
//                 logger.warn("搜索类时发生错误: ${e.message}")
//             }
//
//             results.take(50) // 限制类搜索结果数量
//         }
//     }
//
//     /**
//      * 搜索方法定义
//      */
//     private fun searchMethods(query: String, scope: GlobalSearchScope, config: SearchConfig): List<SymbolSearchResult> {
//         return ReadAction.compute<List<SymbolSearchResult>, RuntimeException> {
//             val results = mutableListOf<SymbolSearchResult>()
//
//             try {
//                 val shortNamesCache = PsiShortNamesCache.getInstance(project)
//
//                 // 精确匹配
//                 val exactMethods = shortNamesCache.getMethodsByName(query, scope)
//                 for (method in exactMethods) {
//                     results.add(createSymbolResult(method, query, "方法", 2.0))
//                 }
//
//                 // 模糊匹配
//                 if (!config.caseSensitive) {
//                     val allMethodNames = shortNamesCache.getAllMethodNames()
//                     for (methodName in allMethodNames.take(500)) { // 限制处理数量
//                         if (methodName.contains(query, ignoreCase = true) && methodName != query) {
//                             val methods = shortNamesCache.getMethodsByName(methodName, scope)
//                             for (method in methods.take(3)) { // 限制每个方法名的结果数量
//                                 val score = calculateSymbolScore(methodName, query)
//                                 results.add(createSymbolResult(method, query, "方法", score))
//                             }
//                         }
//                     }
//                 }
//
//             } catch (e: Exception) {
//                 logger.warn("搜索方法时发生错误: ${e.message}")
//             }
//
//             results.take(100) // 限制方法搜索结果数量
//         }
//     }
//
//     /**
//      * 搜索字段定义
//      */
//     private fun searchFields(query: String, scope: GlobalSearchScope, config: SearchConfig): List<SymbolSearchResult> {
//         return ReadAction.compute<List<SymbolSearchResult>, RuntimeException> {
//             val results = mutableListOf<SymbolSearchResult>()
//
//             try {
//                 val shortNamesCache = PsiShortNamesCache.getInstance(project)
//
//                 // 精确匹配
//                 val exactFields = shortNamesCache.getFieldsByName(query, scope)
//                 for (field in exactFields) {
//                     results.add(createSymbolResult(field, query, "字段", 2.0))
//                 }
//
//                 // 模糊匹配
//                 if (!config.caseSensitive) {
//                     val allFieldNames = shortNamesCache.getAllFieldNames()
//                     for (fieldName in allFieldNames.take(500)) { // 限制处理数量
//                         if (fieldName.contains(query, ignoreCase = true) && fieldName != query) {
//                             val fields = shortNamesCache.getFieldsByName(fieldName, scope)
//                             for (field in fields.take(3)) { // 限制每个字段名的结果数量
//                                 val score = calculateSymbolScore(fieldName, query)
//                                 results.add(createSymbolResult(field, query, "字段", score))
//                             }
//                         }
//                     }
//                 }
//
//             } catch (e: Exception) {
//                 logger.warn("搜索字段时发生错误: ${e.message}")
//             }
//
//             results.take(80) // 限制字段搜索结果数量
//         }
//     }
//
//     /**
//      * 搜索变量定义
//      */
//     private fun searchVariables(query: String, scope: GlobalSearchScope, config: SearchConfig): List<SymbolSearchResult> {
//         return ReadAction.compute<List<SymbolSearchResult>, RuntimeException> {
//             val results = mutableListOf<SymbolSearchResult>()
//
//             try {
//                 // 通过文件遍历查找变量（简化实现）
//                 val files = getFilesToSearch(scope, config)
//
//                 for (file in files.take(50)) { // 限制文件数量
//                     val psiFile = PsiManager.getInstance(project).findFile(file) ?: continue
//
//                     val variables = PsiTreeUtil.findChildrenOfType(psiFile, PsiVariable::class.java)
//                     for (variable in variables) {
//                         val variableName = variable.name ?: continue
//                         if (matchesQuery(variableName, query, config.caseSensitive)) {
//                             val score = calculateSymbolScore(variableName, query)
//                             results.add(createSymbolResult(variable, query, "变量", score))
//                         }
//                     }
//                 }
//
//             } catch (e: Exception) {
//                 logger.warn("搜索变量时发生错误: ${e.message}")
//             }
//
//             results.take(60) // 限制变量搜索结果数量
//         }
//     }
//
//     /**
//      * 搜索其他符号
//      */
//     private fun searchOtherSymbols(query: String, scope: GlobalSearchScope, config: SearchConfig): List<SymbolSearchResult> {
//         return ReadAction.compute<List<SymbolSearchResult>, RuntimeException> {
//             val results = mutableListOf<SymbolSearchResult>()
//
//             try {
//                 // 搜索接口、枚举等其他符号
//                 val files = getFilesToSearch(scope, config)
//
//                 for (file in files.take(30)) { // 限制文件数量
//                     val psiFile = PsiManager.getInstance(project).findFile(file) ?: continue
//
//                     // 查找所有命名元素
//                     val namedElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiNamedElement::class.java)
//                     for (element in namedElements) {
//                         val elementName = element.name ?: continue
//                         if (matchesQuery(elementName, query, config.caseSensitive)) {
//                             val elementType = determineElementType(element)
//                             if (elementType !in setOf("类", "方法", "字段", "变量")) {
//                                 val score = calculateSymbolScore(elementName, query)
//                                 results.add(createSymbolResult(element, query, elementType, score))
//                             }
//                         }
//                     }
//                 }
//
//             } catch (e: Exception) {
//                 logger.warn("搜索其他符号时发生错误: ${e.message}")
//             }
//
//             results.take(40) // 限制其他符号搜索结果数量
//         }
//     }
//
//     /**
//      * 创建符号搜索结果
//      */
//     private fun createSymbolResult(element: PsiElement, query: String, symbolType: String, score: Double): SymbolSearchResult {
//         val containingFile = element.containingFile?.virtualFile
//         val document = PsiDocumentManager.getInstance(project).getDocument(element.containingFile!!)
//
//         val lineNumber = if (document != null) {
//             document.getLineNumber(element.textOffset)
//         } else {
//             0
//         }
//
//         val columnNumber = if (document != null) {
//             element.textOffset - document.getLineStartOffset(lineNumber)
//         } else {
//             0
//         }
//
//         val symbolName = when (element) {
//             is PsiNamedElement -> element.name ?: "未知"
//             else -> element.text.take(50)
//         }
//
//         val containingClass = PsiTreeUtil.getParentOfType(element, PsiClass::class.java)?.name
//         val context = getSymbolContext(element, document, lineNumber)
//
//         return SymbolSearchResult(
//             file = containingFile,
//             line = lineNumber + 1,
//             column = columnNumber + 1,
//             text = symbolName,
//             context = context,
//             relevanceScore = score,
//             symbolName = symbolName,
//             symbolType = symbolType,
//             psiElement = element,
//             containingClass = containingClass
//         )
//     }
//
//     /**
//      * 获取符号上下文
//      */
//     private fun getSymbolContext(element: PsiElement, document: com.intellij.openapi.editor.Document?, lineNumber: Int): String {
//         return try {
//             if (document != null) {
//                 val lineStartOffset = document.getLineStartOffset(lineNumber)
//                 val lineEndOffset = document.getLineEndOffset(lineNumber)
//                 val lineText = document.getText(com.intellij.openapi.util.TextRange(lineStartOffset, lineEndOffset))
//                 lineText.trim()
//             } else {
//                 element.text.take(100)
//             }
//         } catch (e: Exception) {
//             "无法获取上下文"
//         }
//     }
//
//     /**
//      * 确定元素类型
//      */
//     private fun determineElementType(element: PsiElement): String {
//         return when (element) {
//             is PsiClass -> "类"
//             is PsiMethod -> "方法"
//             is PsiField -> "字段"
//             is PsiVariable -> "变量"
//             is PsiParameter -> "参数"
//             else -> element.javaClass.simpleName.removePrefix("Psi").removeSuffix("Impl")
//         }
//     }
//
//     /**
//      * 检查是否匹配查询
//      */
//     private fun matchesQuery(text: String, query: String, caseSensitive: Boolean): Boolean {
//         return if (caseSensitive) {
//             text.contains(query)
//         } else {
//             text.contains(query, ignoreCase = true)
//         }
//     }
//
//     /**
//      * 计算符号匹配分数
//      */
//     private fun calculateSymbolScore(symbolName: String, query: String): Double {
//         var score = 1.0
//
//         // 精确匹配
//         if (symbolName.equals(query, ignoreCase = true)) {
//             score = 2.0
//         }
//         // 前缀匹配
//         else if (symbolName.startsWith(query, ignoreCase = true)) {
//             score = 1.8
//         }
//         // 包含匹配
//         else if (symbolName.contains(query, ignoreCase = true)) {
//             score = 1.5
//         }
//
//         // 长度影响
//         val lengthRatio = query.length.toDouble() / symbolName.length
//         score += lengthRatio * 0.3
//
//         return min(score, 2.0)
//     }
//
//     /**
//      * 获取需要搜索的文件列表
//      */
//     private fun getFilesToSearch(scope: GlobalSearchScope, config: SearchConfig): List<com.intellij.openapi.vfs.VirtualFile> {
//         return ReadAction.compute<List<com.intellij.openapi.vfs.VirtualFile>, RuntimeException> {
//             val allFiles = mutableListOf<com.intellij.openapi.vfs.VirtualFile>()
//
//             try {
//                 val allFileNames = com.intellij.psi.search.FilenameIndex.getAllFilenames(project)
//
//                 for (fileName in allFileNames.take(200)) { // 限制文件名数量
//                     val files = com.intellij.psi.search.FilenameIndex.getFilesByName(project, fileName, scope)
//                     for (file in files) {
//                         if (file.isValid && !file.virtualFile.isDirectory) {
//                             val virtualFile = file.virtualFile
//
//                             // 文件类型过滤
//                             if (config.fileTypes.isEmpty() || config.fileTypes.contains(virtualFile.extension ?: "")) {
//                                 allFiles.add(virtualFile)
//                             }
//                         }
//                     }
//                 }
//
//             } catch (e: Exception) {
//                 logger.warn("获取文件列表时发生错误: ${e.message}")
//             }
//
//             allFiles.take(100) // 限制总文件数量
//         }
//     }
//
//     /**
//      * 创建搜索范围
//      */
//     private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
//         return when (config.scope) {
//             SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
//             SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
//             SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
//             SearchScope.FILE -> GlobalSearchScope.projectScope(project)
//             SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
//             SearchScope.OPEN_FILES -> GlobalSearchScope.projectScope(project)
//             SearchScope.CUSTOM -> GlobalSearchScope.projectScope(project)
//         }
//     }
// }
