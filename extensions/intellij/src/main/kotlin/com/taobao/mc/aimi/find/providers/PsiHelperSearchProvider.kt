package com.taobao.mc.aimi.find.providers

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.psi.*
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.search.PsiSearchHelper
import com.intellij.psi.search.UsageSearchContext
import com.intellij.util.Processor
import com.taobao.mc.aimi.find.*
import com.taobao.mc.aimi.find.language.LanguageElementTypes
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.min

/**
 * 基于 PsiSearchHelper 的搜索提供者
 * 使用 IntelliJ 的 PsiSearchHelper 进行高效的 PSI 搜索
 */
class PsiHelperSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(PsiHelperSearchProvider::class.java)
    private val psiSearchHelper = PsiSearchHelper.getInstance(project)
    private val psiManager = PsiManager.getInstance(project)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.PSI_STRUCTURE
    }

    override fun getSupportedFeatures(): Set<SearchFeature> {
        return setOf(
            SearchFeature.CASE_SENSITIVE,
            SearchFeature.CONTEXT_AWARE,
            SearchFeature.ASYNC,
            SearchFeature.CANCELLABLE,
            SearchFeature.PROGRESS_TRACKING,
            SearchFeature.RESULT_RANKING
        )
    }

    override suspend fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        return withContext(Dispatchers.IO) {
            try {
                callback?.onProgress(SearchProgress(0, 100, "开始 PsiHelper 搜索..."))

                val scope = createSearchScope(config)
                
                // 1. 在代码中搜索
                callback?.onProgress(SearchProgress(10, 100, "搜索代码中的引用..."))
                val codeResults = searchInCode(config.query, scope, config)
                results.addAll(codeResults)

                // 2. 在注释中搜索
                if (config.includeComments) {
                    callback?.onProgress(SearchProgress(30, 100, "搜索注释中的引用..."))
                    val commentResults = searchInComments(config.query, scope, config)
                    results.addAll(commentResults)
                }

                // 3. 在字符串字面量中搜索
                if (config.includeStrings) {
                    callback?.onProgress(SearchProgress(50, 100, "搜索字符串字面量中的引用..."))
                    val stringResults = searchInStrings(config.query, scope, config)
                    results.addAll(stringResults)
                }

                // 4. 搜索标识符
                callback?.onProgress(SearchProgress(70, 100, "搜索标识符..."))
                val identifierResults = searchIdentifiers(config.query, scope, config)
                results.addAll(identifierResults)

                // 5. 搜索文本出现
                callback?.onProgress(SearchProgress(85, 100, "搜索文本出现..."))
                val textResults = searchTextOccurrences(config.query, scope, config)
                results.addAll(textResults)

                val searchTime = System.currentTimeMillis() - startTime
                val uniqueResults = results
                    .distinctBy { "${it.file?.path}:${it.line}:${it.column}" }
                    .sortedByDescending { it.relevanceScore }
                    .take(config.maxResults)

                val searchResults = SearchResults(
                    query = config.query,
                    searchType = config.searchType,
                    results = uniqueResults,
                    totalCount = uniqueResults.size,
                    searchTime = searchTime,
                    hasMore = results.size > config.maxResults
                )

                callback?.onProgress(SearchProgress(100, 100, "PsiHelper 搜索完成，找到 ${uniqueResults.size} 个结果"))
                callback?.onCompleted(searchResults)

                searchResults

            } catch (e: Exception) {
                logger.error("PsiHelper 搜索失败: ${e.message}", e)
                callback?.onError(e)
                throw e
            }
        }
    }

    /**
     * 在代码中搜索
     */
    private fun searchInCode(query: String, scope: GlobalSearchScope, config: SearchConfig): List<PsiStructureSearchResult> {
        return ReadAction.compute<List<PsiStructureSearchResult>, RuntimeException> {
            val results = mutableListOf<PsiStructureSearchResult>()
            
            try {
                val processor = createSearchProcessor(results, query, config, "代码引用")
                
                psiSearchHelper.processElementsWithWord(
                    processor,
                    scope,
                    query,
                    UsageSearchContext.IN_CODE,
                    config.caseSensitive
                )
                
            } catch (e: Exception) {
                logger.warn("在代码中搜索时发生错误: ${e.message}")
            }
            
            results.take(100) // 限制代码搜索结果数量
        }
    }

    /**
     * 在注释中搜索
     */
    private fun searchInComments(query: String, scope: GlobalSearchScope, config: SearchConfig): List<PsiStructureSearchResult> {
        return ReadAction.compute<List<PsiStructureSearchResult>, RuntimeException> {
            val results = mutableListOf<PsiStructureSearchResult>()
            
            try {
                val processor = createSearchProcessor(results, query, config, "注释")
                
                psiSearchHelper.processElementsWithWord(
                    processor,
                    scope,
                    query,
                    UsageSearchContext.IN_COMMENTS,
                    config.caseSensitive
                )
                
            } catch (e: Exception) {
                logger.warn("在注释中搜索时发生错误: ${e.message}")
            }
            
            results.take(50) // 限制注释搜索结果数量
        }
    }

    /**
     * 在字符串字面量中搜索
     */
    private fun searchInStrings(query: String, scope: GlobalSearchScope, config: SearchConfig): List<PsiStructureSearchResult> {
        return ReadAction.compute<List<PsiStructureSearchResult>, RuntimeException> {
            val results = mutableListOf<PsiStructureSearchResult>()
            
            try {
                val processor = createSearchProcessor(results, query, config, "字符串字面量")
                
                psiSearchHelper.processElementsWithWord(
                    processor,
                    scope,
                    query,
                    UsageSearchContext.IN_STRINGS,
                    config.caseSensitive
                )
                
            } catch (e: Exception) {
                logger.warn("在字符串字面量中搜索时发生错误: ${e.message}")
            }
            
            results.take(50) // 限制字符串搜索结果数量
        }
    }

    /**
     * 搜索标识符
     */
    private fun searchIdentifiers(query: String, scope: GlobalSearchScope, config: SearchConfig): List<PsiStructureSearchResult> {
        return ReadAction.compute<List<PsiStructureSearchResult>, RuntimeException> {
            val results = mutableListOf<PsiStructureSearchResult>()
            
            try {
                val processor = createSearchProcessor(results, query, config, "标识符")
                
                // 搜索所有上下文中的标识符
                val allContexts = UsageSearchContext.IN_CODE or 
                                 UsageSearchContext.IN_COMMENTS or 
                                 UsageSearchContext.IN_STRINGS or
                                 UsageSearchContext.IN_PLAIN_TEXT
                
                psiSearchHelper.processElementsWithWord(
                    processor,
                    scope,
                    query,
                    allContexts,
                    config.caseSensitive
                )
                
            } catch (e: Exception) {
                logger.warn("搜索标识符时发生错误: ${e.message}")
            }
            
            results.take(80) // 限制标识符搜索结果数量
        }
    }

    /**
     * 搜索文本出现
     */
    private fun searchTextOccurrences(query: String, scope: GlobalSearchScope, config: SearchConfig): List<PsiStructureSearchResult> {
        return ReadAction.compute<List<PsiStructureSearchResult>, RuntimeException> {
            val results = mutableListOf<PsiStructureSearchResult>()
            
            try {
                val processor = createSearchProcessor(results, query, config, "文本出现")
                
                psiSearchHelper.processElementsWithWord(
                    processor,
                    scope,
                    query,
                    UsageSearchContext.ANY,
                    config.caseSensitive
                )
                
            } catch (e: Exception) {
                logger.warn("搜索文本出现时发生错误: ${e.message}")
            }
            
            results.take(60) // 限制文本搜索结果数量
        }
    }

    /**
     * 创建搜索处理器
     */
    private fun createSearchProcessor(
        results: MutableList<PsiStructureSearchResult>,
        query: String,
        config: SearchConfig,
        searchType: String
    ): Processor<PsiReference> {
        return Processor { reference ->
            try {
                val element = reference.element
                val containingFile = element.containingFile?.virtualFile
                
                if (containingFile != null && shouldIncludeFile(containingFile, config)) {
                    val document = PsiDocumentManager.getInstance(project).getDocument(element.containingFile!!)
                    
                    if (document != null) {
                        val textRange = reference.rangeInElement.shiftRight(element.textRange.startOffset)
                        val lineNumber = document.getLineNumber(textRange.startOffset)
                        val columnNumber = textRange.startOffset - document.getLineStartOffset(lineNumber)
                        
                        val context = getElementContext(element, document, lineNumber)
                        val elementType = LanguageElementTypes.determineElementType(element, containingFile.extension)
                        val displayName = LanguageElementTypes.getElementDisplayName(elementType, containingFile.extension)
                        val relevanceScore = calculateRelevanceScore(reference, query, searchType)
                        
                        val result = PsiStructureSearchResult(
                            file = containingFile,
                            line = lineNumber + 1,
                            column = columnNumber + 1,
                            text = reference.canonicalText,
                            context = context,
                            relevanceScore = relevanceScore,
                            psiElement = element,
                            elementType = displayName,
                            elementName = (element as? PsiNamedElement)?.name,
                            parentElement = element.parent
                        )
                        
                        results.add(result)
                    }
                }
                
                // 继续处理（返回 true）
                results.size < config.maxResults
            } catch (e: Exception) {
                logger.debug("处理搜索结果时发生错误: ${e.message}")
                true // 继续处理
            }
        }
    }

    /**
     * 检查是否应该包含文件
     */
    private fun shouldIncludeFile(file: com.intellij.openapi.vfs.VirtualFile, config: SearchConfig): Boolean {
        // 文件类型过滤
        if (config.fileTypes.isNotEmpty() && !config.fileTypes.contains(file.extension ?: "")) {
            return false
        }
        
        // 排除模式过滤
        return config.excludePatterns.none { pattern ->
            file.path.contains(pattern, ignoreCase = true)
        }
    }

    /**
     * 获取元素上下文
     */
    private fun getElementContext(element: PsiElement, document: com.intellij.openapi.editor.Document, lineNumber: Int): String {
        return try {
            val lineStartOffset = document.getLineStartOffset(lineNumber)
            val lineEndOffset = document.getLineEndOffset(lineNumber)
            val lineText = document.getText(com.intellij.openapi.util.TextRange(lineStartOffset, lineEndOffset))
            
            val relativeStart = element.textOffset - lineStartOffset
            val relativeEnd = element.textRange.endOffset - lineStartOffset
            
            if (relativeStart >= 0 && relativeEnd <= lineText.length) {
                val before = lineText.substring(0, relativeStart)
                val match = lineText.substring(relativeStart, relativeEnd)
                val after = lineText.substring(relativeEnd)
                "$before**$match**$after"
            } else {
                lineText.trim()
            }
        } catch (e: Exception) {
            "无法获取上下文"
        }
    }

    /**
     * 计算相关性分数
     */
    private fun calculateRelevanceScore(reference: PsiReference, query: String, searchType: String): Double {
        var score = 1.0
        
        try {
            // 根据搜索类型调整分数
            when (searchType) {
                "代码引用" -> score += 0.3
                "标识符" -> score += 0.2
                "注释" -> score += 0.1
                "字符串字面量" -> score += 0.1
                "文本出现" -> score += 0.05
            }
            
            // 引用文本匹配度
            val refText = reference.canonicalText
            if (refText.equals(query, ignoreCase = true)) {
                score += 0.5
            } else if (refText.contains(query, ignoreCase = true)) {
                score += 0.2
            }
            
            // 引用长度影响
            if (refText.length > 1) {
                score += 0.1
            }
            
        } catch (e: Exception) {
            logger.debug("计算相关性分数时发生错误: ${e.message}")
        }
        
        return min(score, 2.0)
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.scope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
            SearchScope.FILE -> GlobalSearchScope.projectScope(project)
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
            SearchScope.OPEN_FILES -> GlobalSearchScope.projectScope(project)
            SearchScope.CUSTOM -> GlobalSearchScope.projectScope(project)
        }
    }
}
