package com.taobao.mc.aimi.services.search

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.*
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.util.PsiTreeUtil
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.*

/**
 * PSI 搜索提供者
 * 使用 PSI 树遍历进行结构化搜索
 */
class PsiSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(PsiSearchProvider::class.java)
    private val psiManager = PsiManager.getInstance(project)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.PSI
    }

    override fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        try {
            callback?.onProgress(0, 100, "开始 PSI 搜索...")

            val scope = createSearchScope(config)
            val files = getFilesToSearch(scope, config.fileTypes)
            
            callback?.onProgress(10, 100, "找到 ${files.size} 个文件需要搜索")

            var processedFiles = 0
            val totalFiles = files.size

            for (file in files) {
                if (results.size >= config.maxResults) {
                    break
                }

                try {
                    val fileResults = searchInPsiFile(file, config)
                    results.addAll(fileResults)
                    
                    processedFiles++
                    val progress = 10 + (processedFiles * 80 / totalFiles)
                    callback?.onProgress(progress, 100, "搜索文件: ${file.name} (${processedFiles}/${totalFiles})")
                    
                } catch (e: Exception) {
                    logger.warn("搜索 PSI 文件 ${file.path} 时发生错误: ${e.message}")
                }
            }

            val searchTime = System.currentTimeMillis() - startTime
            val searchResults = SearchResults(
                query = config.query,
                searchType = config.searchType,
                results = results.take(config.maxResults),
                totalCount = results.size,
                searchTime = searchTime,
                hasMore = results.size > config.maxResults
            )

            callback?.onProgress(100, 100, "PSI 搜索完成，找到 ${results.size} 个结果")
            callback?.onCompleted(searchResults)

            return searchResults

        } catch (e: Exception) {
            logger.error("PSI 搜索失败: ${e.message}", e)
            callback?.onError(e)
            throw e
        }
    }

    /**
     * 在 PSI 文件中搜索
     */
    private fun searchInPsiFile(file: VirtualFile, config: SearchConfig): List<PsiSearchResult> {
        return ReadAction.compute<List<PsiSearchResult>, RuntimeException> {
            val results = mutableListOf<PsiSearchResult>()
            
            try {
                val psiFile = psiManager.findFile(file) ?: return@compute results
                
                // 搜索不同类型的 PSI 元素
                results.addAll(searchClasses(psiFile, config))
                results.addAll(searchMethods(psiFile, config))
                results.addAll(searchFields(psiFile, config))
                results.addAll(searchVariables(psiFile, config))
                results.addAll(searchComments(psiFile, config))
                results.addAll(searchStringLiterals(psiFile, config))
                
            } catch (e: Exception) {
                logger.warn("在 PSI 文件 ${file.path} 中搜索时发生错误: ${e.message}")
            }
            
            results.take(50) // 限制单个文件的结果数量
        }
    }

    /**
     * 搜索类定义
     */
    private fun searchClasses(psiFile: PsiFile, config: SearchConfig): List<PsiSearchResult> {
        val results = mutableListOf<PsiSearchResult>()

        // 简化实现：查找所有命名元素
        val namedElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiNamedElement::class.java)
        for (element in namedElements) {
            val elementName = element.name ?: continue
            if (element.toString().contains("class", ignoreCase = true) &&
                matchesQuery(elementName, config.query, config.caseSensitive)) {
                results.add(createPsiResult(element, elementName, "类定义", config))
            }
        }

        return results
    }

    /**
     * 搜索方法定义
     */
    private fun searchMethods(psiFile: PsiFile, config: SearchConfig): List<PsiSearchResult> {
        val results = mutableListOf<PsiSearchResult>()

        val namedElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiNamedElement::class.java)
        for (element in namedElements) {
            val elementName = element.name ?: continue
            if ((element.toString().contains("method", ignoreCase = true) ||
                 element.toString().contains("function", ignoreCase = true)) &&
                matchesQuery(elementName, config.query, config.caseSensitive)) {
                results.add(createPsiResult(element, elementName, "方法定义", config))
            }
        }

        return results
    }

    /**
     * 搜索字段定义
     */
    private fun searchFields(psiFile: PsiFile, config: SearchConfig): List<PsiSearchResult> {
        val results = mutableListOf<PsiSearchResult>()

        val namedElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiNamedElement::class.java)
        for (element in namedElements) {
            val elementName = element.name ?: continue
            if ((element.toString().contains("field", ignoreCase = true) ||
                 element.toString().contains("property", ignoreCase = true)) &&
                matchesQuery(elementName, config.query, config.caseSensitive)) {
                results.add(createPsiResult(element, elementName, "字段定义", config))
            }
        }

        return results
    }

    /**
     * 搜索变量定义
     */
    private fun searchVariables(psiFile: PsiFile, config: SearchConfig): List<PsiSearchResult> {
        val results = mutableListOf<PsiSearchResult>()

        val namedElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiNamedElement::class.java)
        for (element in namedElements) {
            val elementName = element.name ?: continue
            if (element.toString().contains("variable", ignoreCase = true) &&
                matchesQuery(elementName, config.query, config.caseSensitive)) {
                results.add(createPsiResult(element, elementName, "变量定义", config))
            }
        }

        return results
    }

    /**
     * 搜索注释
     */
    private fun searchComments(psiFile: PsiFile, config: SearchConfig): List<PsiSearchResult> {
        val results = mutableListOf<PsiSearchResult>()
        
        val comments = PsiTreeUtil.findChildrenOfType(psiFile, PsiComment::class.java)
        for (comment in comments) {
            val commentText = comment.text
            if (matchesQuery(commentText, config.query, config.caseSensitive)) {
                results.add(createPsiResult(comment, commentText.take(50), "注释", config))
            }
        }
        
        return results
    }

    /**
     * 搜索字符串字面量
     */
    private fun searchStringLiterals(psiFile: PsiFile, config: SearchConfig): List<PsiSearchResult> {
        val results = mutableListOf<PsiSearchResult>()

        // 简化实现：查找包含字符串的元素
        val allElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiElement::class.java)
        for (element in allElements) {
            val text = element.text
            if (text.startsWith("\"") && text.endsWith("\"") && text.length > 2) {
                val stringValue = text.substring(1, text.length - 1)
                if (matchesQuery(stringValue, config.query, config.caseSensitive)) {
                    results.add(createPsiResult(element, stringValue.take(50), "字符串字面量", config))
                }
            }
        }

        return results
    }

    /**
     * 创建 PSI 搜索结果
     */
    private fun createPsiResult(element: PsiElement, elementName: String, elementType: String, config: SearchConfig): PsiSearchResult {
        val containingFile = element.containingFile
        val document = PsiDocumentManager.getInstance(project).getDocument(containingFile)
        
        val lineNumber = if (document != null) {
            document.getLineNumber(element.textOffset)
        } else {
            0
        }
        
        val columnNumber = if (document != null) {
            element.textOffset - document.getLineStartOffset(lineNumber)
        } else {
            0
        }
        
        val context = getElementContext(element, document, lineNumber)
        
        return PsiSearchResult(
            file = containingFile.virtualFile,
            line = lineNumber + 1,
            column = columnNumber + 1,
            text = element.text.take(100),
            context = context,
            psiElement = element,
            elementType = elementType,
            elementName = elementName
        )
    }

    /**
     * 获取元素上下文
     */
    private fun getElementContext(element: PsiElement, document: com.intellij.openapi.editor.Document?, lineNumber: Int): String {
        return try {
            if (document != null) {
                val lineStartOffset = document.getLineStartOffset(lineNumber)
                val lineEndOffset = document.getLineEndOffset(lineNumber)
                val lineText = document.getText(com.intellij.openapi.util.TextRange(lineStartOffset, lineEndOffset))
                
                val relativeStart = element.textOffset - lineStartOffset
                val relativeEnd = element.textRange.endOffset - lineStartOffset
                
                if (relativeStart >= 0 && relativeEnd <= lineText.length) {
                    val before = lineText.substring(0, relativeStart)
                    val match = lineText.substring(relativeStart, relativeEnd)
                    val after = lineText.substring(relativeEnd)
                    "$before**$match**$after"
                } else {
                    lineText
                }
            } else {
                element.text.take(100)
            }
        } catch (e: Exception) {
            "无法获取上下文"
        }
    }

    /**
     * 检查是否匹配查询
     */
    private fun matchesQuery(text: String, query: String, caseSensitive: Boolean): Boolean {
        return if (caseSensitive) {
            text.contains(query)
        } else {
            text.contains(query, ignoreCase = true)
        }
    }

    /**
     * 获取需要搜索的文件列表
     */
    private fun getFilesToSearch(scope: GlobalSearchScope, fileTypes: Set<String>): List<VirtualFile> {
        return ReadAction.compute<List<VirtualFile>, RuntimeException> {
            val allFiles = mutableListOf<VirtualFile>()
            
            val allFileNames = FilenameIndex.getAllFilenames(project)
            
            for (fileName in allFileNames) {
                val files = FilenameIndex.getFilesByName(project, fileName, scope)
                for (file in files) {
                    if (file.isValid && !file.isDirectory) {
                        if (fileTypes.isEmpty() || fileTypes.contains(file.virtualFile.extension ?: "")) {
                            allFiles.add(file.virtualFile)
                        }
                    }
                }
            }
            
            allFiles
        }
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.searchScope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
            SearchScope.FILE -> GlobalSearchScope.projectScope(project)
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
        }
    }
}
