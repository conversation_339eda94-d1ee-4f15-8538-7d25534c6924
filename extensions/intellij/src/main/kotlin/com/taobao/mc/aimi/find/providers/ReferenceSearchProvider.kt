package com.taobao.mc.aimi.find.providers

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiNamedElement
import com.intellij.psi.PsiReference
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.search.searches.ReferencesSearch
import com.taobao.mc.aimi.find.*
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.min

/**
 * 引用搜索提供者
 * 使用 IntelliJ 的 ReferencesSearch 进行引用搜索
 */
class ReferenceSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(ReferenceSearchProvider::class.java)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.REFERENCE
    }

    override fun getSupportedFeatures(): Set<SearchFeature> {
        return setOf(
            SearchFeature.CONTEXT_AWARE,
            SearchFeature.ASYNC,
            SearchFeature.CANCELLABLE,
            SearchFeature.PROGRESS_TRACKING,
            SearchFeature.RESULT_RANKING
        )
    }

    override suspend fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        return withContext(Dispatchers.IO) {
            try {
                callback?.onProgress(SearchProgress(0, 100, "开始引用搜索..."))

                // 1. 查找目标元素
                callback?.onProgress(SearchProgress(10, 100, "查找目标元素..."))
                val targetElements = findTargetElements(config.query, config)
                
                if (targetElements.isEmpty()) {
                    val searchResults = SearchResults(
                        query = config.query,
                        searchType = config.searchType,
                        results = emptyList(),
                        totalCount = 0,
                        searchTime = System.currentTimeMillis() - startTime,
                        suggestions = listOf("未找到匹配的符号，请检查拼写或尝试部分匹配")
                    )
                    callback?.onCompleted(searchResults)
                    return@withContext searchResults
                }

                callback?.onProgress(SearchProgress(20, 100, "找到 ${targetElements.size} 个目标元素"))

                // 2. 搜索每个目标元素的引用
                var processedElements = 0
                val totalElements = targetElements.size

                for (element in targetElements) {
                    if (results.size >= config.maxResults) {
                        break
                    }

                    try {
                        val elementReferences = findReferencesForElement(element, config)
                        results.addAll(elementReferences)
                        
                        processedElements++
                        val progress = 20 + (processedElements * 70 / totalElements)
                        callback?.onProgress(SearchProgress(
                            processedElements,
                            totalElements,
                            "搜索元素引用: ${getElementName(element)} (${processedElements}/${totalElements})",
                            progress
                        ))
                        
                    } catch (e: Exception) {
                        logger.warn("搜索元素 ${getElementName(element)} 的引用时发生错误: ${e.message}")
                    }
                }

                val searchTime = System.currentTimeMillis() - startTime
                val uniqueResults = results
                    .distinctBy { "${it.file?.path}:${it.line}:${it.column}" }
                    .sortedByDescending { it.relevanceScore }
                    .take(config.maxResults)

                val searchResults = SearchResults(
                    query = config.query,
                    searchType = config.searchType,
                    results = uniqueResults,
                    totalCount = uniqueResults.size,
                    searchTime = searchTime,
                    hasMore = results.size > config.maxResults
                )

                callback?.onProgress(SearchProgress(100, 100, "引用搜索完成，找到 ${uniqueResults.size} 个引用"))
                callback?.onCompleted(searchResults)

                searchResults

            } catch (e: Exception) {
                logger.error("引用搜索失败: ${e.message}", e)
                callback?.onError(e)
                throw e
            }
        }
    }

    /**
     * 查找目标元素
     */
    private fun findTargetElements(query: String, config: SearchConfig): List<PsiElement> {
        return ReadAction.compute<List<PsiElement>, RuntimeException> {
            val elements = mutableListOf<PsiElement>()
            val scope = createSearchScope(config)
            
            try {
                // 使用符号搜索来查找目标元素
                val symbolProvider = PsiSearchProvider(project)
                val symbolConfig = config.copy(searchType = SearchType.SYMBOL)
                
                // 同步调用符号搜索（在 ReadAction 中）
                val symbolResults = runCatching {
                    kotlinx.coroutines.runBlocking {
                        symbolProvider.search(symbolConfig)
                    }
                }.getOrNull()
                
                symbolResults?.results?.forEach { result ->
                    if (result is SymbolSearchResult && result.psiElement != null) {
                        elements.add(result.psiElement)
                    }
                }
                
            } catch (e: Exception) {
                logger.warn("查找目标元素时发生错误: ${e.message}")
            }
            
            elements.distinctBy { it.textOffset }.take(20) // 限制目标元素数量
        }
    }

    /**
     * 查找元素的所有引用
     */
    private fun findReferencesForElement(element: PsiElement, config: SearchConfig): List<ReferenceSearchResult> {
        return ReadAction.compute<List<ReferenceSearchResult>, RuntimeException> {
            val results = mutableListOf<ReferenceSearchResult>()
            val scope = createSearchScope(config)

            try {
                // 使用 ReferencesSearch 查找引用
                val references = ReferencesSearch.search(element, scope).findAll()
                
                for (reference in references) {
                    if (results.size >= 100) { // 限制单个元素的引用数量
                        break
                    }

                    try {
                        val refElement = reference.element
                        val containingFile = refElement.containingFile?.virtualFile
                        
                        if (containingFile != null) {
                            val document = PsiDocumentManager.getInstance(project).getDocument(refElement.containingFile!!)
                            
                            if (document != null) {
                                val textRange = reference.rangeInElement.shiftRight(refElement.textRange.startOffset)
                                val lineNumber = document.getLineNumber(textRange.startOffset)
                                val columnNumber = textRange.startOffset - document.getLineStartOffset(lineNumber)
                                
                                val context = getReferenceContext(document, lineNumber, textRange)
                                val referenceType = determineReferenceType(refElement, element)
                                val relevanceScore = calculateReferenceScore(reference, element)
                                
                                val result = ReferenceSearchResult(
                                    file = containingFile,
                                    line = lineNumber + 1,
                                    column = columnNumber + 1,
                                    text = reference.canonicalText,
                                    context = context,
                                    relevanceScore = relevanceScore,
                                    reference = reference,
                                    referenceType = referenceType,
                                    targetElement = element
                                )
                                
                                results.add(result)
                            }
                        }
                    } catch (e: Exception) {
                        logger.debug("处理引用时发生错误: ${e.message}")
                    }
                }

            } catch (e: Exception) {
                logger.warn("查找元素引用时发生错误: ${e.message}")
            }

            results
        }
    }

    /**
     * 获取引用上下文
     */
    private fun getReferenceContext(document: com.intellij.openapi.editor.Document, lineNumber: Int, textRange: TextRange): String {
        return try {
            val contextLines = mutableListOf<String>()
            val contextRange = 1 // 前后1行
            
            for (i in (lineNumber - contextRange)..(lineNumber + contextRange)) {
                if (i >= 0 && i < document.lineCount) {
                    val lineStartOffset = document.getLineStartOffset(i)
                    val lineEndOffset = document.getLineEndOffset(i)
                    val lineText = document.getText(TextRange(lineStartOffset, lineEndOffset))
                    
                    if (i == lineNumber) {
                        // 高亮当前行的引用部分
                        val relativeStart = textRange.startOffset - lineStartOffset
                        val relativeEnd = textRange.endOffset - lineStartOffset
                        
                        if (relativeStart >= 0 && relativeEnd <= lineText.length) {
                            val before = lineText.substring(0, relativeStart)
                            val match = lineText.substring(relativeStart, relativeEnd)
                            val after = lineText.substring(relativeEnd)
                            contextLines.add("$before**$match**$after")
                        } else {
                            contextLines.add(lineText)
                        }
                    } else {
                        contextLines.add(lineText)
                    }
                }
            }
            
            contextLines.joinToString("\n")
        } catch (e: Exception) {
            "无法获取上下文"
        }
    }

    /**
     * 确定引用类型
     */
    private fun determineReferenceType(refElement: PsiElement, targetElement: PsiElement): ReferenceType {
        return try {
            val parent = refElement.parent
            
            when {
                // 检查是否是赋值操作
                parent?.toString()?.contains("assignment", ignoreCase = true) == true -> ReferenceType.WRITE
                
                // 检查是否是方法调用
                parent?.toString()?.contains("call", ignoreCase = true) == true -> ReferenceType.CALL
                
                // 检查是否是导入语句
                // PsiTreeUtil.getParentOfType(refElement, PsiImportStatement::class.java) != null -> ReferenceType.IMPORT
                
                // 检查是否是继承关系
                parent?.toString()?.contains("extends", ignoreCase = true) == true ||
                parent?.toString()?.contains("implements", ignoreCase = true) == true -> ReferenceType.INHERITANCE
                
                // 检查是否是注解
                parent?.toString()?.contains("annotation", ignoreCase = true) == true -> ReferenceType.ANNOTATION
                
                // 默认为读取引用
                else -> ReferenceType.READ
            }
        } catch (e: Exception) {
            ReferenceType.OTHER
        }
    }

    /**
     * 计算引用相关性分数
     */
    private fun calculateReferenceScore(reference: PsiReference, targetElement: PsiElement): Double {
        var score = 1.0
        
        try {
            val refElement = reference.element
            val targetFile = targetElement.containingFile
            val refFile = refElement.containingFile
            
            // 同文件引用加分
            if (targetFile == refFile) {
                score += 0.3
            }
            
            // 引用类型加分
            val referenceType = determineReferenceType(refElement, targetElement)
            when (referenceType) {
                ReferenceType.CALL -> score += 0.2
                ReferenceType.WRITE -> score += 0.15
                ReferenceType.READ -> score += 0.1
                else -> score += 0.05
            }
            
            // 引用文本长度影响
            val refText = reference.canonicalText
            if (refText.length > 1) {
                score += 0.1
            }
            
        } catch (e: Exception) {
            logger.debug("计算引用分数时发生错误: ${e.message}")
        }
        
        return min(score, 2.0)
    }

    /**
     * 获取元素名称
     */
    private fun getElementName(element: PsiElement): String {
        return when (element) {
            is PsiNamedElement -> element.name ?: "未知"
            else -> element.text.take(50)
        }
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.scope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
            SearchScope.FILE -> GlobalSearchScope.projectScope(project)
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
            SearchScope.OPEN_FILES -> GlobalSearchScope.projectScope(project)
            SearchScope.CUSTOM -> GlobalSearchScope.projectScope(project)
        }
    }
}
