package com.taobao.mc.aimi.services

import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.*
import kotlinx.coroutines.runBlocking

/**
 * 文本搜索工具使用示例
 * 展示如何使用 TextSearchService 进行各种类型的搜索
 */
class TextSearchExample(private val project: Project) {
    private val logger = LoggerManager.getLogger(TextSearchExample::class.java)
    private val searchService = TextSearchService.getInstance(project)

    /**
     * 基本文本搜索示例
     */
    fun basicTextSearch() {
        logger.info("=== 基本文本搜索示例 ===")
        
        val config = SearchConfig(
            query = "class",
            searchType = SearchType.TEXT,
            caseSensitive = false,
            wholeWords = true,
            maxResults = 50
        )
        
        val callback = object : SearchProgressCallback {
            override fun onProgress(current: Int, total: Int, message: String) {
                logger.info("搜索进度: $current/$total - $message")
            }
            
            override fun onCompleted(results: SearchResults) {
                logger.info("搜索完成: 找到 ${results.totalCount} 个结果，耗时 ${results.searchTime}ms")
                results.results.take(5).forEach { result ->
                    logger.info("结果: ${result.file?.name}:${result.line} - ${result.text}")
                }
            }
            
            override fun onError(error: Throwable) {
                logger.error("搜索失败: ${error.message}")
            }
        }
        
        try {
            val results = searchService.search(config, callback)
            logger.info("同步搜索完成，共找到 ${results.totalCount} 个结果")
        } catch (e: Exception) {
            logger.error("搜索异常: ${e.message}")
        }
    }

    /**
     * Usage 搜索示例
     */
    fun usageSearch() {
        logger.info("=== Usage 搜索示例 ===")
        
        val config = SearchConfig(
            query = "String",
            searchType = SearchType.USAGE,
            caseSensitive = false,
            maxResults = 30
        )
        
        try {
            val results = searchService.search(config)
            logger.info("Usage 搜索完成: 找到 ${results.totalCount} 个使用")
            
            results.results.filterIsInstance<UsageSearchResult>().take(3).forEach { result ->
                logger.info("Usage: ${result.file?.name}:${result.line} - ${result.usageType} - ${result.text}")
            }
        } catch (e: Exception) {
            logger.error("Usage 搜索失败: ${e.message}")
        }
    }

    /**
     * 索引搜索示例
     */
    fun indexSearch() {
        logger.info("=== 索引搜索示例 ===")
        
        val config = SearchConfig(
            query = "main",
            searchType = SearchType.INDEX,
            caseSensitive = false,
            maxResults = 20
        )
        
        try {
            val results = searchService.search(config)
            logger.info("索引搜索完成: 找到 ${results.totalCount} 个结果")
            
            results.results.filterIsInstance<IndexSearchResult>().take(3).forEach { result ->
                logger.info("索引: ${result.file?.name} - ${result.indexType} - ${result.indexKey}")
            }
        } catch (e: Exception) {
            logger.error("索引搜索失败: ${e.message}")
        }
    }

    /**
     * PSI 搜索示例
     */
    fun psiSearch() {
        logger.info("=== PSI 搜索示例 ===")
        
        val config = SearchConfig(
            query = "method",
            searchType = SearchType.PSI,
            caseSensitive = false,
            fileTypes = setOf("java", "kt"),
            maxResults = 25
        )
        
        try {
            val results = searchService.search(config)
            logger.info("PSI 搜索完成: 找到 ${results.totalCount} 个结果")
            
            results.results.filterIsInstance<PsiSearchResult>().take(3).forEach { result ->
                logger.info("PSI: ${result.file?.name}:${result.line} - ${result.elementType} - ${result.elementName}")
            }
        } catch (e: Exception) {
            logger.error("PSI 搜索失败: ${e.message}")
        }
    }

    /**
     * 组合搜索示例
     */
    fun combinedSearch() {
        logger.info("=== 组合搜索示例 ===")
        
        val callback = object : SearchProgressCallback {
            override fun onProgress(current: Int, total: Int, message: String) {
                logger.info("组合搜索进度: $current% - $message")
            }
            
            override fun onCompleted(results: SearchResults) {
                logger.info("组合搜索完成: 总共找到 ${results.totalCount} 个结果")
                
                // 按搜索结果类型分组显示
                val groupedResults = results.results.groupBy { it::class.simpleName }
                groupedResults.forEach { (type, resultList) ->
                    logger.info("$type: ${resultList.size} 个结果")
                }
            }
            
            override fun onError(error: Throwable) {
                logger.error("组合搜索失败: ${error.message}")
            }
        }
        
        try {
            val results = searchService.combinedSearch(
                query = "test",
                searchTypes = setOf(SearchType.TEXT, SearchType.PSI, SearchType.INDEX),
                caseSensitive = false,
                maxResults = 100,
                callback = callback
            )
            
            logger.info("组合搜索同步完成，共找到 ${results.totalCount} 个结果")
        } catch (e: Exception) {
            logger.error("组合搜索异常: ${e.message}")
        }
    }

    /**
     * 异步搜索示例
     */
    fun asyncSearch() {
        logger.info("=== 异步搜索示例 ===")
        
        val config = SearchConfig(
            query = "function",
            searchType = SearchType.TEXT,
            caseSensitive = false,
            maxResults = 40
        )
        
        val callback = object : SearchProgressCallback {
            override fun onProgress(current: Int, total: Int, message: String) {
                logger.info("异步搜索进度: $current/$total - $message")
            }
            
            override fun onCompleted(results: SearchResults) {
                logger.info("异步搜索完成: 找到 ${results.totalCount} 个结果")
            }
            
            override fun onError(error: Throwable) {
                logger.error("异步搜索失败: ${error.message}")
            }
        }
        
        // 启动异步搜索
        val job = searchService.searchAsync(config, callback)
        
        logger.info("异步搜索已启动，任务ID: ${job}")
        
        // 可以在需要时取消搜索
        // job.cancel()
    }

    /**
     * 搜索历史示例
     */
    fun searchHistory() {
        logger.info("=== 搜索历史示例 ===")
        
        // 执行几次搜索以建立历史
        val queries = listOf("class", "method", "String", "test")
        val searchTypes = listOf(SearchType.TEXT, SearchType.USAGE, SearchType.PSI, SearchType.INDEX)
        
        queries.forEachIndexed { index, query ->
            val config = SearchConfig(
                query = query,
                searchType = searchTypes[index % searchTypes.size],
                maxResults = 10
            )
            
            try {
                searchService.search(config)
                logger.info("执行搜索: $query (${config.searchType})")
            } catch (e: Exception) {
                logger.warn("搜索失败: ${e.message}")
            }
        }
        
        // 查看搜索历史
        val history = searchService.getSearchHistory()
        logger.info("搜索历史 (${history.size} 条):")
        history.take(5).forEach { config ->
            logger.info("历史: ${config.query} (${config.searchType})")
        }
    }

    /**
     * 运行所有示例
     */
    fun runAllExamples() {
        logger.info("开始运行所有搜索示例...")
        
        try {
            basicTextSearch()
            Thread.sleep(1000)
            
            usageSearch()
            Thread.sleep(1000)
            
            indexSearch()
            Thread.sleep(1000)
            
            psiSearch()
            Thread.sleep(1000)
            
            combinedSearch()
            Thread.sleep(1000)
            
            asyncSearch()
            Thread.sleep(2000) // 等待异步搜索完成
            
            searchHistory()
            
            logger.info("所有搜索示例运行完成")
            
            // 显示服务状态
            logger.info("服务状态:")
            logger.info("- 是否可用: ${searchService.isAvailable()}")
            logger.info("- 支持的搜索类型: ${searchService.getSupportedSearchTypes()}")
            logger.info("- 活跃搜索数量: ${searchService.getActiveSearchCount()}")
            logger.info("- 搜索历史数量: ${searchService.getSearchHistory().size}")
            
        } catch (e: Exception) {
            logger.error("运行示例时发生错误: ${e.message}", e)
        }
    }
}
