package com.taobao.mc.aimi.find.providers

import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.psi.*
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.util.PsiTreeUtil
import com.taobao.mc.aimi.find.*
import com.taobao.mc.aimi.find.language.LanguageElementTypes
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.min

/**
 * 通用符号搜索提供者
 * 支持多种编程语言的符号搜索，不依赖特定语言的 API
 */
class UniversalSymbolSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(UniversalSymbolSearchProvider::class.java)
    private val psiManager = PsiManager.getInstance(project)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.SYMBOL
    }

    override fun getSupportedFeatures(): Set<SearchFeature> {
        return setOf(
            SearchFeature.CASE_SENSITIVE,
            SearchFeature.FUZZY_MATCH,
            SearchFeature.CONTEXT_AWARE,
            SearchFeature.ASYNC,
            SearchFeature.CANCELLABLE,
            SearchFeature.PROGRESS_TRACKING,
            SearchFeature.RESULT_RANKING
        )
    }

    override suspend fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        return withContext(Dispatchers.IO) {
            try {
                callback?.onProgress(SearchProgress(0, 100, "开始通用符号搜索..."))

                val scope = createSearchScope(config)
                val files = getFilesToSearch(scope, config)
                
                callback?.onProgress(SearchProgress(10, 100, "找到 ${files.size} 个文件需要搜索"))

                var processedFiles = 0
                val totalFiles = files.size

                for (file in files) {
                    if (results.size >= config.maxResults) {
                        break
                    }

                    try {
                        val fileResults = searchSymbolsInFile(file, config)
                        results.addAll(fileResults)
                        
                        processedFiles++
                        val progress = 10 + (processedFiles * 80 / totalFiles)
                        callback?.onProgress(SearchProgress(
                            processedFiles,
                            totalFiles,
                            "搜索文件: ${file.name} (${processedFiles}/${totalFiles})",
                            progress
                        ))
                        
                    } catch (e: Exception) {
                        logger.warn("搜索文件 ${file.path} 中的符号时发生错误: ${e.message}")
                    }
                }

                val searchTime = System.currentTimeMillis() - startTime
                val uniqueResults = results
                    .distinctBy { "${it.file?.path}:${it.line}:${it.column}" }
                    .sortedByDescending { it.relevanceScore }
                    .take(config.maxResults)

                val searchResults = SearchResults(
                    query = config.query,
                    searchType = config.searchType,
                    results = uniqueResults,
                    totalCount = uniqueResults.size,
                    searchTime = searchTime,
                    hasMore = results.size > config.maxResults
                )

                callback?.onProgress(SearchProgress(100, 100, "通用符号搜索完成，找到 ${uniqueResults.size} 个结果"))
                callback?.onCompleted(searchResults)

                searchResults

            } catch (e: Exception) {
                logger.error("通用符号搜索失败: ${e.message}", e)
                callback?.onError(e)
                throw e
            }
        }
    }

    /**
     * 在文件中搜索符号
     */
    private fun searchSymbolsInFile(file: com.intellij.openapi.vfs.VirtualFile, config: SearchConfig): List<SymbolSearchResult> {
        return ReadAction.compute<List<SymbolSearchResult>, RuntimeException> {
            val results = mutableListOf<SymbolSearchResult>()
            
            try {
                val psiFile = psiManager.findFile(file) ?: return@compute results
                val fileExtension = file.extension
                val elementMapping = LanguageElementTypes.getElementMapping(fileExtension)
                
                // 搜索所有命名元素
                val namedElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiNamedElement::class.java)
                for (element in namedElements) {
                    val elementName = element.name ?: continue
                    
                    if (matchesQuery(elementName, config.query, config.caseSensitive)) {
                        val elementType = elementMapping.getElementType(element)
                        val displayName = LanguageElementTypes.getElementDisplayName(elementType, fileExtension)
                        val score = calculateSymbolScore(elementName, config.query, elementType)
                        val containingClass = findContainingClass(element)
                        
                        results.add(createSymbolResult(element, elementName, displayName, score, containingClass))
                    }
                }
                
                // 搜索特定语言的符号模式
                results.addAll(searchLanguageSpecificSymbols(psiFile, config, fileExtension))
                
            } catch (e: Exception) {
                logger.warn("在文件 ${file.path} 中搜索符号时发生错误: ${e.message}")
            }
            
            results.take(30) // 限制单个文件的符号结果数量
        }
    }

    /**
     * 搜索语言特定的符号模式
     */
    private fun searchLanguageSpecificSymbols(psiFile: PsiFile, config: SearchConfig, fileExtension: String?): List<SymbolSearchResult> {
        val results = mutableListOf<SymbolSearchResult>()
        
        try {
            when (fileExtension?.lowercase()) {
                "java" -> results.addAll(searchJavaSymbols(psiFile, config))
                "kt" -> results.addAll(searchKotlinSymbols(psiFile, config))
                "swift" -> results.addAll(searchSwiftSymbols(psiFile, config))
                "m", "mm", "h" -> results.addAll(searchObjectiveCSymbols(psiFile, config))
                "ts", "tsx", "js", "jsx" -> results.addAll(searchTypeScriptJavaScriptSymbols(psiFile, config))
                else -> results.addAll(searchGenericSymbols(psiFile, config))
            }
        } catch (e: Exception) {
            logger.debug("搜索语言特定符号时发生错误: ${e.message}")
        }
        
        return results
    }

    /**
     * 搜索 Java 符号
     */
    private fun searchJavaSymbols(psiFile: PsiFile, config: SearchConfig): List<SymbolSearchResult> {
        val results = mutableListOf<SymbolSearchResult>()
        
        // 搜索包声明
        val packageStatements = PsiTreeUtil.findChildrenOfType(psiFile, PsiPackageStatement::class.java)
        for (packageStmt in packageStatements) {
            val packageName = packageStmt.packageName
            if (packageName != null && matchesQuery(packageName, config.query, config.caseSensitive)) {
                val score = calculateSymbolScore(packageName, config.query, LanguageElementTypes.ElementType.NAMESPACE)
                results.add(createSymbolResult(packageStmt, packageName, "包", score, null))
            }
        }
        
        // 搜索导入声明
        val importStatements = PsiTreeUtil.findChildrenOfType(psiFile, PsiImportStatement::class.java)
        for (importStmt in importStatements) {
            val importText = importStmt.text
            if (matchesQuery(importText, config.query, config.caseSensitive)) {
                val score = calculateSymbolScore(importText, config.query, LanguageElementTypes.ElementType.UNKNOWN)
                results.add(createSymbolResult(importStmt, importText, "导入", score, null))
            }
        }
        
        return results
    }

    /**
     * 搜索 Kotlin 符号
     */
    private fun searchKotlinSymbols(psiFile: PsiFile, config: SearchConfig): List<SymbolSearchResult> {
        val results = mutableListOf<SymbolSearchResult>()
        
        // 搜索所有元素，使用文本模式匹配 Kotlin 特定语法
        val allElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiElement::class.java)
        for (element in allElements) {
            val elementText = element.text.take(50)
            
            // 检查 Kotlin 特定关键字
            when {
                elementText.startsWith("fun ") -> {
                    val functionName = extractIdentifierAfterKeyword(elementText, "fun")
                    if (functionName != null && matchesQuery(functionName, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(functionName, config.query, LanguageElementTypes.ElementType.FUNCTION)
                        results.add(createSymbolResult(element, functionName, "函数", score, null))
                    }
                }
                elementText.startsWith("val ") || elementText.startsWith("var ") -> {
                    val keyword = if (elementText.startsWith("val ")) "val" else "var"
                    val propertyName = extractIdentifierAfterKeyword(elementText, keyword)
                    if (propertyName != null && matchesQuery(propertyName, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(propertyName, config.query, LanguageElementTypes.ElementType.PROPERTY)
                        results.add(createSymbolResult(element, propertyName, "属性", score, null))
                    }
                }
                elementText.startsWith("class ") -> {
                    val className = extractIdentifierAfterKeyword(elementText, "class")
                    if (className != null && matchesQuery(className, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(className, config.query, LanguageElementTypes.ElementType.CLASS)
                        results.add(createSymbolResult(element, className, "类", score, null))
                    }
                }
            }
        }
        
        return results
    }

    /**
     * 搜索 Swift 符号
     */
    private fun searchSwiftSymbols(psiFile: PsiFile, config: SearchConfig): List<SymbolSearchResult> {
        val results = mutableListOf<SymbolSearchResult>()
        
        val allElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiElement::class.java)
        for (element in allElements) {
            val elementText = element.text.take(50)
            
            when {
                elementText.startsWith("func ") -> {
                    val functionName = extractIdentifierAfterKeyword(elementText, "func")
                    if (functionName != null && matchesQuery(functionName, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(functionName, config.query, LanguageElementTypes.ElementType.FUNCTION)
                        results.add(createSymbolResult(element, functionName, "函数", score, null))
                    }
                }
                elementText.startsWith("class ") -> {
                    val className = extractIdentifierAfterKeyword(elementText, "class")
                    if (className != null && matchesQuery(className, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(className, config.query, LanguageElementTypes.ElementType.CLASS)
                        results.add(createSymbolResult(element, className, "类", score, null))
                    }
                }
                elementText.startsWith("struct ") -> {
                    val structName = extractIdentifierAfterKeyword(elementText, "struct")
                    if (structName != null && matchesQuery(structName, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(structName, config.query, LanguageElementTypes.ElementType.STRUCT)
                        results.add(createSymbolResult(element, structName, "结构体", score, null))
                    }
                }
                elementText.startsWith("protocol ") -> {
                    val protocolName = extractIdentifierAfterKeyword(elementText, "protocol")
                    if (protocolName != null && matchesQuery(protocolName, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(protocolName, config.query, LanguageElementTypes.ElementType.PROTOCOL)
                        results.add(createSymbolResult(element, protocolName, "协议", score, null))
                    }
                }
            }
        }
        
        return results
    }

    /**
     * 搜索 Objective-C 符号
     */
    private fun searchObjectiveCSymbols(psiFile: PsiFile, config: SearchConfig): List<SymbolSearchResult> {
        val results = mutableListOf<SymbolSearchResult>()
        
        val allElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiElement::class.java)
        for (element in allElements) {
            val elementText = element.text.take(100)
            
            when {
                elementText.contains("@interface") -> {
                    val className = extractObjectiveCClassName(elementText)
                    if (className != null && matchesQuery(className, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(className, config.query, LanguageElementTypes.ElementType.CLASS)
                        results.add(createSymbolResult(element, className, "类", score, null))
                    }
                }
                elementText.contains("@protocol") -> {
                    val protocolName = extractObjectiveCProtocolName(elementText)
                    if (protocolName != null && matchesQuery(protocolName, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(protocolName, config.query, LanguageElementTypes.ElementType.PROTOCOL)
                        results.add(createSymbolResult(element, protocolName, "协议", score, null))
                    }
                }
                elementText.contains("@property") -> {
                    val propertyName = extractObjectiveCPropertyName(elementText)
                    if (propertyName != null && matchesQuery(propertyName, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(propertyName, config.query, LanguageElementTypes.ElementType.PROPERTY)
                        results.add(createSymbolResult(element, propertyName, "属性", score, null))
                    }
                }
            }
        }
        
        return results
    }

    /**
     * 搜索 TypeScript/JavaScript 符号
     */
    private fun searchTypeScriptJavaScriptSymbols(psiFile: PsiFile, config: SearchConfig): List<SymbolSearchResult> {
        val results = mutableListOf<SymbolSearchResult>()
        
        val allElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiElement::class.java)
        for (element in allElements) {
            val elementText = element.text.take(50)
            
            when {
                elementText.startsWith("function ") -> {
                    val functionName = extractIdentifierAfterKeyword(elementText, "function")
                    if (functionName != null && matchesQuery(functionName, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(functionName, config.query, LanguageElementTypes.ElementType.FUNCTION)
                        results.add(createSymbolResult(element, functionName, "函数", score, null))
                    }
                }
                elementText.startsWith("class ") -> {
                    val className = extractIdentifierAfterKeyword(elementText, "class")
                    if (className != null && matchesQuery(className, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(className, config.query, LanguageElementTypes.ElementType.CLASS)
                        results.add(createSymbolResult(element, className, "类", score, null))
                    }
                }
                elementText.startsWith("interface ") -> {
                    val interfaceName = extractIdentifierAfterKeyword(elementText, "interface")
                    if (interfaceName != null && matchesQuery(interfaceName, config.query, config.caseSensitive)) {
                        val score = calculateSymbolScore(interfaceName, config.query, LanguageElementTypes.ElementType.INTERFACE)
                        results.add(createSymbolResult(element, interfaceName, "接口", score, null))
                    }
                }
            }
        }
        
        return results
    }

    /**
     * 搜索通用符号
     */
    private fun searchGenericSymbols(psiFile: PsiFile, config: SearchConfig): List<SymbolSearchResult> {
        val results = mutableListOf<SymbolSearchResult>()
        
        // 基于文本模式的通用符号搜索
        val allElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiElement::class.java)
        for (element in allElements) {
            val elementText = element.text
            if (elementText.length > 2 && elementText.length < 100 && 
                matchesQuery(elementText, config.query, config.caseSensitive)) {
                val score = calculateSymbolScore(elementText, config.query, LanguageElementTypes.ElementType.UNKNOWN)
                results.add(createSymbolResult(element, elementText, "符号", score, null))
            }
        }
        
        return results.take(10) // 限制通用符号结果数量
    }

    // ==================== 辅助方法 ====================

    /**
     * 提取关键字后的标识符
     */
    private fun extractIdentifierAfterKeyword(text: String, keyword: String): String? {
        val regex = Regex("$keyword\\s+([a-zA-Z_][a-zA-Z0-9_]*)")
        val match = regex.find(text)
        return match?.groupValues?.get(1)
    }

    /**
     * 提取 Objective-C 类名
     */
    private fun extractObjectiveCClassName(text: String): String? {
        val regex = Regex("@interface\\s+([a-zA-Z_][a-zA-Z0-9_]*)")
        val match = regex.find(text)
        return match?.groupValues?.get(1)
    }

    /**
     * 提取 Objective-C 协议名
     */
    private fun extractObjectiveCProtocolName(text: String): String? {
        val regex = Regex("@protocol\\s+([a-zA-Z_][a-zA-Z0-9_]*)")
        val match = regex.find(text)
        return match?.groupValues?.get(1)
    }

    /**
     * 提取 Objective-C 属性名
     */
    private fun extractObjectiveCPropertyName(text: String): String? {
        val regex = Regex("@property.*?([a-zA-Z_][a-zA-Z0-9_]*)\\s*;")
        val match = regex.find(text)
        return match?.groupValues?.get(1)
    }

    /**
     * 检查是否匹配查询
     */
    private fun matchesQuery(text: String, query: String, caseSensitive: Boolean): Boolean {
        return if (caseSensitive) {
            text.contains(query)
        } else {
            text.contains(query, ignoreCase = true)
        }
    }

    /**
     * 计算符号匹配分数
     */
    private fun calculateSymbolScore(symbolName: String, query: String, elementType: LanguageElementTypes.ElementType): Double {
        var score = 1.0
        
        // 精确匹配
        if (symbolName.equals(query, ignoreCase = true)) {
            score = 2.0
        }
        // 前缀匹配
        else if (symbolName.startsWith(query, ignoreCase = true)) {
            score = 1.8
        }
        // 包含匹配
        else if (symbolName.contains(query, ignoreCase = true)) {
            score = 1.5
        }
        
        // 元素类型加分
        when (elementType) {
            LanguageElementTypes.ElementType.CLASS -> score += 0.3
            LanguageElementTypes.ElementType.INTERFACE -> score += 0.3
            LanguageElementTypes.ElementType.FUNCTION -> score += 0.2
            LanguageElementTypes.ElementType.METHOD -> score += 0.2
            LanguageElementTypes.ElementType.PROPERTY -> score += 0.1
            LanguageElementTypes.ElementType.FIELD -> score += 0.1
            else -> score += 0.05
        }
        
        // 长度影响
        val lengthRatio = query.length.toDouble() / symbolName.length
        score += lengthRatio * 0.2
        
        return min(score, 2.0)
    }

    /**
     * 查找包含的类
     */
    private fun findContainingClass(element: PsiElement): String? {
        var parent = element.parent
        while (parent != null) {
            if (parent is PsiClass) {
                return parent.name
            }
            // 对于其他语言，检查文本模式
            val parentText = parent.text.take(50)
            if (parentText.contains("class ") || parentText.contains("@interface")) {
                // 尝试提取类名
                val className = extractIdentifierAfterKeyword(parentText, "class") 
                    ?: extractObjectiveCClassName(parentText)
                if (className != null) {
                    return className
                }
            }
            parent = parent.parent
        }
        return null
    }

    /**
     * 创建符号搜索结果
     */
    private fun createSymbolResult(element: PsiElement, symbolName: String, symbolType: String, score: Double, containingClass: String?): SymbolSearchResult {
        val containingFile = element.containingFile?.virtualFile
        val document = PsiDocumentManager.getInstance(project).getDocument(element.containingFile!!)
        
        val lineNumber = if (document != null) {
            document.getLineNumber(element.textOffset)
        } else {
            0
        }
        
        val columnNumber = if (document != null) {
            element.textOffset - document.getLineStartOffset(lineNumber)
        } else {
            0
        }
        
        val context = getSymbolContext(element, document, lineNumber)
        
        return SymbolSearchResult(
            file = containingFile,
            line = lineNumber + 1,
            column = columnNumber + 1,
            text = symbolName,
            context = context,
            relevanceScore = score,
            symbolName = symbolName,
            symbolType = symbolType,
            psiElement = element,
            containingClass = containingClass
        )
    }

    /**
     * 获取符号上下文
     */
    private fun getSymbolContext(element: PsiElement, document: com.intellij.openapi.editor.Document?, lineNumber: Int): String {
        return try {
            if (document != null) {
                val lineStartOffset = document.getLineStartOffset(lineNumber)
                val lineEndOffset = document.getLineEndOffset(lineNumber)
                val lineText = document.getText(com.intellij.openapi.util.TextRange(lineStartOffset, lineEndOffset))
                lineText.trim()
            } else {
                element.text.take(100)
            }
        } catch (e: Exception) {
            "无法获取上下文"
        }
    }

    /**
     * 获取需要搜索的文件列表
     */
    private fun getFilesToSearch(scope: GlobalSearchScope, config: SearchConfig): List<com.intellij.openapi.vfs.VirtualFile> {
        return ReadAction.compute<List<com.intellij.openapi.vfs.VirtualFile>, RuntimeException> {
            val allFiles = mutableListOf<com.intellij.openapi.vfs.VirtualFile>()
            
            try {
                val allFileNames = com.intellij.psi.search.FilenameIndex.getAllFilenames(project)
                
                for (fileName in allFileNames.take(300)) { // 限制文件名数量
                    val files = com.intellij.psi.search.FilenameIndex.getFilesByName(project, fileName, scope)
                    for (file in files) {
                        if (file.isValid && !file.virtualFile.isDirectory) {
                            val virtualFile = file.virtualFile
                            
                            // 文件类型过滤
                            if (config.fileTypes.isEmpty() || config.fileTypes.contains(virtualFile.extension ?: "")) {
                                // 排除模式过滤
                                val shouldExclude = config.excludePatterns.any { pattern ->
                                    virtualFile.path.contains(pattern, ignoreCase = true)
                                }
                                
                                if (!shouldExclude) {
                                    allFiles.add(virtualFile)
                                }
                            }
                        }
                    }
                }
                
            } catch (e: Exception) {
                logger.warn("获取文件列表时发生错误: ${e.message}")
            }
            
            allFiles.take(150) // 限制总文件数量
        }
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.scope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
            SearchScope.FILE -> GlobalSearchScope.projectScope(project)
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
            SearchScope.OPEN_FILES -> GlobalSearchScope.projectScope(project)
            SearchScope.CUSTOM -> GlobalSearchScope.projectScope(project)
        }
    }
}
