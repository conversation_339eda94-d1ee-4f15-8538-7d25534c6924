package com.taobao.mc.aimi.types

import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiElement
import com.intellij.usages.Usage

/**
 * 搜索类型枚举
 */
enum class SearchType {
    TEXT,       // 文本搜索
    USAGE,      // Usage 搜索
    INDEX,      // 索引搜索
    PSI         // PSI 搜索
}

/**
 * 搜索配置
 */
data class SearchConfig(
    val query: String,
    val searchType: SearchType,
    val caseSensitive: Boolean = false,
    val wholeWords: Boolean = false,
    val useRegex: Boolean = false,
    val searchScope: SearchScope = SearchScope.PROJECT,
    val fileTypes: Set<String> = emptySet(),
    val maxResults: Int = 100
)

/**
 * 搜索范围
 */
enum class SearchScope {
    PROJECT,        // 整个项目
    MODULE,         // 当前模块
    DIRECTORY,      // 指定目录
    FILE,           // 当前文件
    SELECTION       // 当前选择
}

/**
 * 搜索结果基类
 */
sealed class SearchResult {
    abstract val file: VirtualFile?
    abstract val line: Int
    abstract val column: Int
    abstract val text: String
    abstract val context: String
}

/**
 * 文本搜索结果
 */
data class TextSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    val startOffset: Int,
    val endOffset: Int,
    val matchedText: String
) : SearchResult()

/**
 * Usage 搜索结果
 */
data class UsageSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    val usage: Usage,
    val element: PsiElement?,
    val usageType: String
) : SearchResult()

/**
 * 索引搜索结果
 */
data class IndexSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    val indexKey: String,
    val indexValue: String,
    val indexType: String
) : SearchResult()

/**
 * PSI 搜索结果
 */
data class PsiSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    val psiElement: PsiElement,
    val elementType: String,
    val elementName: String?
) : SearchResult()

/**
 * 搜索结果集合
 */
data class SearchResults(
    val query: String,
    val searchType: SearchType,
    val results: List<SearchResult>,
    val totalCount: Int,
    val searchTime: Long,
    val hasMore: Boolean = false
)

/**
 * 搜索进度回调
 */
interface SearchProgressCallback {
    fun onProgress(current: Int, total: Int, message: String)
    fun onCompleted(results: SearchResults)
    fun onError(error: Throwable)
}

/**
 * 搜索提供者接口
 */
interface SearchProvider {
    fun search(config: SearchConfig, callback: SearchProgressCallback? = null): SearchResults
    fun canHandle(searchType: SearchType): Boolean
}
