package com.taobao.mc.aimi.services.search

import com.intellij.find.FindManager
import com.intellij.find.FindModel
import com.intellij.find.FindResult
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.editor.Document
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.TextRange
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiManager
import com.intellij.psi.search.FilenameIndex
import com.intellij.psi.search.GlobalSearchScope
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.*
import java.util.concurrent.CompletableFuture

/**
 * 文本搜索提供者
 * 使用 IntelliJ 的 FindManager 和 FindModel 进行文本搜索
 */
class TextSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(TextSearchProvider::class.java)
    private val findManager = FindManager.getInstance(project)
    private val fileDocumentManager = FileDocumentManager.getInstance()
    private val psiDocumentManager = PsiDocumentManager.getInstance(project)
    private val psiManager = PsiManager.getInstance(project)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.TEXT
    }

    override fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        try {
            callback?.onProgress(0, 100, "开始文本搜索...")

            val findModel = createFindModel(config)
            val searchScope = createSearchScope(config)
            val files = getFilesToSearch(searchScope, config.fileTypes)

            callback?.onProgress(10, 100, "找到 ${files.size} 个文件需要搜索")

            var processedFiles = 0
            val totalFiles = files.size

            for (file in files) {
                if (results.size >= config.maxResults) {
                    break
                }

                try {
                    val fileResults = searchInFile(file, findModel, config)
                    results.addAll(fileResults)
                    
                    processedFiles++
                    val progress = 10 + (processedFiles * 80 / totalFiles)
                    callback?.onProgress(progress, 100, "搜索文件: ${file.name} (${processedFiles}/${totalFiles})")
                    
                } catch (e: Exception) {
                    logger.warn("搜索文件 ${file.path} 时发生错误: ${e.message}")
                }
            }

            val searchTime = System.currentTimeMillis() - startTime
            val searchResults = SearchResults(
                query = config.query,
                searchType = config.searchType,
                results = results.take(config.maxResults),
                totalCount = results.size,
                searchTime = searchTime,
                hasMore = results.size > config.maxResults
            )

            callback?.onProgress(100, 100, "搜索完成，找到 ${results.size} 个结果")
            callback?.onCompleted(searchResults)

            return searchResults

        } catch (e: Exception) {
            logger.error("文本搜索失败: ${e.message}", e)
            callback?.onError(e)
            throw e
        }
    }

    /**
     * 创建查找模型
     */
    private fun createFindModel(config: SearchConfig): FindModel {
        val findModel = FindModel()
        findModel.stringToFind = config.query
        findModel.isCaseSensitive = config.caseSensitive
        findModel.isWholeWordsOnly = config.wholeWords
        findModel.isRegularExpressions = config.useRegex
        // findModel.isSearchHighlighters = true
        // findModel.isSearchInCommentsAndLiterals = true
        findModel.isPreserveCase = false
        return findModel
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.searchScope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project) // 简化实现
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project) // 简化实现
            SearchScope.FILE -> GlobalSearchScope.projectScope(project) // 简化实现
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project) // 简化实现
        }
    }

    /**
     * 获取需要搜索的文件列表
     */
    private fun getFilesToSearch(scope: GlobalSearchScope, fileTypes: Set<String>): List<VirtualFile> {
        return ReadAction.compute<List<VirtualFile>, RuntimeException> {
            val allFiles = mutableListOf<VirtualFile>()
            
            // 使用 FilenameIndex 获取所有文件
            val allFileNames = FilenameIndex.getAllFilenames(project)
            
            for (fileName in allFileNames) {
                val files = FilenameIndex.getFilesByName(project, fileName, scope)
                for (file in files) {
                    if (file.isValid && !file.isDirectory) {
                        // 过滤文件类型
                        if (fileTypes.isEmpty() || fileTypes.contains(file.virtualFile.extension ?: "")) {
                            allFiles.add(file.virtualFile)
                        }
                    }
                }
            }
            
            allFiles
        }
    }

    /**
     * 在单个文件中搜索
     */
    private fun searchInFile(file: VirtualFile, findModel: FindModel, config: SearchConfig): List<TextSearchResult> {
        return ReadAction.compute<List<TextSearchResult>, RuntimeException> {
            val results = mutableListOf<TextSearchResult>()
            
            try {
                val document = fileDocumentManager.getDocument(file) ?: return@compute results
                val text = document.text
                
                if (text.isEmpty()) {
                    return@compute results
                }

                var offset = 0
                while (offset < text.length) {
                    val findResult = findManager.findString(text, offset, findModel)
                    
                    if (!findResult.isStringFound) {
                        break
                    }

                    val startOffset = findResult.startOffset
                    val endOffset = findResult.endOffset
                    val matchedText = text.substring(startOffset, endOffset)
                    
                    // 获取行号和列号
                    val lineNumber = document.getLineNumber(startOffset)
                    val columnNumber = startOffset - document.getLineStartOffset(lineNumber)
                    
                    // 获取上下文
                    val context = getContext(document, lineNumber, startOffset, endOffset)
                    
                    val result = TextSearchResult(
                        file = file,
                        line = lineNumber + 1, // 1-based line number
                        column = columnNumber + 1, // 1-based column number
                        text = matchedText,
                        context = context,
                        startOffset = startOffset,
                        endOffset = endOffset,
                        matchedText = matchedText
                    )
                    
                    results.add(result)
                    offset = endOffset
                    
                    // 限制单个文件的结果数量
                    if (results.size >= 50) {
                        break
                    }
                }
                
            } catch (e: Exception) {
                logger.warn("在文件 ${file.path} 中搜索时发生错误: ${e.message}")
            }
            
            results
        }
    }

    /**
     * 获取搜索结果的上下文
     */
    private fun getContext(document: Document, lineNumber: Int, startOffset: Int, endOffset: Int): String {
        return try {
            val lineStartOffset = document.getLineStartOffset(lineNumber)
            val lineEndOffset = document.getLineEndOffset(lineNumber)
            val lineText = document.getText(TextRange(lineStartOffset, lineEndOffset))
            
            // 高亮匹配的文本
            val relativeStart = startOffset - lineStartOffset
            val relativeEnd = endOffset - lineStartOffset
            
            if (relativeStart >= 0 && relativeEnd <= lineText.length) {
                val before = lineText.substring(0, relativeStart)
                val match = lineText.substring(relativeStart, relativeEnd)
                val after = lineText.substring(relativeEnd)
                "$before**$match**$after"
            } else {
                lineText
            }
        } catch (e: Exception) {
            "无法获取上下文"
        }
    }
}
