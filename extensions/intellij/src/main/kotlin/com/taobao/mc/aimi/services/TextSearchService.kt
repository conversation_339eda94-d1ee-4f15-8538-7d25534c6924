package com.taobao.mc.aimi.services

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.services.search.*
import com.taobao.mc.aimi.types.*
import kotlinx.coroutines.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 文本搜索服务
 * 提供统一的搜索接口，支持多种搜索方式：
 * 1. 文本检索 - 使用 FindManager 进行文本搜索
 * 2. Usage 检索 - 使用 ReferencesSearch 查找符号使用
 * 3. 索引检索 - 使用 FileBasedIndex 进行快速搜索
 * 4. PSI 搜索 - 使用 PSI 树遍历进行结构化搜索
 */
@Service(Service.Level.PROJECT)
class TextSearchService(private val project: Project) {
    private val logger = LoggerManager.getLogger(TextSearchService::class.java)
    
    // 创建服务级别的协程作用域
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 搜索提供者
    private val searchProviders = mutableMapOf<SearchType, SearchProvider>()
    
    // 搜索历史
    private val searchHistory = mutableListOf<SearchConfig>()
    private val maxHistorySize = 100
    
    // 活跃搜索任务
    private val activeSearches = ConcurrentHashMap<String, Job>()
    
    // 服务状态
    private val isInitialized = AtomicBoolean(false)

    companion object {
        fun getInstance(project: Project): TextSearchService = project.service()
    }

    init {
        initializeSearchProviders()
    }

    /**
     * 初始化搜索提供者
     */
    private fun initializeSearchProviders() {
        if (isInitialized.compareAndSet(false, true)) {
            logger.info("初始化文本搜索服务")
            
            searchProviders[SearchType.TEXT] = TextSearchProvider(project)
            searchProviders[SearchType.USAGE] = UsageSearchProvider(project)
            searchProviders[SearchType.INDEX] = IndexSearchProvider(project)
            searchProviders[SearchType.PSI] = PsiSearchProvider(project)
            
            logger.info("搜索提供者初始化完成: ${searchProviders.keys}")
        }
    }

    /**
     * 执行搜索
     */
    fun search(config: SearchConfig, callback: SearchProgressCallback? = null): SearchResults {
        logger.info("开始搜索: query=${config.query}, type=${config.searchType}")
        
        // 添加到搜索历史
        addToHistory(config)
        
        val provider = searchProviders[config.searchType]
            ?: throw IllegalArgumentException("不支持的搜索类型: ${config.searchType}")
        
        return try {
            provider.search(config, callback)
        } catch (e: Exception) {
            logger.error("搜索失败: ${e.message}", e)
            throw e
        }
    }

    /**
     * 异步执行搜索
     */
    fun searchAsync(
        config: SearchConfig, 
        callback: SearchProgressCallback? = null
    ): Job {
        val searchId = generateSearchId(config)
        
        // 取消之前的同类搜索
        cancelSearch(searchId)
        
        val job = serviceScope.launch {
            try {
                val results = search(config, callback)
                callback?.onCompleted(results)
            } catch (e: CancellationException) {
                logger.info("搜索被取消: $searchId")
            } catch (e: Exception) {
                logger.error("异步搜索失败: ${e.message}", e)
                callback?.onError(e)
            }
        }
        
        activeSearches[searchId] = job
        
        // 清理完成的任务
        job.invokeOnCompletion {
            activeSearches.remove(searchId)
        }
        
        return job
    }

    /**
     * 组合搜索 - 同时使用多种搜索方式
     */
    fun combinedSearch(
        query: String,
        searchTypes: Set<SearchType> = setOf(SearchType.TEXT, SearchType.USAGE, SearchType.INDEX, SearchType.PSI),
        caseSensitive: Boolean = false,
        maxResults: Int = 100,
        callback: SearchProgressCallback? = null
    ): SearchResults {
        logger.info("开始组合搜索: query=$query, types=$searchTypes")
        
        val startTime = System.currentTimeMillis()
        val allResults = mutableListOf<SearchResult>()
        val searchConfigs = searchTypes.map { type ->
            SearchConfig(
                query = query,
                searchType = type,
                caseSensitive = caseSensitive,
                maxResults = maxResults / searchTypes.size
            )
        }
        
        var completedSearches = 0
        val totalSearches = searchConfigs.size
        
        for (config in searchConfigs) {
            try {
                callback?.onProgress(
                    (completedSearches * 100 / totalSearches), 
                    100, 
                    "执行 ${config.searchType} 搜索..."
                )
                
                val results = search(config)
                allResults.addAll(results.results)
                
                completedSearches++
            } catch (e: Exception) {
                logger.warn("${config.searchType} 搜索失败: ${e.message}")
                completedSearches++
            }
        }
        
        // 去重和排序
        val uniqueResults = allResults
            .distinctBy { "${it.file?.path}:${it.line}:${it.column}:${it.text}" }
            .sortedWith(compareBy<SearchResult> { it.file?.name }.thenBy { it.line })
            .take(maxResults)
        
        val searchTime = System.currentTimeMillis() - startTime
        val combinedResults = SearchResults(
            query = query,
            searchType = SearchType.TEXT, // 默认类型
            results = uniqueResults,
            totalCount = uniqueResults.size,
            searchTime = searchTime,
            hasMore = allResults.size > maxResults
        )
        
        callback?.onProgress(100, 100, "组合搜索完成，找到 ${uniqueResults.size} 个结果")
        callback?.onCompleted(combinedResults)
        
        return combinedResults
    }

    /**
     * 取消搜索
     */
    fun cancelSearch(searchId: String) {
        activeSearches[searchId]?.cancel()
        activeSearches.remove(searchId)
    }

    /**
     * 取消所有搜索
     */
    fun cancelAllSearches() {
        logger.info("取消所有活跃搜索")
        activeSearches.values.forEach { it.cancel() }
        activeSearches.clear()
    }

    /**
     * 获取搜索历史
     */
    fun getSearchHistory(): List<SearchConfig> {
        return searchHistory.toList()
    }

    /**
     * 清空搜索历史
     */
    fun clearSearchHistory() {
        searchHistory.clear()
        logger.info("搜索历史已清空")
    }

    /**
     * 获取活跃搜索数量
     */
    fun getActiveSearchCount(): Int {
        return activeSearches.size
    }

    /**
     * 检查服务是否可用
     */
    fun isAvailable(): Boolean {
        return isInitialized.get() && searchProviders.isNotEmpty()
    }

    /**
     * 获取支持的搜索类型
     */
    fun getSupportedSearchTypes(): Set<SearchType> {
        return searchProviders.keys.toSet()
    }

    // ==================== 私有方法 ====================

    /**
     * 添加到搜索历史
     */
    private fun addToHistory(config: SearchConfig) {
        synchronized(searchHistory) {
            // 移除重复的搜索
            searchHistory.removeIf { it.query == config.query && it.searchType == config.searchType }
            
            // 添加到开头
            searchHistory.add(0, config)
            
            // 限制历史大小
            if (searchHistory.size > maxHistorySize) {
                searchHistory.removeAt(searchHistory.size - 1)
            }
        }
    }

    /**
     * 生成搜索ID
     */
    private fun generateSearchId(config: SearchConfig): String {
        return "${config.searchType}_${config.query.hashCode()}_${System.currentTimeMillis()}"
    }

    /**
     * 释放资源
     */
    fun dispose() {
        logger.info("释放文本搜索服务资源")
        cancelAllSearches()
        serviceScope.cancel()
        searchProviders.clear()
        searchHistory.clear()
        isInitialized.set(false)
    }
}
