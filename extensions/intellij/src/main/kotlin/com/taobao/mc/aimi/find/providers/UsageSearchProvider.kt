package com.taobao.mc.aimi.find.providers

import com.intellij.find.findUsages.FindUsagesHelper
import com.intellij.find.findUsages.FindUsagesOptions
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.project.Project
import com.intellij.psi.*
import com.intellij.psi.search.GlobalSearchScope
import com.intellij.psi.util.PsiTreeUtil
import com.intellij.usages.Usage
import com.intellij.usages.UsageInfo
import com.intellij.usages.UsageInfoToUsageConverter
import com.taobao.mc.aimi.find.*
import com.taobao.mc.aimi.find.language.LanguageElementTypes
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlin.math.min

/**
 * 基于 FindUsagesHelper 的 Usage 搜索提供者
 * 使用 IntelliJ 的 FindUsagesHelper 进行高效的使用查找
 */
class UsageSearchProvider(private val project: Project) : SearchProvider {
    private val logger = LoggerManager.getLogger(UsageSearchProvider::class.java)
    private val psiManager = PsiManager.getInstance(project)

    override fun canHandle(searchType: SearchType): Boolean {
        return searchType == SearchType.USAGE
    }

    override fun getSupportedFeatures(): Set<SearchFeature> {
        return setOf(
            SearchFeature.CONTEXT_AWARE,
            SearchFeature.ASYNC,
            SearchFeature.CANCELLABLE,
            SearchFeature.PROGRESS_TRACKING,
            SearchFeature.RESULT_RANKING
        )
    }

    override suspend fun search(config: SearchConfig, callback: SearchProgressCallback?): SearchResults {
        val startTime = System.currentTimeMillis()
        val results = mutableListOf<SearchResult>()

        return withContext(Dispatchers.IO) {
            try {
                callback?.onProgress(SearchProgress(0, 100, "开始 Usage 搜索..."))

                // 1. 查找目标元素
                callback?.onProgress(SearchProgress(10, 100, "查找目标元素..."))
                val targetElements = findTargetElements(config.query, config)
                
                if (targetElements.isEmpty()) {
                    val searchResults = SearchResults(
                        query = config.query,
                        searchType = config.searchType,
                        results = emptyList(),
                        totalCount = 0,
                        searchTime = System.currentTimeMillis() - startTime,
                        suggestions = listOf("未找到匹配的符号，请检查拼写或尝试部分匹配")
                    )
                    callback?.onCompleted(searchResults)
                    return@withContext searchResults
                }

                callback?.onProgress(SearchProgress(20, 100, "找到 ${targetElements.size} 个目标元素"))

                // 2. 搜索每个目标元素的使用
                var processedElements = 0
                val totalElements = targetElements.size

                for (element in targetElements) {
                    if (results.size >= config.maxResults) {
                        break
                    }

                    try {
                        val elementUsages = findUsagesForElement(element, config)
                        results.addAll(elementUsages)
                        
                        processedElements++
                        val progress = 20 + (processedElements * 70 / totalElements)
                        callback?.onProgress(SearchProgress(
                            processedElements,
                            totalElements,
                            "搜索元素使用: ${getElementName(element)} (${processedElements}/${totalElements})",
                            progress
                        ))
                        
                    } catch (e: Exception) {
                        logger.warn("搜索元素 ${getElementName(element)} 的使用时发生错误: ${e.message}")
                    }
                }

                val searchTime = System.currentTimeMillis() - startTime
                val uniqueResults = results
                    .distinctBy { "${it.file?.path}:${it.line}:${it.column}" }
                    .sortedByDescending { it.relevanceScore }
                    .take(config.maxResults)

                val searchResults = SearchResults(
                    query = config.query,
                    searchType = config.searchType,
                    results = uniqueResults,
                    totalCount = uniqueResults.size,
                    searchTime = searchTime,
                    hasMore = results.size > config.maxResults
                )

                callback?.onProgress(SearchProgress(100, 100, "Usage 搜索完成，找到 ${uniqueResults.size} 个使用"))
                callback?.onCompleted(searchResults)

                searchResults

            } catch (e: Exception) {
                logger.error("Usage 搜索失败: ${e.message}", e)
                callback?.onError(e)
                throw e
            }
        }
    }

    /**
     * 查找目标元素
     */
    private fun findTargetElements(query: String, config: SearchConfig): List<PsiElement> {
        return ReadAction.compute<List<PsiElement>, RuntimeException> {
            val elements = mutableListOf<PsiElement>()
            val scope = createSearchScope(config)
            val files = getFilesToSearch(scope, config)
            
            try {
                for (file in files.take(100)) { // 限制文件数量
                    val psiFile = psiManager.findFile(file) ?: continue
                    
                    // 查找命名元素
                    val namedElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiNamedElement::class.java)
                    for (element in namedElements) {
                        val elementName = element.name
                        if (elementName != null && matchesQuery(elementName, query, config.caseSensitive)) {
                            elements.add(element)
                        }
                    }
                }
                
            } catch (e: Exception) {
                logger.warn("查找目标元素时发生错误: ${e.message}")
            }
            
            elements.distinctBy { it.textOffset }.take(20) // 限制目标元素数量
        }
    }

    /**
     * 查找元素的所有使用
     */
    private fun findUsagesForElement(element: PsiElement, config: SearchConfig): List<UsageSearchResult> {
        return ReadAction.compute<List<UsageSearchResult>, RuntimeException> {
            val results = mutableListOf<UsageSearchResult>()
            val scope = createSearchScope(config)

            try {
                // 创建查找使用选项
                val findUsagesOptions = createFindUsagesOptions(config)
                
                // 使用 FindUsagesHelper 查找使用
                val usageInfos = mutableListOf<UsageInfo>()
                val findUsagesHelper = FindUsagesHelper.getInstance(project)
                
                // 收集使用信息
                findUsagesHelper.processElementUsages(element, { usageInfo ->
                    usageInfos.add(usageInfo)
                    usageInfos.size < 200 // 限制使用数量
                }, findUsagesOptions)
                
                // 转换为搜索结果
                for (usageInfo in usageInfos) {
                    if (results.size >= 100) { // 限制单个元素的使用数量
                        break
                    }

                    try {
                        val usageElement = usageInfo.element
                        if (usageElement != null) {
                            val containingFile = usageElement.containingFile?.virtualFile
                            
                            if (containingFile != null) {
                                val document = PsiDocumentManager.getInstance(project).getDocument(usageElement.containingFile!!)
                                
                                if (document != null) {
                                    val textRange = usageInfo.rangeInElement?.shiftRight(usageElement.textRange.startOffset) 
                                        ?: usageElement.textRange
                                    val lineNumber = document.getLineNumber(textRange.startOffset)
                                    val columnNumber = textRange.startOffset - document.getLineStartOffset(lineNumber)
                                    
                                    val context = getUsageContext(document, lineNumber, textRange)
                                    val usageType = determineUsageType(usageInfo, element)
                                    val relevanceScore = calculateUsageScore(usageInfo, element)
                                    
                                    // 创建 Usage 对象
                                    val usage = createUsageFromUsageInfo(usageInfo)
                                    
                                    val result = UsageSearchResult(
                                        file = containingFile,
                                        line = lineNumber + 1,
                                        column = columnNumber + 1,
                                        text = usageElement.text.take(50),
                                        context = context,
                                        relevanceScore = relevanceScore,
                                        usage = usage,
                                        usageType = usageType,
                                        element = usageElement
                                    )
                                    
                                    results.add(result)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        logger.debug("处理使用信息时发生错误: ${e.message}")
                    }
                }

            } catch (e: Exception) {
                logger.warn("查找元素使用时发生错误: ${e.message}")
            }

            results
        }
    }

    /**
     * 创建查找使用选项
     */
    private fun createFindUsagesOptions(config: SearchConfig): FindUsagesOptions {
        val options = FindUsagesOptions(project)
        
        // 设置搜索范围
        options.searchScope = createSearchScope(config)
        
        // 设置搜索选项
        options.isSearchForTextOccurrences = true
        options.isUsages = true
        
        return options
    }

    /**
     * 获取使用上下文
     */
    private fun getUsageContext(document: com.intellij.openapi.editor.Document, lineNumber: Int, textRange: com.intellij.openapi.util.TextRange): String {
        return try {
            val contextLines = mutableListOf<String>()
            val contextRange = 1 // 前后1行
            
            for (i in (lineNumber - contextRange)..(lineNumber + contextRange)) {
                if (i >= 0 && i < document.lineCount) {
                    val lineStartOffset = document.getLineStartOffset(i)
                    val lineEndOffset = document.getLineEndOffset(i)
                    val lineText = document.getText(com.intellij.openapi.util.TextRange(lineStartOffset, lineEndOffset))
                    
                    if (i == lineNumber) {
                        // 高亮当前行的使用部分
                        val relativeStart = textRange.startOffset - lineStartOffset
                        val relativeEnd = textRange.endOffset - lineStartOffset
                        
                        if (relativeStart >= 0 && relativeEnd <= lineText.length) {
                            val before = lineText.substring(0, relativeStart)
                            val match = lineText.substring(relativeStart, relativeEnd)
                            val after = lineText.substring(relativeEnd)
                            contextLines.add("$before**$match**$after")
                        } else {
                            contextLines.add(lineText)
                        }
                    } else {
                        contextLines.add(lineText)
                    }
                }
            }
            
            contextLines.joinToString("\n")
        } catch (e: Exception) {
            "无法获取上下文"
        }
    }

    /**
     * 确定使用类型
     */
    private fun determineUsageType(usageInfo: UsageInfo, targetElement: PsiElement): UsageType {
        return try {
            val usageElement = usageInfo.element
            if (usageElement == null) return UsageType.OTHER
            
            val parent = usageElement.parent
            val parentText = parent?.toString()?.lowercase() ?: ""
            val elementText = usageElement.text.lowercase()
            
            when {
                // 检查是否是声明
                usageElement == targetElement -> UsageType.DECLARATION
                
                // 检查是否是赋值操作
                parentText.contains("assignment") || 
                parent?.text?.contains("=") == true -> UsageType.ASSIGNMENT
                
                // 检查是否是方法调用
                parentText.contains("call") || 
                parent?.text?.contains("(") == true -> UsageType.METHOD_CALL
                
                // 检查是否是实例化
                parentText.contains("new") || 
                elementText.contains("new ") -> UsageType.INSTANTIATION
                
                // 检查是否是参数
                parentText.contains("parameter") -> UsageType.PARAMETER
                
                // 检查是否是返回类型
                parentText.contains("return") -> UsageType.RETURN_TYPE
                
                // 检查是否是字段访问
                parent?.text?.contains(".") == true -> UsageType.FIELD_ACCESS
                
                // 默认为引用
                else -> UsageType.REFERENCE
            }
        } catch (e: Exception) {
            UsageType.OTHER
        }
    }

    /**
     * 计算使用相关性分数
     */
    private fun calculateUsageScore(usageInfo: UsageInfo, targetElement: PsiElement): Double {
        var score = 1.0
        
        try {
            val usageElement = usageInfo.element
            if (usageElement == null) return score
            
            val targetFile = targetElement.containingFile
            val usageFile = usageElement.containingFile
            
            // 同文件使用加分
            if (targetFile == usageFile) {
                score += 0.3
            }
            
            // 使用类型加分
            val usageType = determineUsageType(usageInfo, targetElement)
            when (usageType) {
                UsageType.DECLARATION -> score += 0.5
                UsageType.METHOD_CALL -> score += 0.3
                UsageType.ASSIGNMENT -> score += 0.2
                UsageType.REFERENCE -> score += 0.1
                else -> score += 0.05
            }
            
            // 使用文本长度影响
            val usageText = usageElement.text
            if (usageText.length > 1) {
                score += 0.1
            }
            
        } catch (e: Exception) {
            logger.debug("计算使用分数时发生错误: ${e.message}")
        }
        
        return min(score, 2.0)
    }

    /**
     * 从 UsageInfo 创建 Usage 对象
     */
    private fun createUsageFromUsageInfo(usageInfo: UsageInfo): Usage {
        return try {
            // 使用 IntelliJ 的转换器
            val usages = UsageInfoToUsageConverter.convert(arrayOf(usageInfo))
            if (usages.isNotEmpty()) {
                usages[0]
            } else {
                createSimpleUsage(usageInfo)
            }
        } catch (e: Exception) {
            logger.debug("创建 Usage 对象时发生错误: ${e.message}")
            createSimpleUsage(usageInfo)
        }
    }

    /**
     * 创建简单的 Usage 对象
     */
    private fun createSimpleUsage(usageInfo: UsageInfo): Usage {
        return object : Usage {
            override fun getPresentation(): com.intellij.usages.UsagePresentation {
                return object : com.intellij.usages.UsagePresentation {
                    override fun getIcon(): javax.swing.Icon? = null
                    override fun getText(): Array<com.intellij.usages.TextChunk> = emptyArray()
                    override fun getPlainText(): String = usageInfo.element?.text ?: "使用"
                    override fun getTooltipText(): String = usageInfo.element?.text ?: "使用"
                }
            }
            
            override fun isValid(): Boolean = usageInfo.element?.isValid ?: false
            override fun isReadOnly(): Boolean = false
            override fun selectInEditor() {}
            override fun highlightInEditor() {}
            override fun navigate(requestFocus: Boolean) {}
            override fun canNavigate(): Boolean = true
            override fun canNavigateToSource(): Boolean = true
        }
    }

    /**
     * 获取元素名称
     */
    private fun getElementName(element: PsiElement): String {
        return when (element) {
            is PsiNamedElement -> element.name ?: "未知"
            else -> element.text.take(50)
        }
    }

    /**
     * 检查是否匹配查询
     */
    private fun matchesQuery(text: String, query: String, caseSensitive: Boolean): Boolean {
        return if (caseSensitive) {
            text.contains(query)
        } else {
            text.contains(query, ignoreCase = true)
        }
    }

    /**
     * 获取需要搜索的文件列表
     */
    private fun getFilesToSearch(scope: GlobalSearchScope, config: SearchConfig): List<com.intellij.openapi.vfs.VirtualFile> {
        return ReadAction.compute<List<com.intellij.openapi.vfs.VirtualFile>, RuntimeException> {
            val allFiles = mutableListOf<com.intellij.openapi.vfs.VirtualFile>()
            
            try {
                val allFileNames = com.intellij.psi.search.FilenameIndex.getAllFilenames(project)
                
                for (fileName in allFileNames.take(200)) { // 限制文件名数量
                    val files = com.intellij.psi.search.FilenameIndex.getFilesByName(project, fileName, scope)
                    for (file in files) {
                        if (file.isValid && !file.virtualFile.isDirectory) {
                            val virtualFile = file.virtualFile
                            
                            // 文件类型过滤
                            if (config.fileTypes.isEmpty() || config.fileTypes.contains(virtualFile.extension ?: "")) {
                                allFiles.add(virtualFile)
                            }
                        }
                    }
                }
                
            } catch (e: Exception) {
                logger.warn("获取文件列表时发生错误: ${e.message}")
            }
            
            allFiles.take(100) // 限制总文件数量
        }
    }

    /**
     * 创建搜索范围
     */
    private fun createSearchScope(config: SearchConfig): GlobalSearchScope {
        return when (config.scope) {
            SearchScope.PROJECT -> GlobalSearchScope.projectScope(project)
            SearchScope.MODULE -> GlobalSearchScope.projectScope(project)
            SearchScope.DIRECTORY -> GlobalSearchScope.projectScope(project)
            SearchScope.FILE -> GlobalSearchScope.projectScope(project)
            SearchScope.SELECTION -> GlobalSearchScope.projectScope(project)
            SearchScope.OPEN_FILES -> GlobalSearchScope.projectScope(project)
            SearchScope.CUSTOM -> GlobalSearchScope.projectScope(project)
        }
    }
}
