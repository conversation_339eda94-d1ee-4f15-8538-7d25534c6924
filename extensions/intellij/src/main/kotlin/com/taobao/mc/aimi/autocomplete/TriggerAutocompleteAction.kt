package com.taobao.mc.aimi.autocomplete

import com.github.continuedev.continueintellijextension.autocomplete.AutocompleteService
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.EditorKind

class TriggerAutocompleteAction : AnAction() {

    override fun actionPerformed(e: AnActionEvent) {
        val editor = e.getData(CommonDataKeys.EDITOR) ?: return
        val project = e.project ?: return

        // 确保是主编辑器
        if (editor.editorKind != EditorKind.MAIN_EDITOR) {
            return
        }

        // 获取自动补全服务
        val autocompleteService = project.service<AutocompleteService>()

        println("主动触发代码补全")
        // 主动触发代码补全
        autocompleteService.triggerCompletion(editor)
    }

    override fun update(e: AnActionEvent) {
        val editor = e.getData(CommonDataKeys.EDITOR)
        val project = e.project

        // 只有在有编辑器和项目的情况下才启用此 Action
        e.presentation.isEnabled = editor != null && project != null &&
                editor.editorKind == EditorKind.MAIN_EDITOR
    }

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}