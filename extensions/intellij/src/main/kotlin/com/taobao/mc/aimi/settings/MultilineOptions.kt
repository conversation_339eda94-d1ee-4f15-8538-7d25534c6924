package com.taobao.mc.aimi.settings

enum class MultilineOptions(val displayName: String, val value: String) {
    AUTO("自动", "auto"),
    ALWAYS("多行补全", "always"),
    NEVER("单行补全", "never");

    override fun toString(): String {
        return displayName
    }

    companion object {
        val options = arrayOf(AUTO, ALWAYS, NEVER)

        fun fromValue(value: String): MultilineOptions {
            return when (value) {
                ALWAYS.value -> ALWAYS
                NEVER.value -> NEVER
                else -> AUTO
            }
        }
    }
}