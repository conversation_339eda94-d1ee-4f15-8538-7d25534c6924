package com.taobao.mc.aimi.find

import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiReference
import com.intellij.usages.Usage

/**
 * 搜索类型枚举
 */
enum class SearchType {
    TEXT,           // 文本搜索
    FILE_NAME,      // 文件名搜索
    SYMBOL,         // 符号搜索
    REFERENCE,      // 引用搜索
    USAGE,          // 使用搜索
    PSI_STRUCTURE,  // PSI 结构搜索
    INDEX,          // 索引搜索
    FULL_TEXT       // 全文搜索
}

/**
 * 搜索范围类型
 */
enum class SearchScope {
    PROJECT,        // 整个项目
    MODULE,         // 当前模块
    DIRECTORY,      // 指定目录
    FILE,           // 当前文件
    SELECTION,      // 当前选择
    OPEN_FILES,     // 打开的文件
    CUSTOM          // 自定义范围
}

/**
 * 搜索配置
 */
data class SearchConfig(
    val query: String,
    val searchType: SearchType,
    val scope: SearchScope = SearchScope.PROJECT,
    val caseSensitive: Boolean = false,
    val wholeWords: Boolean = false,
    val useRegex: Boolean = false,
    val includeComments: Boolean = true,
    val includeStrings: Boolean = true,
    val fileTypes: Set<String> = emptySet(),
    val excludePatterns: Set<String> = emptySet(),
    val maxResults: Int = 1000,
    val timeout: Long = 30000L // 30秒超时
)

/**
 * 搜索结果基类
 */
sealed class SearchResult {
    abstract val file: VirtualFile?
    abstract val line: Int
    abstract val column: Int
    abstract val text: String
    abstract val context: String
    abstract val relevanceScore: Double
}

/**
 * 文本搜索结果
 */
data class TextSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    override val relevanceScore: Double,
    val startOffset: Int,
    val endOffset: Int,
    val matchedText: String,
    val lineText: String
) : SearchResult()

/**
 * 文件名搜索结果
 */
data class FileNameSearchResult(
    override val file: VirtualFile?,
    override val line: Int = 1,
    override val column: Int = 1,
    override val text: String,
    override val context: String,
    override val relevanceScore: Double,
    val fileName: String,
    val filePath: String,
    val matchType: FileMatchType
) : SearchResult()

/**
 * 符号搜索结果
 */
data class SymbolSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    override val relevanceScore: Double,
    val symbolName: String,
    val symbolType: String,
    val psiElement: PsiElement?,
    val containingClass: String?
) : SearchResult()

/**
 * 引用搜索结果
 */
data class ReferenceSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    override val relevanceScore: Double,
    val reference: PsiReference,
    val referenceType: ReferenceType,
    val targetElement: PsiElement?
) : SearchResult()

/**
 * 使用搜索结果
 */
data class UsageSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    override val relevanceScore: Double,
    val usage: Usage,
    val usageType: UsageType,
    val element: PsiElement?
) : SearchResult()

/**
 * PSI 结构搜索结果
 */
data class PsiStructureSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    override val relevanceScore: Double,
    val psiElement: PsiElement,
    val elementType: String,
    val elementName: String?,
    val parentElement: PsiElement?
) : SearchResult()

/**
 * 索引搜索结果
 */
data class IndexSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    override val relevanceScore: Double,
    val indexKey: String,
    val indexValue: String,
    val indexType: String
) : SearchResult()

/**
 * 文件匹配类型
 */
enum class FileMatchType {
    EXACT,          // 精确匹配
    PREFIX,         // 前缀匹配
    SUFFIX,         // 后缀匹配
    CONTAINS,       // 包含匹配
    FUZZY,          // 模糊匹配
    REGEX           // 正则匹配
}

/**
 * 引用类型
 */
enum class ReferenceType {
    READ,           // 读取引用
    WRITE,          // 写入引用
    CALL,           // 调用引用
    IMPORT,         // 导入引用
    INHERITANCE,    // 继承引用
    ANNOTATION,     // 注解引用
    OTHER           // 其他引用
}

/**
 * 使用类型
 */
enum class UsageType {
    DECLARATION,    // 声明
    DEFINITION,     // 定义
    REFERENCE,      // 引用
    CALL,           // 调用
    INSTANTIATION,  // 实例化
    ASSIGNMENT,     // 赋值
    PARAMETER,      // 参数
    RETURN_TYPE,    // 返回类型
    FIELD_ACCESS,   // 字段访问
    METHOD_CALL,    // 方法调用
    OTHER           // 其他
}

/**
 * 搜索结果集合
 */
data class SearchResults(
    val query: String,
    val searchType: SearchType,
    val results: List<SearchResult>,
    val totalCount: Int,
    val searchTime: Long,
    val hasMore: Boolean = false,
    val suggestions: List<String> = emptyList(),
    val errors: List<String> = emptyList()
)

/**
 * 搜索进度信息
 */
data class SearchProgress(
    val current: Int,
    val total: Int,
    val message: String,
    val percentage: Int = if (total > 0) (current * 100 / total) else 0
)

/**
 * 搜索进度回调接口
 */
interface SearchProgressCallback {
    fun onProgress(progress: SearchProgress)
    fun onCompleted(results: SearchResults)
    fun onError(error: Throwable)
    fun onCancelled()
}

/**
 * PSI 搜索结果
 */
data class PsiSearchResult(
    override val file: VirtualFile?,
    override val line: Int,
    override val column: Int,
    override val text: String,
    override val context: String,
    override val relevanceScore: Double,
    val psiElement: PsiElement,
    val elementType: String,
    val elementName: String?
) : SearchResult()

/**
 * 搜索提供者接口
 */
interface SearchProvider {
    fun canHandle(searchType: SearchType): Boolean
    suspend fun search(config: SearchConfig, callback: SearchProgressCallback? = null): SearchResults
    fun getSupportedFeatures(): Set<SearchFeature>
}

/**
 * 搜索功能特性
 */
enum class SearchFeature {
    CASE_SENSITIVE,     // 大小写敏感
    WHOLE_WORDS,        // 全词匹配
    REGEX,              // 正则表达式
    FUZZY_MATCH,        // 模糊匹配
    CONTEXT_AWARE,      // 上下文感知
    INCREMENTAL,        // 增量搜索
    ASYNC,              // 异步搜索
    CANCELLABLE,        // 可取消
    PROGRESS_TRACKING,  // 进度跟踪
    RESULT_RANKING      // 结果排序
}
