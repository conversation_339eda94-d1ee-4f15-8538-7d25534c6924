package com.taobao.mc.aimi.execution

import com.github.continuedev.continueintellijextension.protocol.TerminalOptions
import com.intellij.execution.ExecutionManager
import com.intellij.execution.executors.DefaultRunExecutor
import com.intellij.execution.runners.ExecutionEnvironmentBuilder
import com.intellij.openapi.project.Project
import kotlinx.coroutines.delay
import java.io.File
import java.util.concurrent.atomic.AtomicLong

val TerminalId = AtomicLong(0)

suspend fun executeTerminal(
    project: Project,
    command: String,
    options: TerminalOptions = TerminalOptions.DEFAULT,
    toolUserId: String = "AIMI",
    terminalId: Long = TerminalId.andIncrement,
    timeoutMillis: Long = 30000
): String {
    val factory = AIMIConfigurationFactory(AIMIConfigurationType())
    val configuration = factory.createTemplateConfiguration(project)

    // 配置参数
    configuration.command = command
    configuration.workingDirectory = project.basePath ?: project.baseDir.path
    configuration.toolUserId = toolUserId
    configuration.terminalId = terminalId

    // 执行配置
    val executor = DefaultRunExecutor.getRunExecutorInstance()
    val builder = ExecutionEnvironmentBuilder.create(executor, configuration)
    val environment = builder.build()

    ExecutionManager.getInstance(project).restartRunProfile(environment)

    // 等待一小段时间，确保进程已启动
    delay(1000) // 等待1秒

    // 获取终端输出
    return if (options.waitForCompletion) {
        val result = AIMITerminalUtil.getTerminalOutputWithStatus(project, terminalId, timeoutMillis)
        result.output
    } else {
        AIMITerminalUtil.getCurrentTerminalOutput(project, terminalId) ?: ""
    }
}

/**
 * 检查项目根目录下是否存在指定路径（文件或目录）
 * @param project 当前项目
 * @param pathName 要检查的路径名
 * @return 如果路径存在返回 true，否则返回 false
 */
fun pathExistsInProjectRoot(project: Project, pathName: String): Boolean {
    // 获取项目根路径
    val projectBasePath = project.basePath ?: project.baseDir?.path
    if (projectBasePath == null) {
        return false
    }

    // 构建完整路径
    val path = File(projectBasePath, pathName)

    // 检查路径是否存在
    return path.exists()
}