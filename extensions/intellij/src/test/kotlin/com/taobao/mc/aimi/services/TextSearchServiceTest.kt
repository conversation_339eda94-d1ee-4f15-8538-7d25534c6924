package com.taobao.mc.aimi.services

import com.intellij.openapi.project.Project
import com.intellij.testFramework.fixtures.LightJavaCodeInsightFixtureTestCase
import com.taobao.mc.aimi.types.*
import kotlinx.coroutines.runBlocking
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * TextSearchService 测试类
 */
class TextSearchServiceTest : LightJavaCodeInsightFixtureTestCase() {
    
    private lateinit var searchService: TextSearchService
    
    override fun setUp() {
        super.setUp()
        searchService = TextSearchService.getInstance(project)
    }
    
    @Test
    fun testServiceInitialization() {
        assertTrue(searchService.isAvailable(), "搜索服务应该可用")
        
        val supportedTypes = searchService.getSupportedSearchTypes()
        assertTrue(supportedTypes.contains(SearchType.TEXT), "应该支持文本搜索")
        assertTrue(supportedTypes.contains(SearchType.USAGE), "应该支持 Usage 搜索")
        assertTrue(supportedTypes.contains(SearchType.INDEX), "应该支持索引搜索")
        assertTrue(supportedTypes.contains(SearchType.PSI), "应该支持 PSI 搜索")
    }
    
    @Test
    fun testBasicTextSearch() {
        // 创建测试文件
        myFixture.configureByText("TestClass.java", """
            public class TestClass {
                private String testField;
                
                public void testMethod() {
                    System.out.println("test");
                }
            }
        """.trimIndent())
        
        val config = SearchConfig(
            query = "test",
            searchType = SearchType.TEXT,
            caseSensitive = false,
            maxResults = 10
        )
        
        val results = searchService.search(config)
        
        assertNotNull(results, "搜索结果不应为空")
        assertTrue(results.results.isNotEmpty(), "应该找到搜索结果")
        assertEquals(SearchType.TEXT, results.searchType, "搜索类型应该匹配")
        assertTrue(results.searchTime > 0, "搜索时间应该大于0")
    }
    
    @Test
    fun testSearchConfig() {
        val config = SearchConfig(
            query = "example",
            searchType = SearchType.PSI,
            caseSensitive = true,
            wholeWords = false,
            useRegex = false,
            searchScope = SearchScope.PROJECT,
            fileTypes = setOf("java", "kt"),
            maxResults = 50
        )
        
        assertEquals("example", config.query)
        assertEquals(SearchType.PSI, config.searchType)
        assertTrue(config.caseSensitive)
        assertEquals(SearchScope.PROJECT, config.searchScope)
        assertEquals(setOf("java", "kt"), config.fileTypes)
        assertEquals(50, config.maxResults)
    }
    
    @Test
    fun testSearchHistory() {
        val config1 = SearchConfig(
            query = "test1",
            searchType = SearchType.TEXT,
            maxResults = 10
        )
        
        val config2 = SearchConfig(
            query = "test2",
            searchType = SearchType.USAGE,
            maxResults = 10
        )
        
        // 执行搜索以添加到历史
        try {
            searchService.search(config1)
            searchService.search(config2)
        } catch (e: Exception) {
            // 忽略搜索错误，我们只关心历史记录
        }
        
        val history = searchService.getSearchHistory()
        assertTrue(history.isNotEmpty(), "搜索历史不应为空")
        
        // 清空历史
        searchService.clearSearchHistory()
        val emptyHistory = searchService.getSearchHistory()
        assertTrue(emptyHistory.isEmpty(), "清空后历史应该为空")
    }
    
    @Test
    fun testAsyncSearch() = runBlocking {
        val config = SearchConfig(
            query = "async",
            searchType = SearchType.TEXT,
            maxResults = 5
        )
        
        var callbackCalled = false
        val callback = object : SearchProgressCallback {
            override fun onProgress(current: Int, total: Int, message: String) {
                // 进度回调
            }
            
            override fun onCompleted(results: SearchResults) {
                callbackCalled = true
                assertNotNull(results)
            }
            
            override fun onError(error: Throwable) {
                // 错误处理
            }
        }
        
        val job = searchService.searchAsync(config, callback)
        job.join() // 等待完成
        
        assertTrue(callbackCalled, "回调应该被调用")
    }
    
    @Test
    fun testCombinedSearch() {
        // 创建测试文件
        myFixture.configureByText("CombinedTest.java", """
            public class CombinedTest {
                public void combinedMethod() {
                    String combined = "combined";
                }
            }
        """.trimIndent())
        
        val results = searchService.combinedSearch(
            query = "combined",
            searchTypes = setOf(SearchType.TEXT, SearchType.PSI),
            maxResults = 20
        )
        
        assertNotNull(results, "组合搜索结果不应为空")
        assertTrue(results.searchTime > 0, "搜索时间应该大于0")
    }
    
    @Test
    fun testSearchResultTypes() {
        // 测试不同类型的搜索结果
        val textResult = TextSearchResult(
            file = null,
            line = 1,
            column = 1,
            text = "test",
            context = "test context",
            startOffset = 0,
            endOffset = 4,
            matchedText = "test"
        )
        
        assertEquals("test", textResult.text)
        assertEquals(1, textResult.line)
        assertEquals("test context", textResult.context)
        assertEquals("test", textResult.matchedText)
        
        val indexResult = IndexSearchResult(
            file = null,
            line = 1,
            column = 1,
            text = "index",
            context = "index context",
            indexKey = "key",
            indexValue = "value",
            indexType = "type"
        )
        
        assertEquals("index", indexResult.text)
        assertEquals("key", indexResult.indexKey)
        assertEquals("value", indexResult.indexValue)
        assertEquals("type", indexResult.indexType)
    }
    
    @Test
    fun testSearchScope() {
        val scopes = SearchScope.values()
        assertTrue(scopes.contains(SearchScope.PROJECT))
        assertTrue(scopes.contains(SearchScope.MODULE))
        assertTrue(scopes.contains(SearchScope.DIRECTORY))
        assertTrue(scopes.contains(SearchScope.FILE))
        assertTrue(scopes.contains(SearchScope.SELECTION))
    }
    
    @Test
    fun testSearchTypes() {
        val types = SearchType.values()
        assertTrue(types.contains(SearchType.TEXT))
        assertTrue(types.contains(SearchType.USAGE))
        assertTrue(types.contains(SearchType.INDEX))
        assertTrue(types.contains(SearchType.PSI))
    }
    
    override fun tearDown() {
        try {
            searchService.dispose()
        } finally {
            super.tearDown()
        }
    }
}
