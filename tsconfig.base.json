{
  "compilerOptions": {
    "module": "ESNext",
    "target": "ESNext",
    "lib": ["DOM", "ESNext", "DOM.Iterable"],
    "jsx": "react-jsx",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitAny": false,
    "importHelpers": true,
    "strictNullChecks": true,
    "skipLibCheck": true,
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
  },
  "include": ["src"],
  "exclude": ["build", "dist", "esm"]
}
