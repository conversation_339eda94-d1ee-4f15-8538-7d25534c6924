---
title: How to customize
description: How to customize Chat
keywords: [customize, chat]
sidebar_position: 5
---

There are a number of different ways to customize Chat:

- You can add a [`rules` block](../hub/blocks/block-types.md#rules) to your assistant to give the model persistent instructions through the system prompt. See the [rules deep dive](../customize/deep-dives/rules.mdx) for more information.
- You can configure [`@Codebase`](../customize/deep-dives/codebase.mdx)
- You can configure [`@Docs`](../customize/deep-dives/docs.mdx)
- You can [build your own context provider](../customize/tutorials/build-your-own-context-provider.mdx)
- You can create your own [custom code RAG](../customize/tutorials/custom-code-rag.mdx)
