import { IDE } from "../../index";
import { findUriInDirs } from "../../util/uri";
import { ContextRetrievalService } from "../context/ContextRetrievalService";
import { GetLspDefinitionsFunction } from "../types";
import { HelperVars } from "../util/HelperVars";
import { openedFilesLruCache } from "../util/openedFilesLruCache";
import { getDiffsFromCache } from "./gitDiffCache";

import {
  AutocompleteClipboardSnippet,
  AutocompleteCodeSnippet,
  AutocompleteDiffSnippet,
  AutocompleteRepoSnippet,
  AutocompleteSnippetType,
} from "./types";
import { countTokens, pruneStringFromTop } from "../../llm/countTokens";

const IDE_SNIPPETS_ENABLED = false; // ideSnippets is not used, so it's temporarily disabled

const MAX_SNIPPET_TOKENS = 500;

export interface SnippetPayload {
  repoNames: AutocompleteRepoSnippet[];
  rootPathSnippets: AutocompleteCodeSnippet[];
  importDefinitionSnippets: AutocompleteCodeSnippet[];
  ideSnippets: AutocompleteCodeSnippet[];
  recentlyEditedRangeSnippets: AutocompleteCodeSnippet[];
  recentlyVisitedRangesSnippets: AutocompleteCodeSnippet[];
  diffSnippets: AutocompleteDiffSnippet[];
  clipboardSnippets: AutocompleteClipboardSnippet[];
  retrievalCodeSnippets: AutocompleteCodeSnippet[];
  recentlyOpenedFileSnippets: AutocompleteCodeSnippet[];
}

function racePromise<T>(promise: Promise<T[]>): Promise<T[]> {
  const timeoutPromise = new Promise<T[]>((resolve) => {
    setTimeout(() => resolve([]), 600);
  });

  return Promise.race([promise, timeoutPromise]);
  // return promise;
}

// Some IDEs might have special ways of finding snippets (e.g. JetBrains and VS Code have different "LSP-equivalent" systems,
// or they might separately track recently edited ranges)
async function getIdeSnippets(
  helper: HelperVars,
  ide: IDE,
  getDefinitionsFromLsp: GetLspDefinitionsFunction,
): Promise<AutocompleteCodeSnippet[]> {
  const ideSnippets = await getDefinitionsFromLsp(
    helper.input.filepath,
    helper.fullPrefix + helper.fullSuffix,
    helper.fullPrefix.length,
    ide,
    helper.lang,
  );

  if (helper.options.onlyMyCode) {
    const workspaceDirs = await ide.getWorkspaceDirs();

    return ideSnippets.filter((snippet) =>
      workspaceDirs.some(
        (dir) => !!findUriInDirs(snippet.filepath, [dir]).foundInDir,
      ),
    );
  }

  return ideSnippets;
}

function getSnippetsFromRecentlyEditedRanges(
  helper: HelperVars,
): AutocompleteCodeSnippet[] {
  if (!helper.options.useRecentlyEdited) {
    return [];
  }

  return helper.input.recentlyEditedRanges.map((range) => {
    return {
      filepath: range.filepath,
      content: range.lines.join("\n"),
      type: AutocompleteSnippetType.Code,
    };
  });
}

const getClipboardSnippets = async (
  ide: IDE,
  helper: HelperVars,
): Promise<AutocompleteClipboardSnippet[]> => {
  const content = await ide.getClipboardContent();

  return [content].map((item) => {
    return {
      content: pruneStringFromTop(
        helper.modelName,
        MAX_SNIPPET_TOKENS,
        item.text,
      ),
      copiedAt: item.copiedAt,
      type: AutocompleteSnippetType.Clipboard,
    };
  });
};

const getDiffSnippets = async (
  ide: IDE,
  helper: HelperVars,
): Promise<AutocompleteDiffSnippet[]> => {
  const diffs = await getDiffsFromCache(ide);

  let results = diffs.map((item) => {
    return {
      content: item,
      type: AutocompleteSnippetType.Diff,
    } as AutocompleteDiffSnippet;
  });

  // 暂定给diff信息 MAX_SNIPPET_TOKENS(500) 的token数量限制
  let totalCount = 0;
  results = results.filter((snippet) => {
    const tokens = countTokens(snippet.content, helper.modelName);
    if (totalCount + tokens >= MAX_SNIPPET_TOKENS) {
      return false;
    }
    totalCount += tokens;
    return true;
  });
  return results;
};

const getSnippetsFromRecentlyOpenedFiles = async (
  helper: HelperVars,
  ide: IDE,
): Promise<AutocompleteCodeSnippet[]> => {
  if (helper.options.useRecentlyOpened === false) {
    return [];
  }

  try {
    const currentFileUri = `${helper.filepath}`;

    // Get all file URIs excluding the current file
    const fileUrisToRead = [...openedFilesLruCache.entriesDescending()]
      .filter(([fileUri, _]) => fileUri !== currentFileUri)
      .map(([fileUri, _]) => fileUri);

    // Create an array of promises that each read a file with timeout
    const fileReadPromises = fileUrisToRead.map((fileUri) => {
      // Create a promise that resolves to a snippet or null
      const readPromise = new Promise<AutocompleteCodeSnippet | null>(
        (resolve) => {
          ide
            .readFile(fileUri)
            .then((fileContent) => {
              if (!fileContent || fileContent.trim() === "") {
                resolve(null);
                return;
              }

              resolve({
                filepath: fileUri,
                content: fileContent,
                type: AutocompleteSnippetType.Code,
              });
            })
            .catch((e) => {
              console.error(`Failed to read file ${fileUri}:`, e);
              resolve(null);
            });
        },
      );
      // Cut off at 80ms via racing promises
      return Promise.race([
        readPromise,
        new Promise<null>((resolve) => setTimeout(() => resolve(null), 80)),
      ]);
    });

    // Execute all file reads in parallel
    const results = await Promise.all(fileReadPromises);

    // Filter out null results
    return results.filter(Boolean) as AutocompleteCodeSnippet[];
  } catch (e) {
    console.error("Error processing opened files cache:", e);
    return [];
  }
};

export const getAllSnippets = async ({
  helper,
  ide,
  getDefinitionsFromLsp,
  contextRetrievalService,
}: {
  helper: HelperVars;
  ide: IDE;
  getDefinitionsFromLsp: GetLspDefinitionsFunction;
  contextRetrievalService: ContextRetrievalService;
}): Promise<SnippetPayload> => {
  const recentlyEditedRangeSnippets =
    getSnippetsFromRecentlyEditedRanges(helper);

  const ideInfo = await ide.getIdeInfo();

  const repoName = (await ide.getRepoName(helper.filepath)) ?? "";

  const [
    rootPathSnippets,
    importDefinitionSnippets,
    ideSnippets,
    diffSnippets,
    clipboardSnippets,
    retrievalCodeSnippets,
    recentlyOpenedFileSnippets,
  ] = await Promise.all([
    racePromise(contextRetrievalService.getRootPathSnippets(helper)),
    racePromise(
      contextRetrievalService.getSnippetsFromImportDefinitions(helper),
    ),
    IDE_SNIPPETS_ENABLED
      ? racePromise(getIdeSnippets(helper, ide, getDefinitionsFromLsp))
      : [],
    racePromise(getDiffSnippets(ide, helper)), // temporarily disabled, see https://github.com/continuedev/continue/pull/5882,
    racePromise(getClipboardSnippets(ide, helper)),
    racePromise(
      ideInfo.ideType === "jetbrains" && helper.options.enableCrossFileComplete
        ? contextRetrievalService.retrievalCodeSnippets(helper)
        : Promise.resolve([]),
    ),
    racePromise(getSnippetsFromRecentlyOpenedFiles(helper, ide)), // giving this one a little more time to complete
  ]);

  return {
    repoNames: [{ content: repoName, type: AutocompleteSnippetType.RepoName }],
    rootPathSnippets: rootPathSnippets || [],
    importDefinitionSnippets: importDefinitionSnippets || [],
    ideSnippets: ideSnippets || [],
    recentlyEditedRangeSnippets: recentlyEditedRangeSnippets || [],
    diffSnippets: diffSnippets || [],
    clipboardSnippets: clipboardSnippets || [],
    recentlyVisitedRangesSnippets: helper.input.recentlyVisitedRanges || [],
    retrievalCodeSnippets: retrievalCodeSnippets || [],
    recentlyOpenedFileSnippets,
  };
};
