// TypeScript version of R8 Timing utility
// Helper for collecting timing information during execution.
// Usage:
//   const timing = new Timing("MyApp");
//   timing.begin("My task");
//   try { ... } finally { timing.end(); }
// or alternatively:
//   timing.time("My task", () => { ... });
// Finally a report is printed by:
//   timing.report();

export interface ThrowingAction<E extends Error> {
  (): void | Promise<void>;
}

export interface ThrowingSupplier<T, E extends Error> {
  (): T | Promise<T>;
}

interface MemInfo {
  used: number;
}

class Node {
  readonly title: string;
  readonly trackMemory: boolean;
  readonly children: Map<string, Node> = new Map();

  duration: number = 0;
  private startTime: number = -1;
  private startMemory?: Map<string, MemInfo>;
  private endMemory?: Map<string, MemInfo>;

  constructor(title: string, trackMemory: boolean = false) {
    this.title = title;
    this.trackMemory = trackMemory;
    if (trackMemory) {
      this.startMemory = this.computeMemoryInformation();
    }
    this.startTime = performance.now();
  }

  restart(): void {
    console.assert(this.startTime === -1);
    if (this.trackMemory) {
      this.startMemory = this.computeMemoryInformation();
    }
    this.startTime = performance.now();
  }

  end(): void {
    this.duration += performance.now() - this.startTime;
    this.startTime = -1;
    console.assert(this.getDuration() >= 0);
    if (this.trackMemory) {
      this.endMemory = this.computeMemoryInformation();
    }
  }

  getDuration(): number {
    return this.duration;
  }

  toString(): string {
    return `${this.title}: ${Timing.prettyTime(this.getDuration())}`;
  }

  toStringWithPercentage(top: Node): string {
    if (this === top) return this.toString();
    return `(${Timing.prettyPercentage(this.getDuration(), top.getDuration())}) ${this.toString()}`;
  }

  report(depth: number, top: Node): void {
    console.assert(this.getDuration() >= 0);
    if (
      Timing.percentage(this.getDuration(), top.getDuration()) <
      Timing.MINIMUM_REPORT_PERCENTAGE
    ) {
      return;
    }

    this.printPrefix(depth);
    console.log(this.toStringWithPercentage(top));

    if (this.trackMemory) {
      this.printMemory(depth);
    }

    if (this.children.size === 0) {
      return;
    }

    const childNodes = Array.from(this.children.values());
    let childTime = 0;
    for (const childNode of childNodes) {
      childTime += childNode.getDuration();
    }

    if (childTime < this.getDuration()) {
      const unaccounted = this.getDuration() - childTime;
      if (
        Timing.percentage(unaccounted, top.getDuration()) >=
        Timing.MINIMUM_REPORT_PERCENTAGE
      ) {
        this.printPrefix(depth + 1);
        console.log(
          `(${Timing.prettyPercentage(unaccounted, top.getDuration())}) Unaccounted: ${Timing.prettyTime(unaccounted)}`,
        );
      }
    }

    childNodes.forEach((child) => child.report(depth + 1, top));
  }

  protected printPrefix(depth: number): void {
    if (depth > 0) {
      process.stdout.write("  ".repeat(depth));
      process.stdout.write("- ");
    }
  }

  protected printMemory(depth: number): void {
    if (!this.startMemory || !this.endMemory) return;

    for (const [key, startValue] of this.startMemory.entries()) {
      if (key === "Memory") {
        for (let i = 0; i <= depth; i++) {
          process.stdout.write("  ");
        }
        const endValue = this.endMemory.get(key);
        if (endValue) {
          console.log(
            `${key} start: ${Timing.prettySize(startValue.used)}, ` +
              `end: ${Timing.prettySize(endValue.used)}, ` +
              `delta: ${Timing.prettySize(endValue.used - startValue.used)}`,
          );
        }
      }
    }
  }

  private computeMemoryInformation(): Map<string, MemInfo> {
    const info = new Map<string, MemInfo>();

    // In Node.js environment
    if (typeof process !== "undefined" && process.memoryUsage) {
      const memUsage = process.memoryUsage();
      info.set("Memory", { used: memUsage.heapUsed });
    } else {
      // In browser environment, memory info is limited
      info.set("Memory", { used: 0 });
    }

    return info;
  }
}

export class TimingMerger {
  private readonly parent: Node;
  private readonly merged: Node;
  private readonly numberOfThreads: number;
  private taskCount: number = 0;
  private slowest: Node | null = new Node("<zero>", false);

  constructor(title: string, numberOfThreads: number, timing: Timing) {
    this.parent = timing.getStackPeek();
    this.numberOfThreads = numberOfThreads;
    this.merged = new (class extends Node {
      report(depth: number, top: Node): void {
        console.assert(this.getDuration() >= 0);
        this.printPrefix(depth);
        process.stdout.write(this.toString());

        if (numberOfThreads <= 0) {
          console.log(" (unknown thread count)");
        } else {
          const walltime = (this.constructor as any).parent.getDuration();
          const perThreadTime = this.getDuration() / numberOfThreads;
          console.log(
            `, tasks: ${(this.constructor as any).taskCount}, ` +
              `threads: ${numberOfThreads}, ` +
              `utilization: ${Timing.prettyPercentage(perThreadTime, walltime)}`,
          );
        }

        if (this.trackMemory) {
          this.printMemory(depth);
        }

        this.children.forEach((node) => node.report(depth + 1, this));

        const slowest = (this.constructor as any).slowest;
        if (slowest && slowest.duration > 0) {
          this.printPrefix(depth);
          console.log("SLOWEST " + slowest.toStringWithPercentage(this));
          slowest.children.forEach((node: Node) =>
            node.report(depth + 1, this),
          );
        }
      }

      toString(): string {
        return "MERGE " + super.toString();
      }
    })(title, timing.trackMemory);

    // Store references for the inner class
    (this.merged.constructor as any).parent = this.parent;
    (this.merged.constructor as any).taskCount = () => this.taskCount;
    (this.merged.constructor as any).slowest = () => this.slowest;
  }

  disableSlowestReporting(): TimingMerger {
    this.slowest = null;
    return this;
  }

  isEmpty(): boolean {
    return false;
  }

  add(timings: Timing[]): void {
    const trackMemory = this.merged.trackMemory;
    const worklist: Array<{ mergeTarget: Node; mergeSource: Node }> = [];

    for (const timing of timings) {
      if (timing === Timing.empty()) {
        continue;
      }

      console.assert(
        timing.isStackEmpty(),
        "Expected sub-timing to have completed prior to merge",
      );
      ++this.taskCount;
      this.merged.duration += timing.getTopDuration();

      if (
        this.slowest &&
        timing.getTopDuration() > this.slowest.getDuration()
      ) {
        this.slowest = timing.getTop();
      }

      worklist.push({ mergeTarget: this.merged, mergeSource: timing.getTop() });
    }

    while (worklist.length > 0) {
      const item = worklist.shift()!;
      item.mergeSource.children.forEach((child, title) => {
        let mergeTarget = item.mergeTarget.children.get(title);
        if (!mergeTarget) {
          mergeTarget = new Node(title, trackMemory);
          item.mergeTarget.children.set(title, mergeTarget);
        }
        mergeTarget.duration += child.getDuration();

        if (child.children.size > 0) {
          worklist.push({ mergeTarget, mergeSource: child });
        }
      });
    }
  }

  end(): void {
    console.assert(Timing.verifyUnambiguous(this.parent, this.merged.title));
    this.merged.end();
    this.parent.children.set(this.merged.title, this.merged);
  }
}

export class Timing {
  static readonly MINIMUM_REPORT_PERCENTAGE: number = 0;

  private static readonly EMPTY = new (class extends Timing {
    constructor() {
      super("<empty>", false);
    }

    beginMerger(title: string, numberOfThreads: number): TimingMerger {
      return new (class extends TimingMerger {
        constructor() {
          super(title, numberOfThreads, new Timing("<empty>"));
        }

        add(timings: Timing[]): void {
          // Ignore
        }

        end(): void {
          // Ignore
        }

        isEmpty(): boolean {
          return true;
        }
      })();
    }

    begin(title: string): void {
      // Ignore
    }

    end(): void {
      // Ignore
    }

    report(): void {
      // Ignore
    }
  })();

  static empty(): Timing {
    return Timing.EMPTY;
  }

  private readonly top: Node;
  private readonly stack: Node[];
  readonly trackMemory: boolean;

  constructor(title: string, trackMemory: boolean = false) {
    this.trackMemory = trackMemory;
    this.stack = [];
    this.top = new Node(title, trackMemory);
    this.stack.push(this.top);
  }

  static percentage(part: number, total: number): number {
    return Math.floor((part * 100) / total);
  }

  static prettyPercentage(part: number, total: number): string {
    return Timing.percentage(part, total) + "%";
  }

  static prettyTime(value: number): string {
    const millisTime = Math.floor(value);
    const secondTime = Math.floor(millisTime / 1000);

    if (secondTime >= 60) {
      const minutes = Math.floor(secondTime / 60);
      const seconds = secondTime % 60;
      const millis = millisTime % 1000;
      return `${minutes}m${seconds}s${millis}ms`;
    }

    if (secondTime === 0) {
      return `${millisTime}ms`;
    }

    return `${secondTime}s${millisTime % 1000}ms`;
  }

  static prettySize(value: number): string {
    return Timing.prettyNumber(Math.floor(value / 1024)) + "k";
  }

  static prettyNumber(value: number): string {
    const printed = Math.abs(value).toString();
    if (printed.length < 4) {
      return value.toString();
    }

    let result = "";
    if (value < 0) {
      result += "-";
    }

    const prefix = printed.length % 3;
    result += printed.substring(0, prefix);

    for (let i = prefix; i < printed.length; i += 3) {
      if (i > 0) {
        result += ".";
      }
      result += printed.substring(i, i + 3);
    }

    return result;
  }

  static verifyUnambiguous(node: Node, title: string): boolean {
    console.assert(
      !node.children.has(title),
      "Ambiguous timing chain. Insert a begin/end to fix",
    );
    return true;
  }

  beginMerger(title: string, numberOfThreads: number): TimingMerger {
    console.assert(this.stack.length > 0);
    console.assert(
      Timing.verifyUnambiguous(this.stack[this.stack.length - 1], title),
    );
    return new TimingMerger(title, numberOfThreads, this);
  }

  begin(title: string): void {
    const parent = this.stack[this.stack.length - 1];
    let child = parent.children.get(title);

    if (child) {
      child.restart();
    } else {
      child = new Node(title, this.trackMemory);
      parent.children.set(title, child);
    }

    this.stack.push(child);
  }

  async time<E extends Error>(
    title: string,
    action: ThrowingAction<E>,
  ): Promise<void> {
    this.begin(title);
    try {
      await action();
    } finally {
      this.end();
    }
  }

  async timeWithResult<T, E extends Error>(
    title: string,
    supplier: ThrowingSupplier<T, E>,
  ): Promise<T> {
    this.begin(title);
    try {
      return await supplier();
    } finally {
      this.end();
    }
  }

  end(): void {
    const node = this.stack.pop();
    node?.end();
  }

  report(): void {
    console.assert(
      this.stack.length === 1,
      `Unexpected non-singleton stack: ${this.stack.length}`,
    );
    const top = this.stack[this.stack.length - 1];
    console.assert(top === this.top);
    top.end();
    console.log("Recorded timings:");
    top.report(0, top);
  }

  // Helper methods for TimingMerger
  getStackPeek(): Node {
    return this.stack[this.stack.length - 1];
  }

  isStackEmpty(): boolean {
    return this.stack.length === 0;
  }

  getTop(): Node {
    return this.top;
  }

  getTopDuration(): number {
    return this.top.getDuration();
  }
}
