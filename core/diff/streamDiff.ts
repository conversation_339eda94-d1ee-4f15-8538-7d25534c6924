import { DiffLine, DiffLineType } from "../index.js";

import { LineStream, matchLine } from "./util.js";

// 向前查看最多50行
const MaxLookahead = 1000;

/**
 * https://blog.jcoglan.com/2017/02/12/the-myers-diff-algorithm-part-1/
 * Invariants:
 * - new + same = newLines.length
 * - old + same = oldLinesCopy.length
 * ^ (above two guarantee that all lines get represented)
 * - Lines are always output in order, at least among old and new separately
 * - Old lines in a hunk are always output before the new lines
 */
export async function* streamDiff(
  oldLines: string[],
  newLines: LineStream,
): AsyncGenerator<DiffLine> {
  const oldLinesCopy = [...oldLines];
  const maxLookahead = Math.max(MaxLookahead, oldLines.length * 2);

  // If one indentation mistake is made, others are likely. So we are more permissive about matching
  let seenIndentationMistake = false;

  // 缓存新行，用于向前查看
  const newLinesBuffer: string[] = [];
  let bufferIndex = 0;

  // 获取下一行，可能从缓存或迭代器中获取
  async function getNextLine(): Promise<IteratorResult<string, any>> {
    if (bufferIndex < newLinesBuffer.length) {
      return { value: newLinesBuffer[bufferIndex++], done: false };
    }
    const result = await newLines.next();
    if (!result.done) {
      newLinesBuffer.push(result.value);
      bufferIndex++;
    }
    return result;
  }

  let newLineResult = await getNextLine();

  while (oldLinesCopy.length > 0 && !newLineResult.done) {
    const { matchIndex, isPerfectMatch, newLine } = matchLine(
      newLineResult.value,
      oldLinesCopy,
      seenIndentationMistake,
    );

    if (!seenIndentationMistake && newLineResult.value !== newLine) {
      seenIndentationMistake = true;
    }

    let type: DiffLineType;

    const isNewLine = matchIndex === -1;

    if (isNewLine) {
      // 当前行没有匹配，尝试向前查看以找到可能的匹配
      let foundMatch = false;
      let matchedLineIndex = -1;
      let matchedNewLineIndex = -1;

      const lookaheadBuffer: string[] = [];

      // 保存当前状态
      const currentBufferIndex = bufferIndex;

      // 尝试向前查看以找到匹配
      for (let i = 0; i < maxLookahead; i++) {
        // 获取下一行
        const lookaheadResult = await getNextLine();
        if (lookaheadResult.done) break;

        lookaheadBuffer.push(lookaheadResult.value);

        // 尝试匹配这一行
        for (let j = 0; j < Math.min(10, oldLinesCopy.length); j++) {
          const matchResult = matchLine(
            lookaheadResult.value,
            oldLinesCopy.slice(j),
            seenIndentationMistake,
          );

          if (matchResult.matchIndex !== -1 && matchResult.isPerfectMatch) {
            // 找到匹配
            foundMatch = true;
            matchedLineIndex = j + matchResult.matchIndex;
            matchedNewLineIndex = i;
            break;
          }
        }

        if (foundMatch) break;
      }

      // 重置缓存索引
      bufferIndex = currentBufferIndex;

      if (foundMatch && matchedLineIndex >= 0 && matchedNewLineIndex >= 0) {
        // 找到了匹配，处理中间的所有行为删除
        for (let i = 0; i < matchedLineIndex; i++) {
          yield { type: "old", line: oldLinesCopy.shift()! };
        }

        // 输出当前行为新增行
        yield { type: "new", line: newLine };

        // 输出匹配前的所有新行
        for (let i = 0; i < matchedNewLineIndex; i++) {
          bufferIndex++; // 跳过已处理的行
          yield { type: "new", line: lookaheadBuffer[i] };
        }

        // 输出匹配行
        yield { type: "same", line: oldLinesCopy.shift()! };

        // 更新当前行
        bufferIndex++; // 跳过匹配行
        newLineResult = await getNextLine();
        continue;
      } else {
        // 没有找到匹配，将当前行标记为新增
        type = "new";
      }
    } else {
      // Insert all deleted lines before match
      for (let i = 0; i < matchIndex; i++) {
        yield { type: "old", line: oldLinesCopy.shift()! };
      }
      type = isPerfectMatch ? "same" : "old";
    }

    switch (type) {
      case "new":
        yield { type, line: newLine };
        break;

      case "same":
        yield { type, line: oldLinesCopy.shift()! };
        break;

      case "old":
        yield { type, line: oldLinesCopy.shift()! };
        yield { type: "new", line: newLine };
        break;

      default:
        console.error(`Error streaming diff, unrecognized diff type: ${type}`);
    }
    newLineResult = await newLines.next();
  }

  // Once at the edge, only one choice
  if (newLineResult.done && oldLinesCopy.length > 0) {
    for (const oldLine of oldLinesCopy) {
      yield { type: "old", line: oldLine };
    }
  }

  if (!newLineResult.done && oldLinesCopy.length === 0) {
    yield { type: "new", line: newLineResult.value };
    for await (const newLine of newLines) {
      yield { type: "new", line: newLine };
    }
  }
}
