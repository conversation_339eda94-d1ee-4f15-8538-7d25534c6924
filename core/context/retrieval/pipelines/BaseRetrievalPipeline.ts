// @ts-ignore
import nlp from "wink-nlp-utils";

import { BranchAndDir, Chunk, ContinueConfig, IDE, ILLM } from "../../../";
import { chunkDocument } from "../../../indexing/chunk/chunk";
import { FullTextSearchCodebaseIndex } from "../../../indexing/FullTextSearchCodebaseIndex";
import { LanceDbIndex } from "../../../indexing/LanceDbIndex";
import { findUriInDirs } from "../../../util/uri";
import { deduplicateChunks } from "../util";
import { requestFilesFromRepoMap } from "../repoMapRequest";
import { openedFilesLruCache } from "../../../autocomplete/util/openedFilesLruCache";

const DEFAULT_CHUNK_SIZE = 384;

export interface RetrievalPipelineOptions {
  llm: ILLM;
  config: ContinueConfig;
  ide: IDE;
  input: string;
  nRetrieve: number;
  nFinal: number;
  tags: BranchAndDir[];
  filterDirectory?: string;
}

export interface RetrievalPipelineRunArguments {
  query: string;
  tags: BranchAndDir[];
  filterDirectory?: string;
  includeEmbeddings: boolean;
  disableFiles?: string[];
}

export interface IRetrievalPipeline {
  run(args: RetrievalPipelineRunArguments): Promise<Chunk[]>;
}

export default class BaseRetrievalPipeline implements IRetrievalPipeline {
  private ftsIndex = new FullTextSearchCodebaseIndex();
  private lanceDbIndex: LanceDbIndex | null = null;
  private lanceDbIsInitialized = false;

  constructor(protected readonly options: RetrievalPipelineOptions) {
    // void this.initLanceDb();
  }

  private async initLanceDb() {
    if (this.lanceDbIsInitialized && this.lanceDbIndex) {
      return;
    }
    const embedModel = this.options.config.selectedModelByRole.embed;

    if (!embedModel) {
      return;
    }

    this.lanceDbIndex = await LanceDbIndex.create(embedModel, (uri) =>
      this.options.ide.readFile(uri),
    );
    this.lanceDbIsInitialized = true;
  }

  private getCleanedTrigrams(
    query: RetrievalPipelineRunArguments["query"],
  ): string[] {
    let text = nlp.string.removeExtraSpaces(query);
    text = nlp.string.stem(text);

    let tokens = nlp.string
      .tokenize(text, true)
      .filter((token: any) => token.tag === "word")
      .map((token: any) => token.value);

    tokens = nlp.tokens.removeWords(tokens);
    tokens = nlp.tokens.setOfWords(tokens);

    const cleanedTokens = [...tokens].join(" ");
    const trigrams = nlp.string.ngram(cleanedTokens, 3);

    return trigrams.map(this.escapeFtsQueryString);
  }

  private escapeFtsQueryString(query: string): string {
    const escapedDoubleQuotes = query.replace(/"/g, '""');
    return `"${escapedDoubleQuotes}"`;
  }

  protected async retrieveFts(
    args: RetrievalPipelineRunArguments,
    n: number,
  ): Promise<Chunk[]> {
    await this.initLanceDb();
    if (args.query.trim() === "") {
      return [];
    }

    const tokens = this.getCleanedTrigrams(args.query).join(" OR ");

    return await this.ftsIndex.retrieve({
      n,
      text: tokens,
      tags: args.tags,
      directory: args.filterDirectory,
    });
  }

  protected async retrieveAndChunkRecentlyEditedFiles(
    n: number,
  ): Promise<Chunk[]> {
    await this.initLanceDb();
    const recentlyEditedFilesSlice = Array.from(
      openedFilesLruCache.keys(),
    ).slice(0, n);

    // If the number of recently edited files is less than the retrieval limit,
    // include additional open files. This is useful in the case where a user
    // has many tabs open and reloads their IDE. They now have 0 recently edited files,
    // but many open tabs that represent what they were working on prior to reload.
    if (recentlyEditedFilesSlice.length < n) {
      const openFiles = await this.options.ide.getOpenFiles();
      recentlyEditedFilesSlice.push(
        ...openFiles.slice(0, n - recentlyEditedFilesSlice.length),
      );
    }

    const chunks: Chunk[] = [];

    for (const filepath of recentlyEditedFilesSlice) {
      const contents = await this.options.ide.readFile(filepath);
      const fileChunks = chunkDocument({
        filepath,
        contents,
        maxChunkSize:
          this.options.config.selectedModelByRole.embed
            ?.maxEmbeddingChunkSize ?? DEFAULT_CHUNK_SIZE,
        digest: filepath,
      });

      for await (const chunk of fileChunks) {
        chunks.push(chunk);
      }
    }

    return chunks.slice(0, n);
  }

  protected async retrieveEmbeddings(
    input: string,
    n: number,
  ): Promise<Chunk[]> {
    await this.initLanceDb();
    if (!this.lanceDbIndex) {
      console.warn(
        "LanceDB index not available, skipping embeddings retrieval",
      );
      return [];
    }

    return this.lanceDbIndex.retrieve(
      input,
      n,
      this.options.tags,
      this.options.filterDirectory,
    );
  }

  /**
   * 执行初始检索，包括 FTS、embeddings、recently edited files 和 repo map chunks
   * 子类可以覆盖此方法以自定义检索逻辑
   */
  protected async performInitialRetrieval(
    args: RetrievalPipelineRunArguments,
    ftsNFinal: number,
    embeddingsNFinal: number = ftsNFinal,
    recentlyEditedNFinal: number = ftsNFinal,
  ): Promise<Chunk[]> {
    const { input, filterDirectory, config } = this.options;
    let retrievalResults: Chunk[] = [];

    // FTS chunks
    let ftsChunks: Chunk[] = [];
    try {
      ftsChunks = await this.retrieveFts(args, ftsNFinal);
    } catch (error) {
      console.error("Error retrieving FTS chunks:", error);
    }

    // Embeddings chunks
    let embeddingsChunks: Chunk[] = [];
    try {
      embeddingsChunks = !!config.selectedModelByRole.embed
        ? await this.retrieveEmbeddings(input, embeddingsNFinal)
        : [];
    } catch (error) {
      console.error("Error retrieving embeddings chunks:", error);
    }

    // Recently edited files chunks
    let recentlyEditedFilesChunks: Chunk[] = [];
    try {
      recentlyEditedFilesChunks =
        await this.retrieveAndChunkRecentlyEditedFiles(recentlyEditedNFinal);
    } catch (error) {
      console.error("Error retrieving recently edited files chunks:", error);
    }

    // Repo map chunks
    let repoMapChunks: Chunk[] = [];
    try {
      repoMapChunks = await requestFilesFromRepoMap(
        this.options.llm,
        this.options.config,
        this.options.ide,
        input,
        filterDirectory,
      );
    } catch (error) {
      console.error("Error retrieving repo map chunks:", error);
    }

    retrievalResults.push(
      ...recentlyEditedFilesChunks,
      ...ftsChunks,
      ...embeddingsChunks,
      ...repoMapChunks,
    );

    return this.processResults(
      retrievalResults,
      filterDirectory,
      args.disableFiles,
    );
  }

  run(args: RetrievalPipelineRunArguments): Promise<Chunk[]> {
    throw new Error("Not implemented");
  }

  protected processResults(
    retrievalResults: Chunk[],
    filterDirectory?: string,
    disableFiles?: string[],
  ) {
    if (filterDirectory) {
      // Backup if the individual retrieval methods don't listen
      retrievalResults = retrievalResults.filter(
        (chunk) =>
          !!findUriInDirs(chunk.filepath, [filterDirectory]).foundInDir,
      );
    }

    // 支持过滤文件
    if (disableFiles) {
      retrievalResults = retrievalResults.filter((chunk) => {
        return !this.isDisableFile(chunk.filepath, disableFiles);
      });
    }

    return deduplicateChunks(retrievalResults);
  }

  protected isDisableFile(filePath: string, disableFiles: string[]): boolean {
    return (
      disableFiles.findIndex((fileExtension) =>
        filePath.endsWith(`.${fileExtension}`),
      ) != -1
    );
  }
}
